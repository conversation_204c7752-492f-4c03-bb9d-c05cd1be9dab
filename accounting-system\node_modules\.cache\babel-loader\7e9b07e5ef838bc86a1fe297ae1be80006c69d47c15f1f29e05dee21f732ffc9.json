{"ast": null, "code": "var _jsxFileName = \"F:\\\\\\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\\\\accounting-system\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Navbar from './components/Navbar';\nimport Dashboard from './components/Dashboard';\nimport Customers from './components/Customers';\nimport Suppliers from './components/Suppliers';\nimport Products from './components/Products';\nimport SalesInvoices from './components/SalesInvoices';\nimport PurchaseInvoices from './components/PurchaseInvoices';\nimport Treasury from './components/Treasury';\nimport Expenses from './components/Expenses';\nimport Reports from './components/Reports';\nimport Inventory from './components/Inventory';\nimport Backup from './components/Backup';\nimport Settings from './components/Settings';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"app\",\n      dir: \"rtl\",\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-content\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/customers\",\n            element: /*#__PURE__*/_jsxDEV(Customers, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/suppliers\",\n            element: /*#__PURE__*/_jsxDEV(Suppliers, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/products\",\n            element: /*#__PURE__*/_jsxDEV(Products, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/sales-invoices\",\n            element: /*#__PURE__*/_jsxDEV(SalesInvoices, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/purchase-invoices\",\n            element: /*#__PURE__*/_jsxDEV(PurchaseInvoices, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 55\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/treasury\",\n            element: /*#__PURE__*/_jsxDEV(Treasury, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/expenses\",\n            element: /*#__PURE__*/_jsxDEV(Expenses, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/inventory\",\n            element: /*#__PURE__*/_jsxDEV(Inventory, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/backup\",\n            element: /*#__PURE__*/_jsxDEV(Backup, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/reports\",\n            element: /*#__PURE__*/_jsxDEV(Reports, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "<PERSON><PERSON><PERSON>", "Dashboard", "Customers", "Suppliers", "Products", "SalesInvoices", "PurchaseInvoices", "Treasury", "Expenses", "Reports", "Inventory", "Backup", "Settings", "jsxDEV", "_jsxDEV", "App", "children", "className", "dir", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["F:/القيمة المضافة/accounting-system/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Navbar from './components/Navbar';\nimport Dashboard from './components/Dashboard';\nimport Customers from './components/Customers';\nimport Suppliers from './components/Suppliers';\nimport Products from './components/Products';\nimport SalesInvoices from './components/SalesInvoices';\nimport PurchaseInvoices from './components/PurchaseInvoices';\nimport Treasury from './components/Treasury';\nimport Expenses from './components/Expenses';\nimport Reports from './components/Reports';\nimport Inventory from './components/Inventory';\nimport Backup from './components/Backup';\nimport Settings from './components/Settings';\nimport './App.css';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"app\" dir=\"rtl\">\n        <Navbar />\n        <div className=\"main-content\">\n          <Routes>\n            <Route path=\"/\" element={<Dashboard />} />\n            <Route path=\"/customers\" element={<Customers />} />\n            <Route path=\"/suppliers\" element={<Suppliers />} />\n            <Route path=\"/products\" element={<Products />} />\n            <Route path=\"/sales-invoices\" element={<SalesInvoices />} />\n            <Route path=\"/purchase-invoices\" element={<PurchaseInvoices />} />\n            <Route path=\"/treasury\" element={<Treasury />} />\n            <Route path=\"/expenses\" element={<Expenses />} />\n            <Route path=\"/inventory\" element={<Inventory />} />\n            <Route path=\"/backup\" element={<Backup />} />\n            <Route path=\"/reports\" element={<Reports />} />\n          </Routes>\n        </div>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACjB,MAAM;IAAAmB,QAAA,eACLF,OAAA;MAAKG,SAAS,EAAC,KAAK;MAACC,GAAG,EAAC,KAAK;MAAAF,QAAA,gBAC5BF,OAAA,CAACd,MAAM;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVR,OAAA;QAAKG,SAAS,EAAC,cAAc;QAAAD,QAAA,eAC3BF,OAAA,CAAChB,MAAM;UAAAkB,QAAA,gBACLF,OAAA,CAACf,KAAK;YAACwB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEV,OAAA,CAACb,SAAS;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CR,OAAA,CAACf,KAAK;YAACwB,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEV,OAAA,CAACZ,SAAS;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDR,OAAA,CAACf,KAAK;YAACwB,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEV,OAAA,CAACX,SAAS;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDR,OAAA,CAACf,KAAK;YAACwB,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEV,OAAA,CAACV,QAAQ;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDR,OAAA,CAACf,KAAK;YAACwB,IAAI,EAAC,iBAAiB;YAACC,OAAO,eAAEV,OAAA,CAACT,aAAa;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DR,OAAA,CAACf,KAAK;YAACwB,IAAI,EAAC,oBAAoB;YAACC,OAAO,eAAEV,OAAA,CAACR,gBAAgB;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClER,OAAA,CAACf,KAAK;YAACwB,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEV,OAAA,CAACP,QAAQ;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDR,OAAA,CAACf,KAAK;YAACwB,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEV,OAAA,CAACN,QAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDR,OAAA,CAACf,KAAK;YAACwB,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEV,OAAA,CAACJ,SAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDR,OAAA,CAACf,KAAK;YAACwB,IAAI,EAAC,SAAS;YAACC,OAAO,eAAEV,OAAA,CAACH,MAAM;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CR,OAAA,CAACf,KAAK;YAACwB,IAAI,EAAC,UAAU;YAACC,OAAO,eAAEV,OAAA,CAACL,OAAO;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACG,EAAA,GAvBQV,GAAG;AAyBZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}