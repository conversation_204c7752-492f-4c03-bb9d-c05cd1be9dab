import React, { useState, useEffect } from 'react';
import axios from 'axios';

const Treasury = () => {
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [balance, setBalance] = useState(0);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    fetchTransactions();
  }, []);

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:5000/api/treasury-transactions');
      setTransactions(response.data);
      
      // حساب الرصيد
      const totalIncome = response.data
        .filter(t => t.transaction_type === 'income')
        .reduce((sum, t) => sum + t.amount, 0);
      
      const totalExpense = response.data
        .filter(t => t.transaction_type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0);
      
      setBalance(totalIncome - totalExpense);
    } catch (error) {
      setError('خطأ في جلب بيانات الخزينة');
      console.error('Error fetching treasury transactions:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">جاري تحميل بيانات الخزينة...</div>
      </div>
    );
  }

  return (
    <div className="container">
      <h1 className="page-title">إدارة الخزينة</h1>
      
      {message && <div className="success">{message}</div>}
      {error && <div className="error">{error}</div>}
      
      <div className="row">
        <div className="col-md-4">
          <div className="stat-card">
            <div className="stat-number">{balance.toLocaleString()}</div>
            <div className="stat-label">رصيد الخزينة الحالي (ج.م)</div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="stat-card" style={{ background: 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)' }}>
            <div className="stat-number">
              {transactions
                .filter(t => t.transaction_type === 'income')
                .reduce((sum, t) => sum + t.amount, 0)
                .toLocaleString()}
            </div>
            <div className="stat-label">إجمالي الإيرادات (ج.م)</div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="stat-card" style={{ background: 'linear-gradient(135deg, #f44336 0%, #da190b 100%)' }}>
            <div className="stat-number">
              {transactions
                .filter(t => t.transaction_type === 'expense')
                .reduce((sum, t) => sum + t.amount, 0)
                .toLocaleString()}
            </div>
            <div className="stat-label">إجمالي المصروفات (ج.م)</div>
          </div>
        </div>
      </div>
      
      <div className="card">
        <div className="card-header">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <h3 className="card-title">حركات الخزينة</h3>
            <button className="btn btn-primary">
              إضافة حركة جديدة
            </button>
          </div>
        </div>
        
        <div className="card-body">
          {transactions.length === 0 ? (
            <p>لا توجد حركات في الخزينة حتى الآن</p>
          ) : (
            <table className="table">
              <thead>
                <tr>
                  <th>التاريخ</th>
                  <th>النوع</th>
                  <th>الوصف</th>
                  <th>الفئة</th>
                  <th>المبلغ</th>
                  <th>المرجع</th>
                </tr>
              </thead>
              <tbody>
                {transactions.map(transaction => (
                  <tr key={transaction.id}>
                    <td>{new Date(transaction.transaction_date).toLocaleDateString('ar-EG')}</td>
                    <td>
                      <span className={`badge ${transaction.transaction_type === 'income' ? 'badge-success' : 'badge-danger'}`}>
                        {transaction.transaction_type === 'income' ? 'إيراد' : 'مصروف'}
                      </span>
                    </td>
                    <td>{transaction.description}</td>
                    <td>{transaction.category || '-'}</td>
                    <td style={{ color: transaction.transaction_type === 'income' ? 'green' : 'red' }}>
                      {transaction.transaction_type === 'income' ? '+' : '-'}{transaction.amount.toLocaleString()} ج.م
                    </td>
                    <td>{transaction.reference_type || '-'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
};

export default Treasury;
