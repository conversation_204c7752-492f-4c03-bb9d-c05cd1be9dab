import React, { useState, useEffect } from 'react';
import axios from 'axios';
import ImportExcel from './ImportExcel';

const Products = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    unit: '',
    price: '',
    cost: '',
    stock_quantity: ''
  });
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [showImport, setShowImport] = useState(false);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:5000/api/products');
      setProducts(response.data);
    } catch (error) {
      setError('خطأ في جلب بيانات المنتجات');
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const productData = {
        ...formData,
        price: parseFloat(formData.price) || 0,
        cost: parseFloat(formData.cost) || 0,
        stock_quantity: parseInt(formData.stock_quantity) || 0
      };

      if (editingProduct) {
        await axios.put(`http://localhost:5000/api/products/${editingProduct.id}`, productData);
        setMessage('تم تحديث المنتج بنجاح');
      } else {
        await axios.post('http://localhost:5000/api/products', productData);
        setMessage('تم إضافة المنتج بنجاح');
      }
      
      setFormData({
        name: '',
        description: '',
        unit: '',
        price: '',
        cost: '',
        stock_quantity: ''
      });
      setShowForm(false);
      setEditingProduct(null);
      fetchProducts();
      
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      setError('خطأ في حفظ بيانات المنتج');
      console.error('Error saving product:', error);
    }
  };

  const handleEdit = (product) => {
    setEditingProduct(product);
    setFormData({
      name: product.name,
      description: product.description || '',
      unit: product.unit || '',
      price: product.price?.toString() || '',
      cost: product.cost?.toString() || '',
      stock_quantity: product.stock_quantity?.toString() || ''
    });
    setShowForm(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      unit: '',
      price: '',
      cost: '',
      stock_quantity: ''
    });
    setShowForm(false);
    setEditingProduct(null);
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">جاري تحميل بيانات المنتجات...</div>
      </div>
    );
  }

  return (
    <div className="container">
      <h1 className="page-title">إدارة المنتجات</h1>
      
      {message && <div className="success">{message}</div>}
      {error && <div className="error">{error}</div>}
      
      <div className="card">
        <div className="card-header">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <h3 className="card-title">قائمة المنتجات</h3>
            <div style={{ display: 'flex', gap: '1rem' }}>
              <button
                className="btn btn-primary"
                onClick={() => setShowForm(!showForm)}
              >
                {showForm ? 'إلغاء' : 'إضافة منتج جديد'}
              </button>
              <button
                className="btn btn-success"
                onClick={() => setShowImport(true)}
              >
                📥 استيراد من Excel
              </button>
            </div>
          </div>
        </div>
        
        {showForm && (
          <div className="card-body">
            <form onSubmit={handleSubmit}>
              <div className="row">
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">اسم المنتج *</label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="form-control"
                      required
                    />
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">الوحدة</label>
                    <input
                      type="text"
                      name="unit"
                      value={formData.unit}
                      onChange={handleInputChange}
                      className="form-control"
                      placeholder="قطعة، كيلو، متر..."
                    />
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="form-group">
                    <label className="form-label">سعر البيع</label>
                    <input
                      type="number"
                      step="0.01"
                      name="price"
                      value={formData.price}
                      onChange={handleInputChange}
                      className="form-control"
                    />
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="form-group">
                    <label className="form-label">سعر التكلفة</label>
                    <input
                      type="number"
                      step="0.01"
                      name="cost"
                      value={formData.cost}
                      onChange={handleInputChange}
                      className="form-control"
                    />
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="form-group">
                    <label className="form-label">الكمية المتاحة</label>
                    <input
                      type="number"
                      name="stock_quantity"
                      value={formData.stock_quantity}
                      onChange={handleInputChange}
                      className="form-control"
                    />
                  </div>
                </div>
                <div className="col">
                  <div className="form-group">
                    <label className="form-label">الوصف</label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      className="form-control"
                      rows="3"
                    ></textarea>
                  </div>
                </div>
              </div>
              <div style={{ display: 'flex', gap: '1rem' }}>
                <button type="submit" className="btn btn-success">
                  {editingProduct ? 'تحديث المنتج' : 'إضافة المنتج'}
                </button>
                <button type="button" className="btn btn-danger" onClick={resetForm}>
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        )}
        
        <div className="card-body">
          {products.length === 0 ? (
            <p>لا توجد منتجات مسجلة حتى الآن</p>
          ) : (
            <table className="table">
              <thead>
                <tr>
                  <th>اسم المنتج</th>
                  <th>الوحدة</th>
                  <th>سعر البيع</th>
                  <th>سعر التكلفة</th>
                  <th>الكمية المتاحة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {products.map(product => (
                  <tr key={product.id}>
                    <td>{product.name}</td>
                    <td>{product.unit || '-'}</td>
                    <td>{product.price?.toLocaleString() || '0'} ج.م</td>
                    <td>{product.cost?.toLocaleString() || '0'} ج.م</td>
                    <td>{product.stock_quantity || '0'}</td>
                    <td>
                      <button 
                        className="btn btn-warning"
                        onClick={() => handleEdit(product)}
                        style={{ marginLeft: '0.5rem' }}
                      >
                        تعديل
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>

      {/* نموذج استيراد Excel */}
      {showImport && (
        <ImportExcel
          type="products"
          onClose={() => setShowImport(false)}
          onImportComplete={() => {
            setShowImport(false);
            fetchProducts();
            setMessage('تم استيراد المنتجات بنجاح');
            setTimeout(() => setMessage(''), 3000);
          }}
        />
      )}
    </div>
  );
};

export default Products;
