{"ast": null, "code": "var _jsxFileName = \"F:\\\\\\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\\\\accounting-system\\\\src\\\\components\\\\Suppliers.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport ImportExcel from './ImportExcel';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Suppliers = () => {\n  _s();\n  const [suppliers, setSuppliers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showForm, setShowForm] = useState(false);\n  const [editingSupplier, setEditingSupplier] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    phone: '',\n    email: '',\n    address: '',\n    tax_number: ''\n  });\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n  const [showImport, setShowImport] = useState(false);\n  useEffect(() => {\n    fetchSuppliers();\n  }, []);\n  const fetchSuppliers = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/suppliers');\n      setSuppliers(response.data);\n    } catch (error) {\n      setError('خطأ في جلب بيانات الموردين');\n      console.error('Error fetching suppliers:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      if (editingSupplier) {\n        await axios.put(`http://localhost:5000/api/suppliers/${editingSupplier.id}`, formData);\n        setMessage('تم تحديث المورد بنجاح');\n      } else {\n        await axios.post('http://localhost:5000/api/suppliers', formData);\n        setMessage('تم إضافة المورد بنجاح');\n      }\n      setFormData({\n        name: '',\n        phone: '',\n        email: '',\n        address: '',\n        tax_number: ''\n      });\n      setShowForm(false);\n      setEditingSupplier(null);\n      fetchSuppliers();\n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      setError('خطأ في حفظ بيانات المورد');\n      console.error('Error saving supplier:', error);\n    }\n  };\n  const handleEdit = supplier => {\n    setEditingSupplier(supplier);\n    setFormData({\n      name: supplier.name,\n      phone: supplier.phone || '',\n      email: supplier.email || '',\n      address: supplier.address || '',\n      tax_number: supplier.tax_number || ''\n    });\n    setShowForm(true);\n  };\n  const handleDelete = async id => {\n    if (window.confirm('هل أنت متأكد من حذف هذا المورد؟')) {\n      try {\n        await axios.delete(`http://localhost:5000/api/suppliers/${id}`);\n        setMessage('تم حذف المورد بنجاح');\n        fetchSuppliers();\n        setTimeout(() => setMessage(''), 3000);\n      } catch (error) {\n        setError('خطأ في حذف المورد');\n        console.error('Error deleting supplier:', error);\n      }\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      phone: '',\n      email: '',\n      address: '',\n      tax_number: ''\n    });\n    setShowForm(false);\n    setEditingSupplier(null);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"page-title\",\n      children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 19\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"card-title\",\n            children: \"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => setShowForm(!showForm),\n              children: showForm ? 'إلغاء' : 'إضافة مورد جديد'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-success\",\n              onClick: () => setShowImport(true),\n              children: \"\\uD83D\\uDCE5 \\u0627\\u0633\\u062A\\u064A\\u0631\\u0627\\u062F \\u0645\\u0646 Excel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), showForm && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleInputChange,\n                  className: \"form-control\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"phone\",\n                  value: formData.phone,\n                  onChange: handleInputChange,\n                  className: \"form-control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  onChange: handleInputChange,\n                  className: \"form-control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"tax_number\",\n                  value: formData.tax_number,\n                  onChange: handleInputChange,\n                  className: \"form-control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  name: \"address\",\n                  value: formData.address,\n                  onChange: handleInputChange,\n                  className: \"form-control\",\n                  rows: \"3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-success\",\n              children: editingSupplier ? 'تحديث المورد' : 'إضافة المورد'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-danger\",\n              onClick: resetForm,\n              children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: suppliers.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0645\\u0648\\u0631\\u062F\\u064A\\u0646 \\u0645\\u0633\\u062C\\u0644\\u064A\\u0646 \\u062D\\u062A\\u0649 \\u0627\\u0644\\u0622\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0627\\u0633\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0631\\u0635\\u064A\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: suppliers.map(supplier => {\n              var _supplier$balance;\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: supplier.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: supplier.phone || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: supplier.email || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: supplier.tax_number || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [((_supplier$balance = supplier.balance) === null || _supplier$balance === void 0 ? void 0 : _supplier$balance.toLocaleString()) || '0', \" \\u062C.\\u0645\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-warning\",\n                    onClick: () => handleEdit(supplier),\n                    style: {\n                      marginLeft: '0.5rem'\n                    },\n                    children: \"\\u062A\\u0639\\u062F\\u064A\\u0644\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-danger\",\n                    onClick: () => handleDelete(supplier.id),\n                    children: \"\\u062D\\u0630\\u0641\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this)]\n              }, supplier.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n};\n_s(Suppliers, \"WzmxiJiyXzJcJ7OYSbSm5ZElLTA=\");\n_c = Suppliers;\nexport default Suppliers;\nvar _c;\n$RefreshReg$(_c, \"Suppliers\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "ImportExcel", "jsxDEV", "_jsxDEV", "Suppliers", "_s", "suppliers", "setSuppliers", "loading", "setLoading", "showForm", "setShowForm", "editingSupplier", "setEditingSupplier", "formData", "setFormData", "name", "phone", "email", "address", "tax_number", "message", "setMessage", "error", "setError", "showImport", "setShowImport", "fetchSuppliers", "response", "get", "data", "console", "handleInputChange", "e", "target", "value", "handleSubmit", "preventDefault", "put", "id", "post", "setTimeout", "handleEdit", "supplier", "handleDelete", "window", "confirm", "delete", "resetForm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "justifyContent", "alignItems", "gap", "onClick", "onSubmit", "type", "onChange", "required", "rows", "length", "map", "_supplier$balance", "balance", "toLocaleString", "marginLeft", "_c", "$RefreshReg$"], "sources": ["F:/القيمة المضافة/accounting-system/src/components/Suppliers.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport ImportExcel from './ImportExcel';\n\nconst Suppliers = () => {\n  const [suppliers, setSuppliers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showForm, setShowForm] = useState(false);\n  const [editingSupplier, setEditingSupplier] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    phone: '',\n    email: '',\n    address: '',\n    tax_number: ''\n  });\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n  const [showImport, setShowImport] = useState(false);\n\n  useEffect(() => {\n    fetchSuppliers();\n  }, []);\n\n  const fetchSuppliers = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/suppliers');\n      setSuppliers(response.data);\n    } catch (error) {\n      setError('خطأ في جلب بيانات الموردين');\n      console.error('Error fetching suppliers:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      if (editingSupplier) {\n        await axios.put(`http://localhost:5000/api/suppliers/${editingSupplier.id}`, formData);\n        setMessage('تم تحديث المورد بنجاح');\n      } else {\n        await axios.post('http://localhost:5000/api/suppliers', formData);\n        setMessage('تم إضافة المورد بنجاح');\n      }\n      \n      setFormData({\n        name: '',\n        phone: '',\n        email: '',\n        address: '',\n        tax_number: ''\n      });\n      setShowForm(false);\n      setEditingSupplier(null);\n      fetchSuppliers();\n      \n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      setError('خطأ في حفظ بيانات المورد');\n      console.error('Error saving supplier:', error);\n    }\n  };\n\n  const handleEdit = (supplier) => {\n    setEditingSupplier(supplier);\n    setFormData({\n      name: supplier.name,\n      phone: supplier.phone || '',\n      email: supplier.email || '',\n      address: supplier.address || '',\n      tax_number: supplier.tax_number || ''\n    });\n    setShowForm(true);\n  };\n\n  const handleDelete = async (id) => {\n    if (window.confirm('هل أنت متأكد من حذف هذا المورد؟')) {\n      try {\n        await axios.delete(`http://localhost:5000/api/suppliers/${id}`);\n        setMessage('تم حذف المورد بنجاح');\n        fetchSuppliers();\n        setTimeout(() => setMessage(''), 3000);\n      } catch (error) {\n        setError('خطأ في حذف المورد');\n        console.error('Error deleting supplier:', error);\n      }\n    }\n  };\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      phone: '',\n      email: '',\n      address: '',\n      tax_number: ''\n    });\n    setShowForm(false);\n    setEditingSupplier(null);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"container\">\n        <div className=\"loading\">جاري تحميل بيانات الموردين...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container\">\n      <h1 className=\"page-title\">إدارة الموردين</h1>\n      \n      {message && <div className=\"success\">{message}</div>}\n      {error && <div className=\"error\">{error}</div>}\n      \n      <div className=\"card\">\n        <div className=\"card-header\">\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <h3 className=\"card-title\">قائمة الموردين</h3>\n            <div style={{ display: 'flex', gap: '1rem' }}>\n              <button\n                className=\"btn btn-primary\"\n                onClick={() => setShowForm(!showForm)}\n              >\n                {showForm ? 'إلغاء' : 'إضافة مورد جديد'}\n              </button>\n              <button\n                className=\"btn btn-success\"\n                onClick={() => setShowImport(true)}\n              >\n                📥 استيراد من Excel\n              </button>\n            </div>\n          </div>\n        </div>\n        \n        {showForm && (\n          <div className=\"card-body\">\n            <form onSubmit={handleSubmit}>\n              <div className=\"row\">\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">اسم المورد *</label>\n                    <input\n                      type=\"text\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                      required\n                    />\n                  </div>\n                </div>\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">رقم الهاتف</label>\n                    <input\n                      type=\"text\"\n                      name=\"phone\"\n                      value={formData.phone}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                    />\n                  </div>\n                </div>\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">البريد الإلكتروني</label>\n                    <input\n                      type=\"email\"\n                      name=\"email\"\n                      value={formData.email}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                    />\n                  </div>\n                </div>\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">الرقم الضريبي</label>\n                    <input\n                      type=\"text\"\n                      name=\"tax_number\"\n                      value={formData.tax_number}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                    />\n                  </div>\n                </div>\n                <div className=\"col\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">العنوان</label>\n                    <textarea\n                      name=\"address\"\n                      value={formData.address}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                      rows=\"3\"\n                    ></textarea>\n                  </div>\n                </div>\n              </div>\n              <div style={{ display: 'flex', gap: '1rem' }}>\n                <button type=\"submit\" className=\"btn btn-success\">\n                  {editingSupplier ? 'تحديث المورد' : 'إضافة المورد'}\n                </button>\n                <button type=\"button\" className=\"btn btn-danger\" onClick={resetForm}>\n                  إلغاء\n                </button>\n              </div>\n            </form>\n          </div>\n        )}\n        \n        <div className=\"card-body\">\n          {suppliers.length === 0 ? (\n            <p>لا توجد موردين مسجلين حتى الآن</p>\n          ) : (\n            <table className=\"table\">\n              <thead>\n                <tr>\n                  <th>الاسم</th>\n                  <th>الهاتف</th>\n                  <th>البريد الإلكتروني</th>\n                  <th>الرقم الضريبي</th>\n                  <th>الرصيد</th>\n                  <th>الإجراءات</th>\n                </tr>\n              </thead>\n              <tbody>\n                {suppliers.map(supplier => (\n                  <tr key={supplier.id}>\n                    <td>{supplier.name}</td>\n                    <td>{supplier.phone || '-'}</td>\n                    <td>{supplier.email || '-'}</td>\n                    <td>{supplier.tax_number || '-'}</td>\n                    <td>{supplier.balance?.toLocaleString() || '0'} ج.م</td>\n                    <td>\n                      <button \n                        className=\"btn btn-warning\"\n                        onClick={() => handleEdit(supplier)}\n                        style={{ marginLeft: '0.5rem' }}\n                      >\n                        تعديل\n                      </button>\n                      <button \n                        className=\"btn btn-danger\"\n                        onClick={() => handleDelete(supplier.id)}\n                      >\n                        حذف\n                      </button>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Suppliers;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACc,eAAe,EAAEC,kBAAkB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd4B,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMmB,QAAQ,GAAG,MAAM5B,KAAK,CAAC6B,GAAG,CAAC,qCAAqC,CAAC;MACvEtB,YAAY,CAACqB,QAAQ,CAACE,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,QAAQ,CAAC,4BAA4B,CAAC;MACtCO,OAAO,CAACR,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,iBAAiB,GAAIC,CAAC,IAAK;IAC/BlB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACmB,CAAC,CAACC,MAAM,CAAClB,IAAI,GAAGiB,CAAC,CAACC,MAAM,CAACC;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,IAAIzB,eAAe,EAAE;QACnB,MAAMZ,KAAK,CAACsC,GAAG,CAAC,uCAAuC1B,eAAe,CAAC2B,EAAE,EAAE,EAAEzB,QAAQ,CAAC;QACtFQ,UAAU,CAAC,uBAAuB,CAAC;MACrC,CAAC,MAAM;QACL,MAAMtB,KAAK,CAACwC,IAAI,CAAC,qCAAqC,EAAE1B,QAAQ,CAAC;QACjEQ,UAAU,CAAC,uBAAuB,CAAC;MACrC;MAEAP,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACXC,UAAU,EAAE;MACd,CAAC,CAAC;MACFT,WAAW,CAAC,KAAK,CAAC;MAClBE,kBAAkB,CAAC,IAAI,CAAC;MACxBc,cAAc,CAAC,CAAC;MAEhBc,UAAU,CAAC,MAAMnB,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,QAAQ,CAAC,0BAA0B,CAAC;MACpCO,OAAO,CAACR,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMmB,UAAU,GAAIC,QAAQ,IAAK;IAC/B9B,kBAAkB,CAAC8B,QAAQ,CAAC;IAC5B5B,WAAW,CAAC;MACVC,IAAI,EAAE2B,QAAQ,CAAC3B,IAAI;MACnBC,KAAK,EAAE0B,QAAQ,CAAC1B,KAAK,IAAI,EAAE;MAC3BC,KAAK,EAAEyB,QAAQ,CAACzB,KAAK,IAAI,EAAE;MAC3BC,OAAO,EAAEwB,QAAQ,CAACxB,OAAO,IAAI,EAAE;MAC/BC,UAAU,EAAEuB,QAAQ,CAACvB,UAAU,IAAI;IACrC,CAAC,CAAC;IACFT,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMiC,YAAY,GAAG,MAAOL,EAAE,IAAK;IACjC,IAAIM,MAAM,CAACC,OAAO,CAAC,iCAAiC,CAAC,EAAE;MACrD,IAAI;QACF,MAAM9C,KAAK,CAAC+C,MAAM,CAAC,uCAAuCR,EAAE,EAAE,CAAC;QAC/DjB,UAAU,CAAC,qBAAqB,CAAC;QACjCK,cAAc,CAAC,CAAC;QAChBc,UAAU,CAAC,MAAMnB,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,QAAQ,CAAC,mBAAmB,CAAC;QAC7BO,OAAO,CAACR,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;IACF;EACF,CAAC;EAED,MAAMyB,SAAS,GAAGA,CAAA,KAAM;IACtBjC,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE;IACd,CAAC,CAAC;IACFT,WAAW,CAAC,KAAK,CAAC;IAClBE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,IAAIL,OAAO,EAAE;IACX,oBACEL,OAAA;MAAK8C,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxB/C,OAAA;QAAK8C,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC;EAEV;EAEA,oBACEnD,OAAA;IAAK8C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB/C,OAAA;MAAI8C,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAE7CjC,OAAO,iBAAIlB,OAAA;MAAK8C,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAE7B;IAAO;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACnD/B,KAAK,iBAAIpB,OAAA;MAAK8C,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAE3B;IAAK;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE9CnD,OAAA;MAAK8C,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB/C,OAAA;QAAK8C,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1B/C,OAAA;UAAKoD,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAR,QAAA,gBACrF/C,OAAA;YAAI8C,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9CnD,OAAA;YAAKoD,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEG,GAAG,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAC3C/C,OAAA;cACE8C,SAAS,EAAC,iBAAiB;cAC3BW,OAAO,EAAEA,CAAA,KAAMjD,WAAW,CAAC,CAACD,QAAQ,CAAE;cAAAwC,QAAA,EAErCxC,QAAQ,GAAG,OAAO,GAAG;YAAiB;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACTnD,OAAA;cACE8C,SAAS,EAAC,iBAAiB;cAC3BW,OAAO,EAAEA,CAAA,KAAMlC,aAAa,CAAC,IAAI,CAAE;cAAAwB,QAAA,EACpC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL5C,QAAQ,iBACPP,OAAA;QAAK8C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB/C,OAAA;UAAM0D,QAAQ,EAAEzB,YAAa;UAAAc,QAAA,gBAC3B/C,OAAA;YAAK8C,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB/C,OAAA;cAAK8C,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvB/C,OAAA;gBAAK8C,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/C,OAAA;kBAAO8C,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDnD,OAAA;kBACE2D,IAAI,EAAC,MAAM;kBACX9C,IAAI,EAAC,MAAM;kBACXmB,KAAK,EAAErB,QAAQ,CAACE,IAAK;kBACrB+C,QAAQ,EAAE/B,iBAAkB;kBAC5BiB,SAAS,EAAC,cAAc;kBACxBe,QAAQ;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnD,OAAA;cAAK8C,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvB/C,OAAA;gBAAK8C,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/C,OAAA;kBAAO8C,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChDnD,OAAA;kBACE2D,IAAI,EAAC,MAAM;kBACX9C,IAAI,EAAC,OAAO;kBACZmB,KAAK,EAAErB,QAAQ,CAACG,KAAM;kBACtB8C,QAAQ,EAAE/B,iBAAkB;kBAC5BiB,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnD,OAAA;cAAK8C,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvB/C,OAAA;gBAAK8C,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/C,OAAA;kBAAO8C,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvDnD,OAAA;kBACE2D,IAAI,EAAC,OAAO;kBACZ9C,IAAI,EAAC,OAAO;kBACZmB,KAAK,EAAErB,QAAQ,CAACI,KAAM;kBACtB6C,QAAQ,EAAE/B,iBAAkB;kBAC5BiB,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnD,OAAA;cAAK8C,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvB/C,OAAA;gBAAK8C,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/C,OAAA;kBAAO8C,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnDnD,OAAA;kBACE2D,IAAI,EAAC,MAAM;kBACX9C,IAAI,EAAC,YAAY;kBACjBmB,KAAK,EAAErB,QAAQ,CAACM,UAAW;kBAC3B2C,QAAQ,EAAE/B,iBAAkB;kBAC5BiB,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnD,OAAA;cAAK8C,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClB/C,OAAA;gBAAK8C,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB/C,OAAA;kBAAO8C,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7CnD,OAAA;kBACEa,IAAI,EAAC,SAAS;kBACdmB,KAAK,EAAErB,QAAQ,CAACK,OAAQ;kBACxB4C,QAAQ,EAAE/B,iBAAkB;kBAC5BiB,SAAS,EAAC,cAAc;kBACxBgB,IAAI,EAAC;gBAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNnD,OAAA;YAAKoD,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEG,GAAG,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAC3C/C,OAAA;cAAQ2D,IAAI,EAAC,QAAQ;cAACb,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC9CtC,eAAe,GAAG,cAAc,GAAG;YAAc;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACTnD,OAAA;cAAQ2D,IAAI,EAAC,QAAQ;cAACb,SAAS,EAAC,gBAAgB;cAACW,OAAO,EAAEZ,SAAU;cAAAE,QAAA,EAAC;YAErE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,eAEDnD,OAAA;QAAK8C,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB5C,SAAS,CAAC4D,MAAM,KAAK,CAAC,gBACrB/D,OAAA;UAAA+C,QAAA,EAAG;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAErCnD,OAAA;UAAO8C,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACtB/C,OAAA;YAAA+C,QAAA,eACE/C,OAAA;cAAA+C,QAAA,gBACE/C,OAAA;gBAAA+C,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdnD,OAAA;gBAAA+C,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfnD,OAAA;gBAAA+C,QAAA,EAAI;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1BnD,OAAA;gBAAA+C,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBnD,OAAA;gBAAA+C,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfnD,OAAA;gBAAA+C,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRnD,OAAA;YAAA+C,QAAA,EACG5C,SAAS,CAAC6D,GAAG,CAACxB,QAAQ;cAAA,IAAAyB,iBAAA;cAAA,oBACrBjE,OAAA;gBAAA+C,QAAA,gBACE/C,OAAA;kBAAA+C,QAAA,EAAKP,QAAQ,CAAC3B;gBAAI;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxBnD,OAAA;kBAAA+C,QAAA,EAAKP,QAAQ,CAAC1B,KAAK,IAAI;gBAAG;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChCnD,OAAA;kBAAA+C,QAAA,EAAKP,QAAQ,CAACzB,KAAK,IAAI;gBAAG;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChCnD,OAAA;kBAAA+C,QAAA,EAAKP,QAAQ,CAACvB,UAAU,IAAI;gBAAG;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrCnD,OAAA;kBAAA+C,QAAA,GAAK,EAAAkB,iBAAA,GAAAzB,QAAQ,CAAC0B,OAAO,cAAAD,iBAAA,uBAAhBA,iBAAA,CAAkBE,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,gBAAI;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxDnD,OAAA;kBAAA+C,QAAA,gBACE/C,OAAA;oBACE8C,SAAS,EAAC,iBAAiB;oBAC3BW,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAACC,QAAQ,CAAE;oBACpCY,KAAK,EAAE;sBAAEgB,UAAU,EAAE;oBAAS,CAAE;oBAAArB,QAAA,EACjC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTnD,OAAA;oBACE8C,SAAS,EAAC,gBAAgB;oBAC1BW,OAAO,EAAEA,CAAA,KAAMhB,YAAY,CAACD,QAAQ,CAACJ,EAAE,CAAE;oBAAAW,QAAA,EAC1C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GApBEX,QAAQ,CAACJ,EAAE;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqBhB,CAAC;YAAA,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjD,EAAA,CA5QID,SAAS;AAAAoE,EAAA,GAATpE,SAAS;AA8Qf,eAAeA,SAAS;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}