const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const multer = require('multer');
const pdfParse = require('pdf-parse');

console.log('بدء تشغيل الخادم...');

let db;
try {
    db = require('./database');
    console.log('تم الاتصال بقاعدة البيانات بنجاح');
} catch (error) {
    console.error('خطأ في الاتصال بقاعدة البيانات:', error);
    process.exit(1);
}

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// إعداد multer لرفع الملفات
const upload = multer({
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB
    }
});

// ==================== العملاء ====================

// الحصول على جميع العملاء
app.get('/api/customers', (req, res) => {
    db.all('SELECT * FROM customers ORDER BY name', (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows);
    });
});

// إضافة عميل جديد
app.post('/api/customers', (req, res) => {
    const { name, phone, email, address, tax_number } = req.body;
    
    db.run(
        'INSERT INTO customers (name, phone, email, address, tax_number) VALUES (?, ?, ?, ?, ?)',
        [name, phone, email, address, tax_number],
        function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            res.json({ id: this.lastID, message: 'تم إضافة العميل بنجاح' });
        }
    );
});

// تحديث عميل
app.put('/api/customers/:id', (req, res) => {
    const { name, phone, email, address, tax_number } = req.body;
    const { id } = req.params;
    
    db.run(
        'UPDATE customers SET name = ?, phone = ?, email = ?, address = ?, tax_number = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [name, phone, email, address, tax_number, id],
        function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            res.json({ message: 'تم تحديث العميل بنجاح' });
        }
    );
});

// حذف عميل
app.delete('/api/customers/:id', (req, res) => {
    const { id } = req.params;
    
    db.run('DELETE FROM customers WHERE id = ?', [id], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ message: 'تم حذف العميل بنجاح' });
    });
});

// ==================== الموردين ====================

// الحصول على جميع الموردين
app.get('/api/suppliers', (req, res) => {
    db.all('SELECT * FROM suppliers ORDER BY name', (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows);
    });
});

// إضافة مورد جديد
app.post('/api/suppliers', (req, res) => {
    const { name, phone, email, address, tax_number } = req.body;
    
    db.run(
        'INSERT INTO suppliers (name, phone, email, address, tax_number) VALUES (?, ?, ?, ?, ?)',
        [name, phone, email, address, tax_number],
        function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            res.json({ id: this.lastID, message: 'تم إضافة المورد بنجاح' });
        }
    );
});

// تحديث مورد
app.put('/api/suppliers/:id', (req, res) => {
    const { name, phone, email, address, tax_number } = req.body;
    const { id } = req.params;

    db.run(
        'UPDATE suppliers SET name = ?, phone = ?, email = ?, address = ?, tax_number = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [name, phone, email, address, tax_number, id],
        function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            res.json({ message: 'تم تحديث المورد بنجاح' });
        }
    );
});

// تحديث منتج
app.put('/api/products/:id', (req, res) => {
    const { name, description, unit, price, cost, stock_quantity } = req.body;
    const { id } = req.params;

    db.run(
        'UPDATE products SET name = ?, description = ?, unit = ?, price = ?, cost = ?, stock_quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [name, description, unit, price, cost, stock_quantity, id],
        function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            res.json({ message: 'تم تحديث المنتج بنجاح' });
        }
    );
});

// حذف مورد
app.delete('/api/suppliers/:id', (req, res) => {
    const { id } = req.params;
    
    db.run('DELETE FROM suppliers WHERE id = ?', [id], function(err) {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json({ message: 'تم حذف المورد بنجاح' });
    });
});

// ==================== المنتجات ====================

// الحصول على جميع المنتجات
app.get('/api/products', (req, res) => {
    db.all('SELECT * FROM products ORDER BY name', (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows);
    });
});

// إضافة منتج جديد
app.post('/api/products', (req, res) => {
    const { name, description, unit, price, cost, stock_quantity } = req.body;
    
    db.run(
        'INSERT INTO products (name, description, unit, price, cost, stock_quantity) VALUES (?, ?, ?, ?, ?, ?)',
        [name, description, unit, price, cost, stock_quantity],
        function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            res.json({ id: this.lastID, message: 'تم إضافة المنتج بنجاح' });
        }
    );
});

// ==================== فواتير المبيعات ====================

// الحصول على جميع فواتير المبيعات
app.get('/api/sales-invoices', (req, res) => {
    const query = `
        SELECT si.*, c.name as customer_name 
        FROM sales_invoices si 
        LEFT JOIN customers c ON si.customer_id = c.id 
        ORDER BY si.invoice_date DESC
    `;
    
    db.all(query, (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows);
    });
});

// إضافة فاتورة مبيعات جديدة
app.post('/api/sales-invoices', (req, res) => {
    const { invoice_number, customer_id, total_amount, tax_amount, discount_amount, net_amount, invoice_date, due_date, notes, items } = req.body;
    
    db.serialize(() => {
        db.run('BEGIN TRANSACTION');
        
        // إدراج الفاتورة
        db.run(
            'INSERT INTO sales_invoices (invoice_number, customer_id, total_amount, tax_amount, discount_amount, net_amount, invoice_date, due_date, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
            [invoice_number, customer_id, total_amount, tax_amount, discount_amount, net_amount, invoice_date, due_date, notes],
            function(err) {
                if (err) {
                    db.run('ROLLBACK');
                    res.status(500).json({ error: err.message });
                    return;
                }
                
                const invoiceId = this.lastID;
                
                // إدراج عناصر الفاتورة
                const stmt = db.prepare('INSERT INTO sales_invoice_items (invoice_id, product_id, product_name, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?, ?)');
                
                items.forEach(item => {
                    stmt.run([invoiceId, item.product_id, item.product_name, item.quantity, item.unit_price, item.total_price]);
                });
                
                stmt.finalize();
                
                // إضافة حركة للخزينة
                db.run(
                    'INSERT INTO treasury_transactions (transaction_type, amount, description, category, reference_type, reference_id, transaction_date) VALUES (?, ?, ?, ?, ?, ?, ?)',
                    ['income', net_amount, `فاتورة مبيعات رقم ${invoice_number}`, 'مبيعات', 'sales', invoiceId, invoice_date]
                );
                
                db.run('COMMIT');
                res.json({ id: invoiceId, message: 'تم إضافة فاتورة المبيعات بنجاح' });
            }
        );
    });
});

// ==================== المصاريف ====================

// الحصول على جميع المصاريف
app.get('/api/expenses', (req, res) => {
    db.all('SELECT * FROM expenses ORDER BY expense_date DESC', (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows);
    });
});

// إضافة مصروف جديد
app.post('/api/expenses', (req, res) => {
    const { category, description, amount, expense_date, payment_method, receipt_number, notes } = req.body;

    db.run(
        'INSERT INTO expenses (category, description, amount, expense_date, payment_method, receipt_number, notes) VALUES (?, ?, ?, ?, ?, ?, ?)',
        [category, description, amount, expense_date, payment_method, receipt_number, notes],
        function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }

            // إضافة حركة للخزينة
            db.run(
                'INSERT INTO treasury_transactions (transaction_type, amount, description, category, reference_type, reference_id, transaction_date) VALUES (?, ?, ?, ?, ?, ?, ?)',
                ['expense', amount, description, category, 'expense', this.lastID, expense_date]
            );

            res.json({ id: this.lastID, message: 'تم إضافة المصروف بنجاح' });
        }
    );
});

// ==================== حركات الخزينة ====================

// الحصول على جميع حركات الخزينة
app.get('/api/treasury-transactions', (req, res) => {
    db.all('SELECT * FROM treasury_transactions ORDER BY transaction_date DESC', (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows);
    });
});

// ==================== فواتير المشتريات ====================

// الحصول على جميع فواتير المشتريات
app.get('/api/purchase-invoices', (req, res) => {
    const query = `
        SELECT pi.*, s.name as supplier_name
        FROM purchase_invoices pi
        LEFT JOIN suppliers s ON pi.supplier_id = s.id
        ORDER BY pi.invoice_date DESC
    `;

    db.all(query, (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows);
    });
});

// إضافة فاتورة مشتريات جديدة
app.post('/api/purchase-invoices', (req, res) => {
    const { invoice_number, supplier_id, total_amount, tax_amount, discount_amount, net_amount, invoice_date, due_date, notes, items } = req.body;

    db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        // إدراج الفاتورة
        db.run(
            'INSERT INTO purchase_invoices (invoice_number, supplier_id, total_amount, tax_amount, discount_amount, net_amount, invoice_date, due_date, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
            [invoice_number, supplier_id, total_amount, tax_amount, discount_amount, net_amount, invoice_date, due_date, notes],
            function(err) {
                if (err) {
                    db.run('ROLLBACK');
                    res.status(500).json({ error: err.message });
                    return;
                }

                const invoiceId = this.lastID;

                // إدراج عناصر الفاتورة
                const stmt = db.prepare('INSERT INTO purchase_invoice_items (invoice_id, product_id, product_name, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?, ?)');

                items.forEach(item => {
                    stmt.run([invoiceId, item.product_id, item.product_name, item.quantity, item.unit_price, item.total_price]);
                });

                stmt.finalize();

                // إضافة حركة للخزينة
                db.run(
                    'INSERT INTO treasury_transactions (transaction_type, amount, description, category, reference_type, reference_id, transaction_date) VALUES (?, ?, ?, ?, ?, ?, ?)',
                    ['expense', net_amount, `فاتورة مشتريات رقم ${invoice_number}`, 'مشتريات', 'purchase', invoiceId, invoice_date]
                );

                db.run('COMMIT');
                res.json({ id: invoiceId, message: 'تم إضافة فاتورة المشتريات بنجاح' });
            }
        );
    });
});

// ==================== حركات المخزون ====================

// إضافة حركة مخزون
app.post('/api/inventory-movements', (req, res) => {
    const { product_id, movement_type, quantity, old_quantity, new_quantity, reason, notes } = req.body;

    db.run(
        'INSERT INTO inventory_movements (product_id, movement_type, quantity, old_quantity, new_quantity, reason, notes) VALUES (?, ?, ?, ?, ?, ?, ?)',
        [product_id, movement_type, quantity, old_quantity, new_quantity, reason, notes],
        function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            res.json({ id: this.lastID, message: 'تم تسجيل حركة المخزون بنجاح' });
        }
    );
});

// الحصول على حركات المخزون
app.get('/api/inventory-movements', (req, res) => {
    const query = `
        SELECT im.*, p.name as product_name
        FROM inventory_movements im
        LEFT JOIN products p ON im.product_id = p.id
        ORDER BY im.created_at DESC
    `;

    db.all(query, (err, rows) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(rows);
    });
});

// ==================== الإعدادات ====================

// الحصول على الإعدادات
app.get('/api/settings', (req, res) => {
    db.get('SELECT * FROM settings WHERE id = 1', (err, row) => {
        if (err) {
            res.status(500).json({ error: err.message });
            return;
        }
        res.json(row ? JSON.parse(row.data) : {});
    });
});

// حفظ الإعدادات
app.post('/api/settings', (req, res) => {
    const settingsData = JSON.stringify(req.body);

    db.run(
        'INSERT OR REPLACE INTO settings (id, data, updated_at) VALUES (1, ?, CURRENT_TIMESTAMP)',
        [settingsData],
        function(err) {
            if (err) {
                res.status(500).json({ error: err.message });
                return;
            }
            res.json({ message: 'تم حفظ الإعدادات بنجاح' });
        }
    );
});

// ==================== استيراد البيانات ====================

// استيراد العملاء
app.post('/api/import/customers', (req, res) => {
    const { data } = req.body;
    let imported = 0;

    db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        const stmt = db.prepare('INSERT INTO customers (name, phone, email, address, tax_number) VALUES (?, ?, ?, ?, ?)');

        data.forEach(customer => {
            if (customer.name) {
                stmt.run([customer.name, customer.phone, customer.email, customer.address, customer.tax_number]);
                imported++;
            }
        });

        stmt.finalize();
        db.run('COMMIT');

        res.json({ imported, message: `تم استيراد ${imported} عميل بنجاح` });
    });
});

// استيراد الموردين
app.post('/api/import/suppliers', (req, res) => {
    const { data } = req.body;
    let imported = 0;

    db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        const stmt = db.prepare('INSERT INTO suppliers (name, phone, email, address, tax_number) VALUES (?, ?, ?, ?, ?)');

        data.forEach(supplier => {
            if (supplier.name) {
                stmt.run([supplier.name, supplier.phone, supplier.email, supplier.address, supplier.tax_number]);
                imported++;
            }
        });

        stmt.finalize();
        db.run('COMMIT');

        res.json({ imported, message: `تم استيراد ${imported} مورد بنجاح` });
    });
});

// استيراد المنتجات
app.post('/api/import/products', (req, res) => {
    const { data } = req.body;
    let imported = 0;

    db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        const stmt = db.prepare('INSERT INTO products (name, description, unit, price, cost, stock_quantity) VALUES (?, ?, ?, ?, ?, ?)');

        data.forEach(product => {
            if (product.name) {
                stmt.run([product.name, product.description, product.unit, product.price, product.cost, product.stock_quantity]);
                imported++;
            }
        });

        stmt.finalize();
        db.run('COMMIT');

        res.json({ imported, message: `تم استيراد ${imported} منتج بنجاح` });
    });
});

// استيراد المصاريف
app.post('/api/import/expenses', (req, res) => {
    const { data } = req.body;
    let imported = 0;

    db.serialize(() => {
        db.run('BEGIN TRANSACTION');

        const stmt = db.prepare('INSERT INTO expenses (category, description, amount, expense_date, payment_method, receipt_number) VALUES (?, ?, ?, ?, ?, ?)');

        data.forEach(expense => {
            if (expense.description && expense.amount) {
                stmt.run([expense.category, expense.description, expense.amount, expense.expense_date, expense.payment_method, expense.receipt_number]);
                imported++;
            }
        });

        stmt.finalize();
        db.run('COMMIT');

        res.json({ imported, message: `تم استيراد ${imported} مصروف بنجاح` });
    });
});

// ==================== استخراج PDF ====================

// استخراج النص من PDF
app.post('/api/extract-pdf', upload.single('pdf'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'لم يتم رفع ملف PDF' });
        }

        const pdfBuffer = req.file.buffer;
        const data = await pdfParse(pdfBuffer);

        res.json({
            text: data.text,
            pages: data.numpages,
            info: data.info
        });
    } catch (error) {
        console.error('Error extracting PDF:', error);
        res.status(500).json({ error: 'خطأ في استخراج النص من PDF: ' + error.message });
    }
});

// بدء الخادم
app.listen(PORT, () => {
    console.log(`✅ الخادم يعمل على المنفذ ${PORT}`);
    console.log(`🌐 يمكنك الوصول للتطبيق على: http://localhost:${PORT}`);
});

module.exports = app;
