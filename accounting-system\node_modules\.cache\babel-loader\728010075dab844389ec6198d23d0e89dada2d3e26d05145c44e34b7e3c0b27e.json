{"ast": null, "code": "var _jsxFileName = \"F:\\\\\\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\\\\accounting-system\\\\src\\\\components\\\\Products.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport ImportExcel from './ImportExcel';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Products = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showForm, setShowForm] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    unit: '',\n    price: '',\n    cost: '',\n    stock_quantity: ''\n  });\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n  const [showImport, setShowImport] = useState(false);\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/products');\n      setProducts(response.data);\n    } catch (error) {\n      setError('خطأ في جلب بيانات المنتجات');\n      console.error('Error fetching products:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      const productData = {\n        ...formData,\n        price: parseFloat(formData.price) || 0,\n        cost: parseFloat(formData.cost) || 0,\n        stock_quantity: parseInt(formData.stock_quantity) || 0\n      };\n      if (editingProduct) {\n        await axios.put(`http://localhost:5000/api/products/${editingProduct.id}`, productData);\n        setMessage('تم تحديث المنتج بنجاح');\n      } else {\n        await axios.post('http://localhost:5000/api/products', productData);\n        setMessage('تم إضافة المنتج بنجاح');\n      }\n      setFormData({\n        name: '',\n        description: '',\n        unit: '',\n        price: '',\n        cost: '',\n        stock_quantity: ''\n      });\n      setShowForm(false);\n      setEditingProduct(null);\n      fetchProducts();\n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      setError('خطأ في حفظ بيانات المنتج');\n      console.error('Error saving product:', error);\n    }\n  };\n  const handleEdit = product => {\n    var _product$price, _product$cost, _product$stock_quanti;\n    setEditingProduct(product);\n    setFormData({\n      name: product.name,\n      description: product.description || '',\n      unit: product.unit || '',\n      price: ((_product$price = product.price) === null || _product$price === void 0 ? void 0 : _product$price.toString()) || '',\n      cost: ((_product$cost = product.cost) === null || _product$cost === void 0 ? void 0 : _product$cost.toString()) || '',\n      stock_quantity: ((_product$stock_quanti = product.stock_quantity) === null || _product$stock_quanti === void 0 ? void 0 : _product$stock_quanti.toString()) || ''\n    });\n    setShowForm(true);\n  };\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      description: '',\n      unit: '',\n      price: '',\n      cost: '',\n      stock_quantity: ''\n    });\n    setShowForm(false);\n    setEditingProduct(null);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"page-title\",\n      children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 19\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"card-title\",\n            children: \"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-primary\",\n              onClick: () => setShowForm(!showForm),\n              children: showForm ? 'إلغاء' : 'إضافة منتج جديد'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-success\",\n              onClick: () => setShowImport(true),\n              children: \"\\uD83D\\uDCE5 \\u0627\\u0633\\u062A\\u064A\\u0631\\u0627\\u062F \\u0645\\u0646 Excel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), showForm && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleInputChange,\n                  className: \"form-control\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0644\\u0648\\u062D\\u062F\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"unit\",\n                  value: formData.unit,\n                  onChange: handleInputChange,\n                  className: \"form-control\",\n                  placeholder: \"\\u0642\\u0637\\u0639\\u0629\\u060C \\u0643\\u064A\\u0644\\u0648\\u060C \\u0645\\u062A\\u0631...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0628\\u064A\\u0639\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.01\",\n                  name: \"price\",\n                  value: formData.price,\n                  onChange: handleInputChange,\n                  className: \"form-control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u062A\\u0643\\u0644\\u0641\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.01\",\n                  name: \"cost\",\n                  value: formData.cost,\n                  onChange: handleInputChange,\n                  className: \"form-control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629 \\u0627\\u0644\\u0645\\u062A\\u0627\\u062D\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_quantity\",\n                  value: formData.stock_quantity,\n                  onChange: handleInputChange,\n                  className: \"form-control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0644\\u0648\\u0635\\u0641\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  name: \"description\",\n                  value: formData.description,\n                  onChange: handleInputChange,\n                  className: \"form-control\",\n                  rows: \"3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-success\",\n              children: editingProduct ? 'تحديث المنتج' : 'إضافة المنتج'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-danger\",\n              onClick: resetForm,\n              children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: products.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0645\\u0633\\u062C\\u0644\\u0629 \\u062D\\u062A\\u0649 \\u0627\\u0644\\u0622\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0648\\u062D\\u062F\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0628\\u064A\\u0639\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u062A\\u0643\\u0644\\u0641\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629 \\u0627\\u0644\\u0645\\u062A\\u0627\\u062D\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: products.map(product => {\n              var _product$price2, _product$cost2;\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: product.unit || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [((_product$price2 = product.price) === null || _product$price2 === void 0 ? void 0 : _product$price2.toLocaleString()) || '0', \" \\u062C.\\u0645\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [((_product$cost2 = product.cost) === null || _product$cost2 === void 0 ? void 0 : _product$cost2.toLocaleString()) || '0', \" \\u062C.\\u0645\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: product.stock_quantity || '0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-warning\",\n                    onClick: () => handleEdit(product),\n                    style: {\n                      marginLeft: '0.5rem'\n                    },\n                    children: \"\\u062A\\u0639\\u062F\\u064A\\u0644\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this)]\n              }, product.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), showImport && /*#__PURE__*/_jsxDEV(ImportExcel, {\n      type: \"products\",\n      onClose: () => setShowImport(false),\n      onImportComplete: () => {\n        setShowImport(false);\n        fetchProducts();\n        setMessage('تم استيراد المنتجات بنجاح');\n        setTimeout(() => setMessage(''), 3000);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 5\n  }, this);\n};\n_s(Products, \"jvaZHzAKp29VJuTTI8MC1zo3Yl0=\");\n_c = Products;\nexport default Products;\nvar _c;\n$RefreshReg$(_c, \"Products\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "ImportExcel", "jsxDEV", "_jsxDEV", "Products", "_s", "products", "setProducts", "loading", "setLoading", "showForm", "setShowForm", "editingProduct", "setEditingProduct", "formData", "setFormData", "name", "description", "unit", "price", "cost", "stock_quantity", "message", "setMessage", "error", "setError", "showImport", "setShowImport", "fetchProducts", "response", "get", "data", "console", "handleInputChange", "e", "target", "value", "handleSubmit", "preventDefault", "productData", "parseFloat", "parseInt", "put", "id", "post", "setTimeout", "handleEdit", "product", "_product$price", "_product$cost", "_product$stock_quanti", "toString", "resetForm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "justifyContent", "alignItems", "gap", "onClick", "onSubmit", "type", "onChange", "required", "placeholder", "step", "rows", "length", "map", "_product$price2", "_product$cost2", "toLocaleString", "marginLeft", "onClose", "onImportComplete", "_c", "$RefreshReg$"], "sources": ["F:/القيمة المضافة/accounting-system/src/components/Products.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport ImportExcel from './ImportExcel';\n\nconst Products = () => {\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showForm, setShowForm] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    unit: '',\n    price: '',\n    cost: '',\n    stock_quantity: ''\n  });\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n  const [showImport, setShowImport] = useState(false);\n\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/products');\n      setProducts(response.data);\n    } catch (error) {\n      setError('خطأ في جلب بيانات المنتجات');\n      console.error('Error fetching products:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      const productData = {\n        ...formData,\n        price: parseFloat(formData.price) || 0,\n        cost: parseFloat(formData.cost) || 0,\n        stock_quantity: parseInt(formData.stock_quantity) || 0\n      };\n\n      if (editingProduct) {\n        await axios.put(`http://localhost:5000/api/products/${editingProduct.id}`, productData);\n        setMessage('تم تحديث المنتج بنجاح');\n      } else {\n        await axios.post('http://localhost:5000/api/products', productData);\n        setMessage('تم إضافة المنتج بنجاح');\n      }\n      \n      setFormData({\n        name: '',\n        description: '',\n        unit: '',\n        price: '',\n        cost: '',\n        stock_quantity: ''\n      });\n      setShowForm(false);\n      setEditingProduct(null);\n      fetchProducts();\n      \n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      setError('خطأ في حفظ بيانات المنتج');\n      console.error('Error saving product:', error);\n    }\n  };\n\n  const handleEdit = (product) => {\n    setEditingProduct(product);\n    setFormData({\n      name: product.name,\n      description: product.description || '',\n      unit: product.unit || '',\n      price: product.price?.toString() || '',\n      cost: product.cost?.toString() || '',\n      stock_quantity: product.stock_quantity?.toString() || ''\n    });\n    setShowForm(true);\n  };\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      description: '',\n      unit: '',\n      price: '',\n      cost: '',\n      stock_quantity: ''\n    });\n    setShowForm(false);\n    setEditingProduct(null);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"container\">\n        <div className=\"loading\">جاري تحميل بيانات المنتجات...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container\">\n      <h1 className=\"page-title\">إدارة المنتجات</h1>\n      \n      {message && <div className=\"success\">{message}</div>}\n      {error && <div className=\"error\">{error}</div>}\n      \n      <div className=\"card\">\n        <div className=\"card-header\">\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <h3 className=\"card-title\">قائمة المنتجات</h3>\n            <div style={{ display: 'flex', gap: '1rem' }}>\n              <button\n                className=\"btn btn-primary\"\n                onClick={() => setShowForm(!showForm)}\n              >\n                {showForm ? 'إلغاء' : 'إضافة منتج جديد'}\n              </button>\n              <button\n                className=\"btn btn-success\"\n                onClick={() => setShowImport(true)}\n              >\n                📥 استيراد من Excel\n              </button>\n            </div>\n          </div>\n        </div>\n        \n        {showForm && (\n          <div className=\"card-body\">\n            <form onSubmit={handleSubmit}>\n              <div className=\"row\">\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">اسم المنتج *</label>\n                    <input\n                      type=\"text\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                      required\n                    />\n                  </div>\n                </div>\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">الوحدة</label>\n                    <input\n                      type=\"text\"\n                      name=\"unit\"\n                      value={formData.unit}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                      placeholder=\"قطعة، كيلو، متر...\"\n                    />\n                  </div>\n                </div>\n                <div className=\"col-md-4\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">سعر البيع</label>\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      name=\"price\"\n                      value={formData.price}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                    />\n                  </div>\n                </div>\n                <div className=\"col-md-4\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">سعر التكلفة</label>\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      name=\"cost\"\n                      value={formData.cost}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                    />\n                  </div>\n                </div>\n                <div className=\"col-md-4\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">الكمية المتاحة</label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_quantity\"\n                      value={formData.stock_quantity}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                    />\n                  </div>\n                </div>\n                <div className=\"col\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">الوصف</label>\n                    <textarea\n                      name=\"description\"\n                      value={formData.description}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                      rows=\"3\"\n                    ></textarea>\n                  </div>\n                </div>\n              </div>\n              <div style={{ display: 'flex', gap: '1rem' }}>\n                <button type=\"submit\" className=\"btn btn-success\">\n                  {editingProduct ? 'تحديث المنتج' : 'إضافة المنتج'}\n                </button>\n                <button type=\"button\" className=\"btn btn-danger\" onClick={resetForm}>\n                  إلغاء\n                </button>\n              </div>\n            </form>\n          </div>\n        )}\n        \n        <div className=\"card-body\">\n          {products.length === 0 ? (\n            <p>لا توجد منتجات مسجلة حتى الآن</p>\n          ) : (\n            <table className=\"table\">\n              <thead>\n                <tr>\n                  <th>اسم المنتج</th>\n                  <th>الوحدة</th>\n                  <th>سعر البيع</th>\n                  <th>سعر التكلفة</th>\n                  <th>الكمية المتاحة</th>\n                  <th>الإجراءات</th>\n                </tr>\n              </thead>\n              <tbody>\n                {products.map(product => (\n                  <tr key={product.id}>\n                    <td>{product.name}</td>\n                    <td>{product.unit || '-'}</td>\n                    <td>{product.price?.toLocaleString() || '0'} ج.م</td>\n                    <td>{product.cost?.toLocaleString() || '0'} ج.م</td>\n                    <td>{product.stock_quantity || '0'}</td>\n                    <td>\n                      <button \n                        className=\"btn btn-warning\"\n                        onClick={() => handleEdit(product)}\n                        style={{ marginLeft: '0.5rem' }}\n                      >\n                        تعديل\n                      </button>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          )}\n        </div>\n      </div>\n\n      {/* نموذج استيراد Excel */}\n      {showImport && (\n        <ImportExcel\n          type=\"products\"\n          onClose={() => setShowImport(false)}\n          onImportComplete={() => {\n            setShowImport(false);\n            fetchProducts();\n            setMessage('تم استيراد المنتجات بنجاح');\n            setTimeout(() => setMessage(''), 3000);\n          }}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default Products;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd6B,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMoB,QAAQ,GAAG,MAAM7B,KAAK,CAAC8B,GAAG,CAAC,oCAAoC,CAAC;MACtEvB,WAAW,CAACsB,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,QAAQ,CAAC,4BAA4B,CAAC;MACtCO,OAAO,CAACR,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,iBAAiB,GAAIC,CAAC,IAAK;IAC/BnB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACoB,CAAC,CAACC,MAAM,CAACnB,IAAI,GAAGkB,CAAC,CAACC,MAAM,CAACC;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMC,WAAW,GAAG;QAClB,GAAGzB,QAAQ;QACXK,KAAK,EAAEqB,UAAU,CAAC1B,QAAQ,CAACK,KAAK,CAAC,IAAI,CAAC;QACtCC,IAAI,EAAEoB,UAAU,CAAC1B,QAAQ,CAACM,IAAI,CAAC,IAAI,CAAC;QACpCC,cAAc,EAAEoB,QAAQ,CAAC3B,QAAQ,CAACO,cAAc,CAAC,IAAI;MACvD,CAAC;MAED,IAAIT,cAAc,EAAE;QAClB,MAAMZ,KAAK,CAAC0C,GAAG,CAAC,sCAAsC9B,cAAc,CAAC+B,EAAE,EAAE,EAAEJ,WAAW,CAAC;QACvFhB,UAAU,CAAC,uBAAuB,CAAC;MACrC,CAAC,MAAM;QACL,MAAMvB,KAAK,CAAC4C,IAAI,CAAC,oCAAoC,EAAEL,WAAW,CAAC;QACnEhB,UAAU,CAAC,uBAAuB,CAAC;MACrC;MAEAR,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTC,IAAI,EAAE,EAAE;QACRC,cAAc,EAAE;MAClB,CAAC,CAAC;MACFV,WAAW,CAAC,KAAK,CAAC;MAClBE,iBAAiB,CAAC,IAAI,CAAC;MACvBe,aAAa,CAAC,CAAC;MAEfiB,UAAU,CAAC,MAAMtB,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,QAAQ,CAAC,0BAA0B,CAAC;MACpCO,OAAO,CAACR,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMsB,UAAU,GAAIC,OAAO,IAAK;IAAA,IAAAC,cAAA,EAAAC,aAAA,EAAAC,qBAAA;IAC9BrC,iBAAiB,CAACkC,OAAO,CAAC;IAC1BhC,WAAW,CAAC;MACVC,IAAI,EAAE+B,OAAO,CAAC/B,IAAI;MAClBC,WAAW,EAAE8B,OAAO,CAAC9B,WAAW,IAAI,EAAE;MACtCC,IAAI,EAAE6B,OAAO,CAAC7B,IAAI,IAAI,EAAE;MACxBC,KAAK,EAAE,EAAA6B,cAAA,GAAAD,OAAO,CAAC5B,KAAK,cAAA6B,cAAA,uBAAbA,cAAA,CAAeG,QAAQ,CAAC,CAAC,KAAI,EAAE;MACtC/B,IAAI,EAAE,EAAA6B,aAAA,GAAAF,OAAO,CAAC3B,IAAI,cAAA6B,aAAA,uBAAZA,aAAA,CAAcE,QAAQ,CAAC,CAAC,KAAI,EAAE;MACpC9B,cAAc,EAAE,EAAA6B,qBAAA,GAAAH,OAAO,CAAC1B,cAAc,cAAA6B,qBAAA,uBAAtBA,qBAAA,CAAwBC,QAAQ,CAAC,CAAC,KAAI;IACxD,CAAC,CAAC;IACFxC,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMyC,SAAS,GAAGA,CAAA,KAAM;IACtBrC,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,EAAE;MACRC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFV,WAAW,CAAC,KAAK,CAAC;IAClBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,IAAIL,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKkD,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBnD,OAAA;QAAKkD,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC;EAEV;EAEA,oBACEvD,OAAA;IAAKkD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBnD,OAAA;MAAIkD,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAE7CpC,OAAO,iBAAInB,OAAA;MAAKkD,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAEhC;IAAO;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACnDlC,KAAK,iBAAIrB,OAAA;MAAKkD,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAE9B;IAAK;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE9CvD,OAAA;MAAKkD,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBnD,OAAA;QAAKkD,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BnD,OAAA;UAAKwD,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAR,QAAA,gBACrFnD,OAAA;YAAIkD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9CvD,OAAA;YAAKwD,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEG,GAAG,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAC3CnD,OAAA;cACEkD,SAAS,EAAC,iBAAiB;cAC3BW,OAAO,EAAEA,CAAA,KAAMrD,WAAW,CAAC,CAACD,QAAQ,CAAE;cAAA4C,QAAA,EAErC5C,QAAQ,GAAG,OAAO,GAAG;YAAiB;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACTvD,OAAA;cACEkD,SAAS,EAAC,iBAAiB;cAC3BW,OAAO,EAAEA,CAAA,KAAMrC,aAAa,CAAC,IAAI,CAAE;cAAA2B,QAAA,EACpC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELhD,QAAQ,iBACPP,OAAA;QAAKkD,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBnD,OAAA;UAAM8D,QAAQ,EAAE5B,YAAa;UAAAiB,QAAA,gBAC3BnD,OAAA;YAAKkD,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBnD,OAAA;cAAKkD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBnD,OAAA;gBAAKkD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBnD,OAAA;kBAAOkD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDvD,OAAA;kBACE+D,IAAI,EAAC,MAAM;kBACXlD,IAAI,EAAC,MAAM;kBACXoB,KAAK,EAAEtB,QAAQ,CAACE,IAAK;kBACrBmD,QAAQ,EAAElC,iBAAkB;kBAC5BoB,SAAS,EAAC,cAAc;kBACxBe,QAAQ;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvD,OAAA;cAAKkD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBnD,OAAA;gBAAKkD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBnD,OAAA;kBAAOkD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5CvD,OAAA;kBACE+D,IAAI,EAAC,MAAM;kBACXlD,IAAI,EAAC,MAAM;kBACXoB,KAAK,EAAEtB,QAAQ,CAACI,IAAK;kBACrBiD,QAAQ,EAAElC,iBAAkB;kBAC5BoB,SAAS,EAAC,cAAc;kBACxBgB,WAAW,EAAC;gBAAoB;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvD,OAAA;cAAKkD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBnD,OAAA;gBAAKkD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBnD,OAAA;kBAAOkD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/CvD,OAAA;kBACE+D,IAAI,EAAC,QAAQ;kBACbI,IAAI,EAAC,MAAM;kBACXtD,IAAI,EAAC,OAAO;kBACZoB,KAAK,EAAEtB,QAAQ,CAACK,KAAM;kBACtBgD,QAAQ,EAAElC,iBAAkB;kBAC5BoB,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvD,OAAA;cAAKkD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBnD,OAAA;gBAAKkD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBnD,OAAA;kBAAOkD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjDvD,OAAA;kBACE+D,IAAI,EAAC,QAAQ;kBACbI,IAAI,EAAC,MAAM;kBACXtD,IAAI,EAAC,MAAM;kBACXoB,KAAK,EAAEtB,QAAQ,CAACM,IAAK;kBACrB+C,QAAQ,EAAElC,iBAAkB;kBAC5BoB,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvD,OAAA;cAAKkD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBnD,OAAA;gBAAKkD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBnD,OAAA;kBAAOkD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpDvD,OAAA;kBACE+D,IAAI,EAAC,QAAQ;kBACblD,IAAI,EAAC,gBAAgB;kBACrBoB,KAAK,EAAEtB,QAAQ,CAACO,cAAe;kBAC/B8C,QAAQ,EAAElC,iBAAkB;kBAC5BoB,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNvD,OAAA;cAAKkD,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClBnD,OAAA;gBAAKkD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBnD,OAAA;kBAAOkD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3CvD,OAAA;kBACEa,IAAI,EAAC,aAAa;kBAClBoB,KAAK,EAAEtB,QAAQ,CAACG,WAAY;kBAC5BkD,QAAQ,EAAElC,iBAAkB;kBAC5BoB,SAAS,EAAC,cAAc;kBACxBkB,IAAI,EAAC;gBAAG;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNvD,OAAA;YAAKwD,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEG,GAAG,EAAE;YAAO,CAAE;YAAAT,QAAA,gBAC3CnD,OAAA;cAAQ+D,IAAI,EAAC,QAAQ;cAACb,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC9C1C,cAAc,GAAG,cAAc,GAAG;YAAc;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACTvD,OAAA;cAAQ+D,IAAI,EAAC,QAAQ;cAACb,SAAS,EAAC,gBAAgB;cAACW,OAAO,EAAEZ,SAAU;cAAAE,QAAA,EAAC;YAErE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,eAEDvD,OAAA;QAAKkD,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBhD,QAAQ,CAACkE,MAAM,KAAK,CAAC,gBACpBrE,OAAA;UAAAmD,QAAA,EAAG;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAEpCvD,OAAA;UAAOkD,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACtBnD,OAAA;YAAAmD,QAAA,eACEnD,OAAA;cAAAmD,QAAA,gBACEnD,OAAA;gBAAAmD,QAAA,EAAI;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBvD,OAAA;gBAAAmD,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfvD,OAAA;gBAAAmD,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClBvD,OAAA;gBAAAmD,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBvD,OAAA;gBAAAmD,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvBvD,OAAA;gBAAAmD,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRvD,OAAA;YAAAmD,QAAA,EACGhD,QAAQ,CAACmE,GAAG,CAAC1B,OAAO;cAAA,IAAA2B,eAAA,EAAAC,cAAA;cAAA,oBACnBxE,OAAA;gBAAAmD,QAAA,gBACEnD,OAAA;kBAAAmD,QAAA,EAAKP,OAAO,CAAC/B;gBAAI;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvBvD,OAAA;kBAAAmD,QAAA,EAAKP,OAAO,CAAC7B,IAAI,IAAI;gBAAG;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9BvD,OAAA;kBAAAmD,QAAA,GAAK,EAAAoB,eAAA,GAAA3B,OAAO,CAAC5B,KAAK,cAAAuD,eAAA,uBAAbA,eAAA,CAAeE,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,gBAAI;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrDvD,OAAA;kBAAAmD,QAAA,GAAK,EAAAqB,cAAA,GAAA5B,OAAO,CAAC3B,IAAI,cAAAuD,cAAA,uBAAZA,cAAA,CAAcC,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,gBAAI;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpDvD,OAAA;kBAAAmD,QAAA,EAAKP,OAAO,CAAC1B,cAAc,IAAI;gBAAG;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxCvD,OAAA;kBAAAmD,QAAA,eACEnD,OAAA;oBACEkD,SAAS,EAAC,iBAAiB;oBAC3BW,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAACC,OAAO,CAAE;oBACnCY,KAAK,EAAE;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAAvB,QAAA,EACjC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GAdEX,OAAO,CAACJ,EAAE;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAef,CAAC;YAAA,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLhC,UAAU,iBACTvB,OAAA,CAACF,WAAW;MACViE,IAAI,EAAC,UAAU;MACfY,OAAO,EAAEA,CAAA,KAAMnD,aAAa,CAAC,KAAK,CAAE;MACpCoD,gBAAgB,EAAEA,CAAA,KAAM;QACtBpD,aAAa,CAAC,KAAK,CAAC;QACpBC,aAAa,CAAC,CAAC;QACfL,UAAU,CAAC,2BAA2B,CAAC;QACvCsB,UAAU,CAAC,MAAMtB,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IAAE;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrD,EAAA,CAhSID,QAAQ;AAAA4E,EAAA,GAAR5E,QAAQ;AAkSd,eAAeA,QAAQ;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}