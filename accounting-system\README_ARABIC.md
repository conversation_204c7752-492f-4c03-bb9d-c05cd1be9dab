# 💼 نظام المحاسبة المتكامل

نظام محاسبي شامل ومتطور باللغة العربية، مصمم خصيصاً للشركات الصغيرة والمتوسطة في الوطن العربي. يتميز بواجهة عربية حديثة وإمكانيات متقدمة لإدارة جميع العمليات المحاسبية.

## 🌟 المميزات الرئيسية

### 📊 إدارة شاملة ومتكاملة
- **إدارة العملاء**: إضافة وتعديل وتتبع بيانات العملاء مع حساب الأرصدة
- **إدارة الموردين**: إدارة كاملة لبيانات الموردين والتعاملات معهم
- **إدارة المنتجات**: كتالوج شامل للمنتجات مع الأسعار والمخزون
- **إدارة المخزون**: تتبع دقيق للمخزون مع التنبيهات الذكية وحركات المخزون

### 💰 النظام المالي المتطور
- **فواتير المبيعات**: إنشاء فواتير احترافية مع حساب الضرائب والخصومات تلقائياً
- **فواتير المشتريات**: تسجيل وإدارة جميع المشتريات مع ربطها بالموردين
- **إدارة الخزينة**: تتبع دقيق لجميع الحركات المالية الواردة والصادرة
- **إدارة المصاريف**: تصنيف وتتبع جميع أنواع المصاريف مع التقارير التفصيلية

### 📈 التقارير والتحليلات المتقدمة
- **تقارير مالية شاملة**: قوائم الأرباح والخسائر والميزانيات
- **رسوم بيانية تفاعلية**: تصور البيانات بطريقة واضحة ومفهومة باستخدام Chart.js
- **تحليل الأداء**: مؤشرات الأداء الرئيسية والتوصيات الذكية
- **تقارير قابلة للطباعة**: تصدير التقارير بصيغ مختلفة

### 📥 استيراد البيانات المتقدم
- **استيراد من Excel**: استيراد البيانات من ملفات Excel مع قوالب جاهزة
- **استيراد من PDF**: استخراج البيانات من ملفات PDF تلقائياً
- **قوالب جاهزة**: قوالب Excel محترفة مع أمثلة وتعليمات مفصلة
- **مركز التحميل**: مركز شامل لتحميل جميع القوالب والأدلة

### 🔒 الأمان والنسخ الاحتياطي
- **نظام النسخ الاحتياطي**: حماية البيانات مع إمكانية الاستعادة الكاملة
- **تصدير البيانات**: تصدير إلى Excel, CSV, JSON
- **إعدادات شاملة**: تخصيص كامل للنظام حسب احتياجات الشركة

### ⚙️ إعدادات النظام المتقدمة
- **إعدادات الشركة**: بيانات الشركة الكاملة مع الشعار
- **إعدادات الضرائب**: تخصيص معدلات الضرائب والحسابات
- **إعدادات العملة**: دعم عملات متعددة مع التنسيق المحلي
- **إعدادات الفواتير**: تخصيص شكل ومحتوى الفواتير
- **إعدادات النظام**: تخصيص اللغة والتاريخ والتنبيهات

## 🚀 التقنيات المستخدمة

### Frontend (الواجهة الأمامية)
- **React 19**: أحدث إصدار من مكتبة React لبناء واجهات المستخدم
- **React Router**: للتنقل السلس بين الصفحات
- **Chart.js & React-ChartJS-2**: للرسوم البيانية التفاعلية المتقدمة
- **Axios**: للتواصل مع الخادم
- **XLSX**: للتعامل مع ملفات Excel
- **React-Dropzone**: لرفع الملفات بسهولة
- **CSS3**: تصميم متجاوب وحديث مع دعم RTL

### Backend (الخادم)
- **Node.js**: بيئة تشغيل JavaScript عالية الأداء
- **Express.js**: إطار عمل الخادم السريع والمرن
- **SQLite**: قاعدة بيانات محلية سريعة وموثوقة
- **Multer**: لمعالجة رفع الملفات
- **PDF-Parse**: لاستخراج النص من ملفات PDF
- **CORS**: للسماح بالطلبات من مصادر مختلفة

## 📦 التثبيت والتشغيل

### المتطلبات الأساسية
- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone [repository-url]
cd accounting-system
```

2. **تثبيت مكتبات الواجهة الأمامية**
```bash
npm install
```

3. **تثبيت مكتبات الخادم**
```bash
cd server
npm install
```

4. **تشغيل الخادم**
```bash
cd server
node server.js
```

5. **تشغيل الواجهة الأمامية**
```bash
# في terminal منفصل
npm start
```

6. **الوصول للتطبيق**
- الواجهة الأمامية: http://localhost:3000
- الخادم: http://localhost:5000

## 📱 كيفية الاستخدام

### البدء السريع

1. **إعداد النظام**
   - انتقل إلى "الإعدادات" لتخصيص بيانات شركتك
   - اضبط إعدادات الضرائب والعملة

2. **إضافة البيانات الأساسية**
   - أضف العملاء في قسم "العملاء"
   - أضف الموردين في قسم "الموردين"
   - أضف المنتجات مع الأسعار والكميات

3. **استيراد البيانات بالجملة**
   - استخدم "مركز التحميل" لتحميل القوالب
   - املأ القوالب ببياناتك
   - استورد البيانات من Excel أو PDF

4. **إنشاء الفواتير**
   - أنشئ فواتير مبيعات احترافية
   - سجل فواتير المشتريات
   - تتبع حالة الدفع

5. **إدارة المخزون**
   - راقب مستويات المخزون
   - احصل على تنبيهات المخزون المنخفض
   - سجل حركات المخزون

6. **متابعة الأموال**
   - راقب حركات الخزينة
   - سجل المصاريف بفئاتها
   - تتبع الأرباح والخسائر

7. **التقارير والتحليلات**
   - اطلع على التقارير المالية الشاملة
   - راجع الرسوم البيانية التفاعلية
   - احصل على توصيات لتحسين الأداء

### النسخ الاحتياطي

- انتقل إلى قسم "النسخ الاحتياطي"
- اضغط على "إنشاء نسخة احتياطية"
- حمل الملف واحتفظ به في مكان آمن
- يمكنك استعادة البيانات في أي وقت

## 🗂️ هيكل المشروع

```
accounting-system/
├── public/                 # الملفات العامة
├── src/                   # كود الواجهة الأمامية
│   ├── components/        # مكونات React
│   │   ├── Dashboard.js   # لوحة التحكم الرئيسية
│   │   ├── Customers.js   # إدارة العملاء
│   │   ├── Suppliers.js   # إدارة الموردين
│   │   ├── Products.js    # إدارة المنتجات
│   │   ├── Inventory.js   # إدارة المخزون
│   │   ├── SalesInvoices.js # فواتير المبيعات
│   │   ├── PurchaseInvoices.js # فواتير المشتريات
│   │   ├── Treasury.js    # إدارة الخزينة
│   │   ├── Expenses.js    # إدارة المصاريف
│   │   ├── Reports.js     # التقارير والتحليلات
│   │   ├── Charts.js      # الرسوم البيانية
│   │   ├── Settings.js    # إعدادات النظام
│   │   ├── Backup.js      # النسخ الاحتياطي
│   │   ├── ImportExcel.js # استيراد Excel
│   │   ├── ImportPDF.js   # استيراد PDF
│   │   ├── TemplateGenerator.js # مولد القوالب
│   │   ├── DownloadCenter.js # مركز التحميل
│   │   └── Navbar.js      # شريط التنقل
│   ├── App.js            # المكون الرئيسي
│   ├── App.css           # الأنماط الرئيسية
│   └── index.js          # نقطة البداية
├── server/               # كود الخادم
│   ├── database.js       # إعداد قاعدة البيانات
│   ├── server.js         # الخادم الرئيسي مع جميع APIs
│   └── package.json      # مكتبات الخادم
└── README_ARABIC.md     # هذا الملف
```

## 🔧 المميزات المتقدمة

### استيراد البيانات الذكي
- **قوالب Excel محترفة**: قوالب جاهزة مع أمثلة وتعليمات
- **استيراد PDF**: استخراج البيانات من ملفات PDF تلقائياً
- **التحقق من البيانات**: فحص البيانات قبل الاستيراد
- **معاينة البيانات**: مراجعة البيانات قبل الحفظ

### الرسوم البيانية التفاعلية
- **مقارنة المبيعات والمشتريات**: رسوم بيانية شهرية
- **توزيع المصاريف**: رسوم دائرية للمصاريف
- **اتجاه الأرباح**: رسوم خطية للأرباح
- **أداء المنتجات**: تحليل أداء المنتجات

### إدارة المخزون المتقدمة
- **تنبيهات ذكية**: تنبيهات للمخزون المنخفض
- **حركات المخزون**: تتبع جميع حركات المخزون
- **تقييم المخزون**: حساب قيمة المخزون الحالية
- **تقارير المخزون**: تقارير مفصلة عن حالة المخزون

## 🎯 الميزات القادمة

- **تطبيق الموبايل**: تطبيق React Native
- **نظام المستخدمين**: صلاحيات متعددة المستويات
- **التكامل البنكي**: ربط مع البنوك المحلية
- **الفواتير الإلكترونية**: دعم الفواتير الإلكترونية الحكومية
- **التقارير المتقدمة**: المزيد من التقارير والتحليلات
- **الذكاء الاصطناعي**: توصيات ذكية وتنبؤات

## 📞 الدعم والمساعدة

### الحصول على المساعدة
- راجع هذا الدليل أولاً
- تحقق من رسائل الخطأ في console المتصفح
- تأكد من تحديث جميع المكتبات

### المساهمة في المشروع
نرحب بمساهماتكم! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 🙏 شكر وتقدير

شكر خاص لجميع المطورين والمساهمين في المكتبات مفتوحة المصدر المستخدمة في هذا المشروع:
- React Team
- Chart.js Team
- Express.js Team
- SQLite Team
- وجميع مطوري المكتبات الأخرى

---

**تم تطوير هذا النظام بعناية فائقة ليلبي احتياجات الشركات العربية. نتمنى أن يساعدكم في إدارة أعمالكم بكفاءة أكبر! 🚀**

**للدعم الفني: <EMAIL>**
**الموقع الإلكتروني: www.accounting-system.com**
