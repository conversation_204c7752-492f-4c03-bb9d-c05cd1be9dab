{"ast": null, "code": "var _jsxFileName = \"F:\\\\\\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\\\\accounting-system\\\\src\\\\components\\\\ImportExcel.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport * as XLSX from 'xlsx';\nimport axios from 'axios';\nimport { downloadTemplate } from './TemplateGenerator';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImportExcel = ({\n  type,\n  onClose,\n  onImportComplete\n}) => {\n  _s();\n  const [file, setFile] = useState(null);\n  const [data, setData] = useState([]);\n  const [headers, setHeaders] = useState([]);\n  const [mapping, setMapping] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [step, setStep] = useState(1); // 1: Upload, 2: Map, 3: Preview, 4: Import\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n\n  // تعريف الحقول المطلوبة لكل نوع\n  const fieldMappings = {\n    customers: {\n      name: 'اسم العميل',\n      phone: 'رقم الهاتف',\n      email: 'البريد الإلكتروني',\n      address: 'العنوان',\n      tax_number: 'الرقم الضريبي'\n    },\n    suppliers: {\n      name: 'اسم المورد',\n      phone: 'رقم الهاتف',\n      email: 'البريد الإلكتروني',\n      address: 'العنوان',\n      tax_number: 'الرقم الضريبي'\n    },\n    products: {\n      name: 'اسم المنتج',\n      description: 'الوصف',\n      unit: 'الوحدة',\n      price: 'سعر البيع',\n      cost: 'سعر التكلفة',\n      stock_quantity: 'الكمية المتاحة'\n    },\n    expenses: {\n      category: 'فئة المصروف',\n      description: 'الوصف',\n      amount: 'المبلغ',\n      expense_date: 'تاريخ المصروف',\n      payment_method: 'طريقة الدفع',\n      receipt_number: 'رقم الإيصال'\n    }\n  };\n  const onDrop = acceptedFiles => {\n    const file = acceptedFiles[0];\n    if (file) {\n      setFile(file);\n      readExcelFile(file);\n    }\n  };\n  const {\n    getRootProps,\n    getInputProps,\n    isDragActive\n  } = useDropzone({\n    onDrop,\n    accept: {\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],\n      'application/vnd.ms-excel': ['.xls'],\n      'text/csv': ['.csv']\n    },\n    multiple: false\n  });\n  const readExcelFile = file => {\n    const reader = new FileReader();\n    reader.onload = e => {\n      try {\n        const data = new Uint8Array(e.target.result);\n        const workbook = XLSX.read(data, {\n          type: 'array'\n        });\n        const sheetName = workbook.SheetNames[0];\n        const worksheet = workbook.Sheets[sheetName];\n        const jsonData = XLSX.utils.sheet_to_json(worksheet, {\n          header: 1\n        });\n        if (jsonData.length > 0) {\n          const headers = jsonData[0];\n          const rows = jsonData.slice(1).filter(row => row.some(cell => cell !== undefined && cell !== ''));\n          setHeaders(headers);\n          setData(rows);\n          setStep(2);\n        } else {\n          setError('الملف فارغ أو لا يحتوي على بيانات صالحة');\n        }\n      } catch (error) {\n        setError('خطأ في قراءة الملف: ' + error.message);\n      }\n    };\n    reader.readAsArrayBuffer(file);\n  };\n  const handleMappingChange = (field, headerIndex) => {\n    setMapping(prev => ({\n      ...prev,\n      [field]: headerIndex\n    }));\n  };\n  const validateMapping = () => {\n    const requiredFields = Object.keys(fieldMappings[type]);\n    const mappedFields = Object.keys(mapping);\n\n    // التحقق من وجود الحقول المطلوبة\n    const missingFields = requiredFields.filter(field => !mappedFields.includes(field) || mapping[field] === '');\n    if (missingFields.length > 0) {\n      setError(`الحقول التالية مطلوبة: ${missingFields.map(f => fieldMappings[type][f]).join(', ')}`);\n      return false;\n    }\n    return true;\n  };\n  const previewData = () => {\n    if (!validateMapping()) return;\n    setStep(3);\n  };\n  const getPreviewRows = () => {\n    return data.slice(0, 5).map(row => {\n      const mappedRow = {};\n      Object.keys(mapping).forEach(field => {\n        const headerIndex = mapping[field];\n        mappedRow[field] = row[headerIndex] || '';\n      });\n      return mappedRow;\n    });\n  };\n  const importData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const importData = data.map(row => {\n        const mappedRow = {};\n        Object.keys(mapping).forEach(field => {\n          const headerIndex = mapping[field];\n          let value = row[headerIndex] || '';\n\n          // تحويل البيانات حسب النوع\n          if (['price', 'cost', 'amount'].includes(field)) {\n            value = parseFloat(value) || 0;\n          } else if (['stock_quantity'].includes(field)) {\n            value = parseInt(value) || 0;\n          }\n          mappedRow[field] = value;\n        });\n        return mappedRow;\n      });\n\n      // إرسال البيانات للخادم\n      const response = await axios.post(`http://localhost:5000/api/import/${type}`, {\n        data: importData\n      });\n      setMessage(`تم استيراد ${response.data.imported} عنصر بنجاح`);\n      setStep(4);\n      if (onImportComplete) {\n        onImportComplete();\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      setError('خطأ في استيراد البيانات: ' + (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || error.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDownloadTemplate = () => {\n    downloadTemplate(type);\n  };\n  const getTypeTitle = () => {\n    const titles = {\n      customers: 'العملاء',\n      suppliers: 'الموردين',\n      products: 'المنتجات',\n      expenses: 'المصاريف'\n    };\n    return titles[type] || type;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    style: {\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      zIndex: 1000\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content\",\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '10px',\n        padding: '2rem',\n        maxWidth: '90vw',\n        maxHeight: '90vh',\n        overflow: 'auto',\n        width: '800px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '2rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"\\uD83D\\uDCE5 \\u0627\\u0633\\u062A\\u064A\\u0631\\u0627\\u062F \", getTypeTitle(), \" \\u0645\\u0646 Excel\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"btn btn-danger\",\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"success\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 21\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '2rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            marginBottom: '1rem'\n          },\n          children: ['رفع الملف', 'ربط الحقول', 'معاينة البيانات', 'الاستيراد'].map((stepName, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '0.5rem 1rem',\n              borderRadius: '20px',\n              backgroundColor: step > index ? '#4CAF50' : step === index + 1 ? '#2196F3' : '#f0f0f0',\n              color: step >= index + 1 ? 'white' : '#666',\n              fontSize: '0.9rem'\n            },\n            children: [index + 1, \". \", stepName]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this), step === 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: downloadTemplate,\n            className: \"btn btn-info\",\n            children: \"\\uD83D\\uDCC4 \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0642\\u0627\\u0644\\u0628 Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              marginTop: '1rem',\n              color: '#666'\n            },\n            children: \"\\u064A\\u0645\\u0643\\u0646\\u0643 \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0642\\u0627\\u0644\\u0628 Excel \\u062C\\u0627\\u0647\\u0632 \\u064A\\u062D\\u062A\\u0648\\u064A \\u0639\\u0644\\u0649 \\u0627\\u0644\\u062D\\u0642\\u0648\\u0644 \\u0627\\u0644\\u0645\\u0637\\u0644\\u0648\\u0628\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ...getRootProps(),\n          style: {\n            border: '2px dashed #ccc',\n            borderRadius: '10px',\n            padding: '3rem',\n            textAlign: 'center',\n            cursor: 'pointer',\n            backgroundColor: isDragActive ? '#f0f8ff' : '#fafafa'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            ...getInputProps()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '3rem',\n              marginBottom: '1rem'\n            },\n            children: \"\\uD83D\\uDCC1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this), isDragActive ? /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u0627\\u0633\\u062D\\u0628 \\u0627\\u0644\\u0645\\u0644\\u0641 \\u0647\\u0646\\u0627...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0627\\u0633\\u062D\\u0628 \\u0645\\u0644\\u0641 Excel \\u0647\\u0646\\u0627 \\u0623\\u0648 \\u0627\\u0636\\u063A\\u0637 \\u0644\\u0644\\u0627\\u062E\\u062A\\u064A\\u0627\\u0631\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#666',\n                fontSize: '0.9rem'\n              },\n              children: \"\\u0627\\u0644\\u0635\\u064A\\u063A \\u0627\\u0644\\u0645\\u062F\\u0639\\u0648\\u0645\\u0629: .xlsx, .xls, .csv\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 11\n      }, this), step === 2 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u0631\\u0628\\u0637 \\u062D\\u0642\\u0648\\u0644 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#666',\n            marginBottom: '2rem'\n          },\n          children: \"\\u0627\\u0631\\u0628\\u0637 \\u0643\\u0644 \\u062D\\u0642\\u0644 \\u0641\\u064A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u0628\\u0627\\u0644\\u0639\\u0645\\u0648\\u062F \\u0627\\u0644\\u0645\\u0646\\u0627\\u0633\\u0628 \\u0641\\u064A \\u0645\\u0644\\u0641 Excel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: Object.entries(fieldMappings[type]).map(([field, label]) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: [label, \" *\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-control\",\n                value: mapping[field] || '',\n                onChange: e => handleMappingChange(field, e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0639\\u0645\\u0648\\u062F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 23\n                }, this), headers.map((header, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: index,\n                  children: header\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 19\n            }, this)\n          }, field, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            justifyContent: 'center',\n            marginTop: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setStep(1),\n            className: \"btn btn-secondary\",\n            children: \"\\u0627\\u0644\\u0633\\u0627\\u0628\\u0642\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: previewData,\n            className: \"btn btn-primary\",\n            children: \"\\u0645\\u0639\\u0627\\u064A\\u0646\\u0629 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this), step === 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u0645\\u0639\\u0627\\u064A\\u0646\\u0629 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#666',\n            marginBottom: '2rem'\n          },\n          children: \"\\u0645\\u0639\\u0627\\u064A\\u0646\\u0629 \\u0623\\u0648\\u0644 5 \\u0635\\u0641\\u0648\\u0641 \\u0645\\u0646 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0633\\u062A\\u0648\\u0631\\u062F\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            overflowX: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: Object.values(fieldMappings[type]).map((label, index) => /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: label\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: getPreviewRows().map((row, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: Object.keys(fieldMappings[type]).map((field, fieldIndex) => /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: row[field]\n                }, fieldIndex, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 25\n                }, this))\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '2rem',\n            padding: '1rem',\n            backgroundColor: '#f8f9fa',\n            borderRadius: '5px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [\"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0635\\u0641\\u0648\\u0641: \", data.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            justifyContent: 'center',\n            marginTop: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setStep(2),\n            className: \"btn btn-secondary\",\n            children: \"\\u0627\\u0644\\u0633\\u0627\\u0628\\u0642\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: importData,\n            className: \"btn btn-success\",\n            disabled: loading,\n            children: loading ? 'جاري الاستيراد...' : 'تأكيد الاستيراد'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 11\n      }, this), step === 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '4rem',\n            color: '#4CAF50',\n            marginBottom: '2rem'\n          },\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u062A\\u0645 \\u0627\\u0644\\u0627\\u0633\\u062A\\u064A\\u0631\\u0627\\u062F \\u0628\\u0646\\u062C\\u0627\\u062D!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#666',\n            marginBottom: '2rem'\n          },\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"btn btn-primary\",\n          children: \"\\u0625\\u063A\\u0644\\u0627\\u0642\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 193,\n    columnNumber: 5\n  }, this);\n};\n_s(ImportExcel, \"H18vZ4yJBm7p7yUeqIS4D9i47iU=\", false, function () {\n  return [useDropzone];\n});\n_c = ImportExcel;\nexport default ImportExcel;\nvar _c;\n$RefreshReg$(_c, \"ImportExcel\");", "map": {"version": 3, "names": ["React", "useState", "useDropzone", "XLSX", "axios", "downloadTemplate", "jsxDEV", "_jsxDEV", "ImportExcel", "type", "onClose", "onImportComplete", "_s", "file", "setFile", "data", "setData", "headers", "setHeaders", "mapping", "setMapping", "loading", "setLoading", "step", "setStep", "message", "setMessage", "error", "setError", "fieldMappings", "customers", "name", "phone", "email", "address", "tax_number", "suppliers", "products", "description", "unit", "price", "cost", "stock_quantity", "expenses", "category", "amount", "expense_date", "payment_method", "receipt_number", "onDrop", "acceptedFiles", "readExcelFile", "getRootProps", "getInputProps", "isDragActive", "accept", "multiple", "reader", "FileReader", "onload", "e", "Uint8Array", "target", "result", "workbook", "read", "sheetName", "SheetNames", "worksheet", "Sheets", "jsonData", "utils", "sheet_to_json", "header", "length", "rows", "slice", "filter", "row", "some", "cell", "undefined", "readAsA<PERSON>y<PERSON><PERSON>er", "handleMappingChange", "field", "headerIndex", "prev", "validateMapping", "requiredFields", "Object", "keys", "<PERSON><PERSON><PERSON>s", "missingFields", "includes", "map", "f", "join", "previewData", "getPreviewRows", "mappedRow", "for<PERSON>ach", "importData", "value", "parseFloat", "parseInt", "response", "post", "imported", "_error$response", "_error$response$data", "handleDownloadTemplate", "getTypeTitle", "titles", "className", "style", "position", "top", "left", "right", "bottom", "backgroundColor", "display", "justifyContent", "alignItems", "zIndex", "children", "borderRadius", "padding", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "width", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "<PERSON><PERSON><PERSON>", "index", "color", "fontSize", "marginTop", "border", "textAlign", "cursor", "entries", "label", "onChange", "gap", "overflowX", "values", "fieldIndex", "disabled", "_c", "$RefreshReg$"], "sources": ["F:/القيمة المضافة/accounting-system/src/components/ImportExcel.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport * as XLSX from 'xlsx';\nimport axios from 'axios';\nimport { downloadTemplate } from './TemplateGenerator';\n\nconst ImportExcel = ({ type, onClose, onImportComplete }) => {\n  const [file, setFile] = useState(null);\n  const [data, setData] = useState([]);\n  const [headers, setHeaders] = useState([]);\n  const [mapping, setMapping] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [step, setStep] = useState(1); // 1: Upload, 2: Map, 3: Preview, 4: Import\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n\n  // تعريف الحقول المطلوبة لكل نوع\n  const fieldMappings = {\n    customers: {\n      name: 'اسم العميل',\n      phone: 'رقم الهاتف',\n      email: 'ال<PERSON>ريد الإلكتروني',\n      address: 'العنوان',\n      tax_number: 'الرقم الضريبي'\n    },\n    suppliers: {\n      name: 'اسم المورد',\n      phone: 'رقم الهاتف',\n      email: 'البريد الإلكتروني',\n      address: 'العنوان',\n      tax_number: 'الرقم الضريبي'\n    },\n    products: {\n      name: 'اسم المنتج',\n      description: 'الوصف',\n      unit: 'الوحدة',\n      price: 'سعر البيع',\n      cost: 'سعر التكلفة',\n      stock_quantity: 'الكمية المتاحة'\n    },\n    expenses: {\n      category: 'فئة المصروف',\n      description: 'الوصف',\n      amount: 'المبلغ',\n      expense_date: 'تاريخ المصروف',\n      payment_method: 'طريقة الدفع',\n      receipt_number: 'رقم الإيصال'\n    }\n  };\n\n  const onDrop = (acceptedFiles) => {\n    const file = acceptedFiles[0];\n    if (file) {\n      setFile(file);\n      readExcelFile(file);\n    }\n  };\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],\n      'application/vnd.ms-excel': ['.xls'],\n      'text/csv': ['.csv']\n    },\n    multiple: false\n  });\n\n  const readExcelFile = (file) => {\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      try {\n        const data = new Uint8Array(e.target.result);\n        const workbook = XLSX.read(data, { type: 'array' });\n        const sheetName = workbook.SheetNames[0];\n        const worksheet = workbook.Sheets[sheetName];\n        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });\n        \n        if (jsonData.length > 0) {\n          const headers = jsonData[0];\n          const rows = jsonData.slice(1).filter(row => row.some(cell => cell !== undefined && cell !== ''));\n          \n          setHeaders(headers);\n          setData(rows);\n          setStep(2);\n        } else {\n          setError('الملف فارغ أو لا يحتوي على بيانات صالحة');\n        }\n      } catch (error) {\n        setError('خطأ في قراءة الملف: ' + error.message);\n      }\n    };\n    reader.readAsArrayBuffer(file);\n  };\n\n  const handleMappingChange = (field, headerIndex) => {\n    setMapping(prev => ({\n      ...prev,\n      [field]: headerIndex\n    }));\n  };\n\n  const validateMapping = () => {\n    const requiredFields = Object.keys(fieldMappings[type]);\n    const mappedFields = Object.keys(mapping);\n    \n    // التحقق من وجود الحقول المطلوبة\n    const missingFields = requiredFields.filter(field => \n      !mappedFields.includes(field) || mapping[field] === ''\n    );\n    \n    if (missingFields.length > 0) {\n      setError(`الحقول التالية مطلوبة: ${missingFields.map(f => fieldMappings[type][f]).join(', ')}`);\n      return false;\n    }\n    \n    return true;\n  };\n\n  const previewData = () => {\n    if (!validateMapping()) return;\n    \n    setStep(3);\n  };\n\n  const getPreviewRows = () => {\n    return data.slice(0, 5).map(row => {\n      const mappedRow = {};\n      Object.keys(mapping).forEach(field => {\n        const headerIndex = mapping[field];\n        mappedRow[field] = row[headerIndex] || '';\n      });\n      return mappedRow;\n    });\n  };\n\n  const importData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      \n      const importData = data.map(row => {\n        const mappedRow = {};\n        Object.keys(mapping).forEach(field => {\n          const headerIndex = mapping[field];\n          let value = row[headerIndex] || '';\n          \n          // تحويل البيانات حسب النوع\n          if (['price', 'cost', 'amount'].includes(field)) {\n            value = parseFloat(value) || 0;\n          } else if (['stock_quantity'].includes(field)) {\n            value = parseInt(value) || 0;\n          }\n          \n          mappedRow[field] = value;\n        });\n        return mappedRow;\n      });\n\n      // إرسال البيانات للخادم\n      const response = await axios.post(`http://localhost:5000/api/import/${type}`, {\n        data: importData\n      });\n\n      setMessage(`تم استيراد ${response.data.imported} عنصر بنجاح`);\n      setStep(4);\n      \n      if (onImportComplete) {\n        onImportComplete();\n      }\n    } catch (error) {\n      setError('خطأ في استيراد البيانات: ' + (error.response?.data?.error || error.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDownloadTemplate = () => {\n    downloadTemplate(type);\n  };\n\n  const getTypeTitle = () => {\n    const titles = {\n      customers: 'العملاء',\n      suppliers: 'الموردين',\n      products: 'المنتجات',\n      expenses: 'المصاريف'\n    };\n    return titles[type] || type;\n  };\n\n  return (\n    <div className=\"modal-overlay\" style={{\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      zIndex: 1000\n    }}>\n      <div className=\"modal-content\" style={{\n        backgroundColor: 'white',\n        borderRadius: '10px',\n        padding: '2rem',\n        maxWidth: '90vw',\n        maxHeight: '90vh',\n        overflow: 'auto',\n        width: '800px'\n      }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>\n          <h2>📥 استيراد {getTypeTitle()} من Excel</h2>\n          <button onClick={onClose} className=\"btn btn-danger\">✕</button>\n        </div>\n\n        {message && <div className=\"success\">{message}</div>}\n        {error && <div className=\"error\">{error}</div>}\n\n        {/* مؤشر التقدم */}\n        <div style={{ marginBottom: '2rem' }}>\n          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '1rem' }}>\n            {['رفع الملف', 'ربط الحقول', 'معاينة البيانات', 'الاستيراد'].map((stepName, index) => (\n              <div key={index} style={{\n                padding: '0.5rem 1rem',\n                borderRadius: '20px',\n                backgroundColor: step > index ? '#4CAF50' : step === index + 1 ? '#2196F3' : '#f0f0f0',\n                color: step >= index + 1 ? 'white' : '#666',\n                fontSize: '0.9rem'\n              }}>\n                {index + 1}. {stepName}\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* الخطوة 1: رفع الملف */}\n        {step === 1 && (\n          <div>\n            <div style={{ marginBottom: '2rem' }}>\n              <button onClick={downloadTemplate} className=\"btn btn-info\">\n                📄 تحميل قالب Excel\n              </button>\n              <p style={{ marginTop: '1rem', color: '#666' }}>\n                يمكنك تحميل قالب Excel جاهز يحتوي على الحقول المطلوبة\n              </p>\n            </div>\n\n            <div\n              {...getRootProps()}\n              style={{\n                border: '2px dashed #ccc',\n                borderRadius: '10px',\n                padding: '3rem',\n                textAlign: 'center',\n                cursor: 'pointer',\n                backgroundColor: isDragActive ? '#f0f8ff' : '#fafafa'\n              }}\n            >\n              <input {...getInputProps()} />\n              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📁</div>\n              {isDragActive ? (\n                <p>اسحب الملف هنا...</p>\n              ) : (\n                <div>\n                  <p>اسحب ملف Excel هنا أو اضغط للاختيار</p>\n                  <p style={{ color: '#666', fontSize: '0.9rem' }}>\n                    الصيغ المدعومة: .xlsx, .xls, .csv\n                  </p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* الخطوة 2: ربط الحقول */}\n        {step === 2 && (\n          <div>\n            <h3>ربط حقول البيانات</h3>\n            <p style={{ color: '#666', marginBottom: '2rem' }}>\n              اربط كل حقل في النظام بالعمود المناسب في ملف Excel\n            </p>\n\n            <div className=\"row\">\n              {Object.entries(fieldMappings[type]).map(([field, label]) => (\n                <div key={field} className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">{label} *</label>\n                    <select\n                      className=\"form-control\"\n                      value={mapping[field] || ''}\n                      onChange={(e) => handleMappingChange(field, e.target.value)}\n                    >\n                      <option value=\"\">اختر العمود</option>\n                      {headers.map((header, index) => (\n                        <option key={index} value={index}>{header}</option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', marginTop: '2rem' }}>\n              <button onClick={() => setStep(1)} className=\"btn btn-secondary\">\n                السابق\n              </button>\n              <button onClick={previewData} className=\"btn btn-primary\">\n                معاينة البيانات\n              </button>\n            </div>\n          </div>\n        )}\n\n        {/* الخطوة 3: معاينة البيانات */}\n        {step === 3 && (\n          <div>\n            <h3>معاينة البيانات</h3>\n            <p style={{ color: '#666', marginBottom: '2rem' }}>\n              معاينة أول 5 صفوف من البيانات المستوردة\n            </p>\n\n            <div style={{ overflowX: 'auto' }}>\n              <table className=\"table\">\n                <thead>\n                  <tr>\n                    {Object.values(fieldMappings[type]).map((label, index) => (\n                      <th key={index}>{label}</th>\n                    ))}\n                  </tr>\n                </thead>\n                <tbody>\n                  {getPreviewRows().map((row, index) => (\n                    <tr key={index}>\n                      {Object.keys(fieldMappings[type]).map((field, fieldIndex) => (\n                        <td key={fieldIndex}>{row[field]}</td>\n                      ))}\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n\n            <div style={{ marginTop: '2rem', padding: '1rem', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>\n              <strong>إجمالي الصفوف: {data.length}</strong>\n            </div>\n\n            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', marginTop: '2rem' }}>\n              <button onClick={() => setStep(2)} className=\"btn btn-secondary\">\n                السابق\n              </button>\n              <button onClick={importData} className=\"btn btn-success\" disabled={loading}>\n                {loading ? 'جاري الاستيراد...' : 'تأكيد الاستيراد'}\n              </button>\n            </div>\n          </div>\n        )}\n\n        {/* الخطوة 4: اكتمال الاستيراد */}\n        {step === 4 && (\n          <div style={{ textAlign: 'center' }}>\n            <div style={{ fontSize: '4rem', color: '#4CAF50', marginBottom: '2rem' }}>✅</div>\n            <h3>تم الاستيراد بنجاح!</h3>\n            <p style={{ color: '#666', marginBottom: '2rem' }}>{message}</p>\n            \n            <button onClick={onClose} className=\"btn btn-primary\">\n              إغلاق\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ImportExcel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,gBAAgB,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACc,IAAI,EAAEC,OAAO,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsB,IAAI,EAAEC,OAAO,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAM4B,aAAa,GAAG;IACpBC,SAAS,EAAE;MACTC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAE,SAAS;MAClBC,UAAU,EAAE;IACd,CAAC;IACDC,SAAS,EAAE;MACTL,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAE,SAAS;MAClBC,UAAU,EAAE;IACd,CAAC;IACDE,QAAQ,EAAE;MACRN,IAAI,EAAE,YAAY;MAClBO,WAAW,EAAE,OAAO;MACpBC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,WAAW;MAClBC,IAAI,EAAE,aAAa;MACnBC,cAAc,EAAE;IAClB,CAAC;IACDC,QAAQ,EAAE;MACRC,QAAQ,EAAE,aAAa;MACvBN,WAAW,EAAE,OAAO;MACpBO,MAAM,EAAE,QAAQ;MAChBC,YAAY,EAAE,eAAe;MAC7BC,cAAc,EAAE,aAAa;MAC7BC,cAAc,EAAE;IAClB;EACF,CAAC;EAED,MAAMC,MAAM,GAAIC,aAAa,IAAK;IAChC,MAAMrC,IAAI,GAAGqC,aAAa,CAAC,CAAC,CAAC;IAC7B,IAAIrC,IAAI,EAAE;MACRC,OAAO,CAACD,IAAI,CAAC;MACbsC,aAAa,CAACtC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAM;IAAEuC,YAAY;IAAEC,aAAa;IAAEC;EAAa,CAAC,GAAGpD,WAAW,CAAC;IAChE+C,MAAM;IACNM,MAAM,EAAE;MACN,mEAAmE,EAAE,CAAC,OAAO,CAAC;MAC9E,0BAA0B,EAAE,CAAC,MAAM,CAAC;MACpC,UAAU,EAAE,CAAC,MAAM;IACrB,CAAC;IACDC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAML,aAAa,GAAItC,IAAI,IAAK;IAC9B,MAAM4C,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MACrB,IAAI;QACF,MAAM7C,IAAI,GAAG,IAAI8C,UAAU,CAACD,CAAC,CAACE,MAAM,CAACC,MAAM,CAAC;QAC5C,MAAMC,QAAQ,GAAG7D,IAAI,CAAC8D,IAAI,CAAClD,IAAI,EAAE;UAAEN,IAAI,EAAE;QAAQ,CAAC,CAAC;QACnD,MAAMyD,SAAS,GAAGF,QAAQ,CAACG,UAAU,CAAC,CAAC,CAAC;QACxC,MAAMC,SAAS,GAAGJ,QAAQ,CAACK,MAAM,CAACH,SAAS,CAAC;QAC5C,MAAMI,QAAQ,GAAGnE,IAAI,CAACoE,KAAK,CAACC,aAAa,CAACJ,SAAS,EAAE;UAAEK,MAAM,EAAE;QAAE,CAAC,CAAC;QAEnE,IAAIH,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;UACvB,MAAMzD,OAAO,GAAGqD,QAAQ,CAAC,CAAC,CAAC;UAC3B,MAAMK,IAAI,GAAGL,QAAQ,CAACM,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,KAAKC,SAAS,IAAID,IAAI,KAAK,EAAE,CAAC,CAAC;UAEjG9D,UAAU,CAACD,OAAO,CAAC;UACnBD,OAAO,CAAC2D,IAAI,CAAC;UACbnD,OAAO,CAAC,CAAC,CAAC;QACZ,CAAC,MAAM;UACLI,QAAQ,CAAC,yCAAyC,CAAC;QACrD;MACF,CAAC,CAAC,OAAOD,KAAK,EAAE;QACdC,QAAQ,CAAC,sBAAsB,GAAGD,KAAK,CAACF,OAAO,CAAC;MAClD;IACF,CAAC;IACDgC,MAAM,CAACyB,iBAAiB,CAACrE,IAAI,CAAC;EAChC,CAAC;EAED,MAAMsE,mBAAmB,GAAGA,CAACC,KAAK,EAAEC,WAAW,KAAK;IAClDjE,UAAU,CAACkE,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,cAAc,GAAGC,MAAM,CAACC,IAAI,CAAC7D,aAAa,CAACpB,IAAI,CAAC,CAAC;IACvD,MAAMkF,YAAY,GAAGF,MAAM,CAACC,IAAI,CAACvE,OAAO,CAAC;;IAEzC;IACA,MAAMyE,aAAa,GAAGJ,cAAc,CAACX,MAAM,CAACO,KAAK,IAC/C,CAACO,YAAY,CAACE,QAAQ,CAACT,KAAK,CAAC,IAAIjE,OAAO,CAACiE,KAAK,CAAC,KAAK,EACtD,CAAC;IAED,IAAIQ,aAAa,CAAClB,MAAM,GAAG,CAAC,EAAE;MAC5B9C,QAAQ,CAAC,0BAA0BgE,aAAa,CAACE,GAAG,CAACC,CAAC,IAAIlE,aAAa,CAACpB,IAAI,CAAC,CAACsF,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAC/F,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACV,eAAe,CAAC,CAAC,EAAE;IAExB/D,OAAO,CAAC,CAAC,CAAC;EACZ,CAAC;EAED,MAAM0E,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAOnF,IAAI,CAAC6D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACkB,GAAG,CAAChB,GAAG,IAAI;MACjC,MAAMqB,SAAS,GAAG,CAAC,CAAC;MACpBV,MAAM,CAACC,IAAI,CAACvE,OAAO,CAAC,CAACiF,OAAO,CAAChB,KAAK,IAAI;QACpC,MAAMC,WAAW,GAAGlE,OAAO,CAACiE,KAAK,CAAC;QAClCe,SAAS,CAACf,KAAK,CAAC,GAAGN,GAAG,CAACO,WAAW,CAAC,IAAI,EAAE;MAC3C,CAAC,CAAC;MACF,OAAOc,SAAS;IAClB,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF/E,UAAU,CAAC,IAAI,CAAC;MAChBM,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMyE,UAAU,GAAGtF,IAAI,CAAC+E,GAAG,CAAChB,GAAG,IAAI;QACjC,MAAMqB,SAAS,GAAG,CAAC,CAAC;QACpBV,MAAM,CAACC,IAAI,CAACvE,OAAO,CAAC,CAACiF,OAAO,CAAChB,KAAK,IAAI;UACpC,MAAMC,WAAW,GAAGlE,OAAO,CAACiE,KAAK,CAAC;UAClC,IAAIkB,KAAK,GAAGxB,GAAG,CAACO,WAAW,CAAC,IAAI,EAAE;;UAElC;UACA,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAACQ,QAAQ,CAACT,KAAK,CAAC,EAAE;YAC/CkB,KAAK,GAAGC,UAAU,CAACD,KAAK,CAAC,IAAI,CAAC;UAChC,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,CAACT,QAAQ,CAACT,KAAK,CAAC,EAAE;YAC7CkB,KAAK,GAAGE,QAAQ,CAACF,KAAK,CAAC,IAAI,CAAC;UAC9B;UAEAH,SAAS,CAACf,KAAK,CAAC,GAAGkB,KAAK;QAC1B,CAAC,CAAC;QACF,OAAOH,SAAS;MAClB,CAAC,CAAC;;MAEF;MACA,MAAMM,QAAQ,GAAG,MAAMrG,KAAK,CAACsG,IAAI,CAAC,oCAAoCjG,IAAI,EAAE,EAAE;QAC5EM,IAAI,EAAEsF;MACR,CAAC,CAAC;MAEF3E,UAAU,CAAC,cAAc+E,QAAQ,CAAC1F,IAAI,CAAC4F,QAAQ,aAAa,CAAC;MAC7DnF,OAAO,CAAC,CAAC,CAAC;MAEV,IAAIb,gBAAgB,EAAE;QACpBA,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC,OAAOgB,KAAK,EAAE;MAAA,IAAAiF,eAAA,EAAAC,oBAAA;MACdjF,QAAQ,CAAC,2BAA2B,IAAI,EAAAgF,eAAA,GAAAjF,KAAK,CAAC8E,QAAQ,cAAAG,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB7F,IAAI,cAAA8F,oBAAA,uBAApBA,oBAAA,CAAsBlF,KAAK,KAAIA,KAAK,CAACF,OAAO,CAAC,CAAC;IACxF,CAAC,SAAS;MACRH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwF,sBAAsB,GAAGA,CAAA,KAAM;IACnCzG,gBAAgB,CAACI,IAAI,CAAC;EACxB,CAAC;EAED,MAAMsG,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG;MACblF,SAAS,EAAE,SAAS;MACpBM,SAAS,EAAE,UAAU;MACrBC,QAAQ,EAAE,UAAU;MACpBM,QAAQ,EAAE;IACZ,CAAC;IACD,OAAOqE,MAAM,CAACvG,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;EAED,oBACEF,OAAA;IAAK0G,SAAS,EAAC,eAAe;IAACC,KAAK,EAAE;MACpCC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,eAAe,EAAE,iBAAiB;MAClCC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,MAAM,EAAE;IACV,CAAE;IAAAC,QAAA,eACAtH,OAAA;MAAK0G,SAAS,EAAC,eAAe;MAACC,KAAK,EAAE;QACpCM,eAAe,EAAE,OAAO;QACxBM,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfC,QAAQ,EAAE,MAAM;QAChBC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE;MACT,CAAE;MAAAN,QAAA,gBACAtH,OAAA;QAAK2G,KAAK,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAES,YAAY,EAAE;QAAO,CAAE;QAAAP,QAAA,gBAC3GtH,OAAA;UAAAsH,QAAA,GAAI,0DAAW,EAACd,YAAY,CAAC,CAAC,EAAC,qBAAS;QAAA;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7CjI,OAAA;UAAQkI,OAAO,EAAE/H,OAAQ;UAACuG,SAAS,EAAC,gBAAgB;UAAAY,QAAA,EAAC;QAAC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,EAEL/G,OAAO,iBAAIlB,OAAA;QAAK0G,SAAS,EAAC,SAAS;QAAAY,QAAA,EAAEpG;MAAO;QAAA4G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EACnD7G,KAAK,iBAAIpB,OAAA;QAAK0G,SAAS,EAAC,OAAO;QAAAY,QAAA,EAAElG;MAAK;QAAA0G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAG9CjI,OAAA;QAAK2G,KAAK,EAAE;UAAEkB,YAAY,EAAE;QAAO,CAAE;QAAAP,QAAA,eACnCtH,OAAA;UAAK2G,KAAK,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEU,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,EACpF,CAAC,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,WAAW,CAAC,CAAC/B,GAAG,CAAC,CAAC4C,QAAQ,EAAEC,KAAK,kBAC/EpI,OAAA;YAAiB2G,KAAK,EAAE;cACtBa,OAAO,EAAE,aAAa;cACtBD,YAAY,EAAE,MAAM;cACpBN,eAAe,EAAEjG,IAAI,GAAGoH,KAAK,GAAG,SAAS,GAAGpH,IAAI,KAAKoH,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;cACtFC,KAAK,EAAErH,IAAI,IAAIoH,KAAK,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM;cAC3CE,QAAQ,EAAE;YACZ,CAAE;YAAAhB,QAAA,GACCc,KAAK,GAAG,CAAC,EAAC,IAAE,EAACD,QAAQ;UAAA,GAPdC,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLjH,IAAI,KAAK,CAAC,iBACThB,OAAA;QAAAsH,QAAA,gBACEtH,OAAA;UAAK2G,KAAK,EAAE;YAAEkB,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBACnCtH,OAAA;YAAQkI,OAAO,EAAEpI,gBAAiB;YAAC4G,SAAS,EAAC,cAAc;YAAAY,QAAA,EAAC;UAE5D;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjI,OAAA;YAAG2G,KAAK,EAAE;cAAE4B,SAAS,EAAE,MAAM;cAAEF,KAAK,EAAE;YAAO,CAAE;YAAAf,QAAA,EAAC;UAEhD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENjI,OAAA;UAAA,GACM6C,YAAY,CAAC,CAAC;UAClB8D,KAAK,EAAE;YACL6B,MAAM,EAAE,iBAAiB;YACzBjB,YAAY,EAAE,MAAM;YACpBC,OAAO,EAAE,MAAM;YACfiB,SAAS,EAAE,QAAQ;YACnBC,MAAM,EAAE,SAAS;YACjBzB,eAAe,EAAElE,YAAY,GAAG,SAAS,GAAG;UAC9C,CAAE;UAAAuE,QAAA,gBAEFtH,OAAA;YAAA,GAAW8C,aAAa,CAAC;UAAC;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9BjI,OAAA;YAAK2G,KAAK,EAAE;cAAE2B,QAAQ,EAAE,MAAM;cAAET,YAAY,EAAE;YAAO,CAAE;YAAAP,QAAA,EAAC;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EAC/DlF,YAAY,gBACX/C,OAAA;YAAAsH,QAAA,EAAG;UAAiB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,gBAExBjI,OAAA;YAAAsH,QAAA,gBACEtH,OAAA;cAAAsH,QAAA,EAAG;YAAmC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1CjI,OAAA;cAAG2G,KAAK,EAAE;gBAAE0B,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAS,CAAE;cAAAhB,QAAA,EAAC;YAEjD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAjH,IAAI,KAAK,CAAC,iBACThB,OAAA;QAAAsH,QAAA,gBACEtH,OAAA;UAAAsH,QAAA,EAAI;QAAiB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BjI,OAAA;UAAG2G,KAAK,EAAE;YAAE0B,KAAK,EAAE,MAAM;YAAER,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,EAAC;QAEnD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJjI,OAAA;UAAK0G,SAAS,EAAC,KAAK;UAAAY,QAAA,EACjBpC,MAAM,CAACyD,OAAO,CAACrH,aAAa,CAACpB,IAAI,CAAC,CAAC,CAACqF,GAAG,CAAC,CAAC,CAACV,KAAK,EAAE+D,KAAK,CAAC,kBACtD5I,OAAA;YAAiB0G,SAAS,EAAC,UAAU;YAAAY,QAAA,eACnCtH,OAAA;cAAK0G,SAAS,EAAC,YAAY;cAAAY,QAAA,gBACzBtH,OAAA;gBAAO0G,SAAS,EAAC,YAAY;gBAAAY,QAAA,GAAEsB,KAAK,EAAC,IAAE;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/CjI,OAAA;gBACE0G,SAAS,EAAC,cAAc;gBACxBX,KAAK,EAAEnF,OAAO,CAACiE,KAAK,CAAC,IAAI,EAAG;gBAC5BgE,QAAQ,EAAGxF,CAAC,IAAKuB,mBAAmB,CAACC,KAAK,EAAExB,CAAC,CAACE,MAAM,CAACwC,KAAK,CAAE;gBAAAuB,QAAA,gBAE5DtH,OAAA;kBAAQ+F,KAAK,EAAC,EAAE;kBAAAuB,QAAA,EAAC;gBAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACpCvH,OAAO,CAAC6E,GAAG,CAAC,CAACrB,MAAM,EAAEkE,KAAK,kBACzBpI,OAAA;kBAAoB+F,KAAK,EAAEqC,KAAM;kBAAAd,QAAA,EAAEpD;gBAAM,GAA5BkE,KAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAgC,CACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC,GAbEpD,KAAK;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENjI,OAAA;UAAK2G,KAAK,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAE4B,GAAG,EAAE,MAAM;YAAE3B,cAAc,EAAE,QAAQ;YAAEoB,SAAS,EAAE;UAAO,CAAE;UAAAjB,QAAA,gBACxFtH,OAAA;YAAQkI,OAAO,EAAEA,CAAA,KAAMjH,OAAO,CAAC,CAAC,CAAE;YAACyF,SAAS,EAAC,mBAAmB;YAAAY,QAAA,EAAC;UAEjE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjI,OAAA;YAAQkI,OAAO,EAAExC,WAAY;YAACgB,SAAS,EAAC,iBAAiB;YAAAY,QAAA,EAAC;UAE1D;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAjH,IAAI,KAAK,CAAC,iBACThB,OAAA;QAAAsH,QAAA,gBACEtH,OAAA;UAAAsH,QAAA,EAAI;QAAe;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBjI,OAAA;UAAG2G,KAAK,EAAE;YAAE0B,KAAK,EAAE,MAAM;YAAER,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,EAAC;QAEnD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJjI,OAAA;UAAK2G,KAAK,EAAE;YAAEoC,SAAS,EAAE;UAAO,CAAE;UAAAzB,QAAA,eAChCtH,OAAA;YAAO0G,SAAS,EAAC,OAAO;YAAAY,QAAA,gBACtBtH,OAAA;cAAAsH,QAAA,eACEtH,OAAA;gBAAAsH,QAAA,EACGpC,MAAM,CAAC8D,MAAM,CAAC1H,aAAa,CAACpB,IAAI,CAAC,CAAC,CAACqF,GAAG,CAAC,CAACqD,KAAK,EAAER,KAAK,kBACnDpI,OAAA;kBAAAsH,QAAA,EAAiBsB;gBAAK,GAAbR,KAAK;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAC5B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRjI,OAAA;cAAAsH,QAAA,EACG3B,cAAc,CAAC,CAAC,CAACJ,GAAG,CAAC,CAAChB,GAAG,EAAE6D,KAAK,kBAC/BpI,OAAA;gBAAAsH,QAAA,EACGpC,MAAM,CAACC,IAAI,CAAC7D,aAAa,CAACpB,IAAI,CAAC,CAAC,CAACqF,GAAG,CAAC,CAACV,KAAK,EAAEoE,UAAU,kBACtDjJ,OAAA;kBAAAsH,QAAA,EAAsB/C,GAAG,CAACM,KAAK;gBAAC,GAAvBoE,UAAU;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAkB,CACtC;cAAC,GAHKG,KAAK;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENjI,OAAA;UAAK2G,KAAK,EAAE;YAAE4B,SAAS,EAAE,MAAM;YAAEf,OAAO,EAAE,MAAM;YAAEP,eAAe,EAAE,SAAS;YAAEM,YAAY,EAAE;UAAM,CAAE;UAAAD,QAAA,eAClGtH,OAAA;YAAAsH,QAAA,GAAQ,6EAAe,EAAC9G,IAAI,CAAC2D,MAAM;UAAA;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eAENjI,OAAA;UAAK2G,KAAK,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAE4B,GAAG,EAAE,MAAM;YAAE3B,cAAc,EAAE,QAAQ;YAAEoB,SAAS,EAAE;UAAO,CAAE;UAAAjB,QAAA,gBACxFtH,OAAA;YAAQkI,OAAO,EAAEA,CAAA,KAAMjH,OAAO,CAAC,CAAC,CAAE;YAACyF,SAAS,EAAC,mBAAmB;YAAAY,QAAA,EAAC;UAEjE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjI,OAAA;YAAQkI,OAAO,EAAEpC,UAAW;YAACY,SAAS,EAAC,iBAAiB;YAACwC,QAAQ,EAAEpI,OAAQ;YAAAwG,QAAA,EACxExG,OAAO,GAAG,mBAAmB,GAAG;UAAiB;YAAAgH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAjH,IAAI,KAAK,CAAC,iBACThB,OAAA;QAAK2G,KAAK,EAAE;UAAE8B,SAAS,EAAE;QAAS,CAAE;QAAAnB,QAAA,gBAClCtH,OAAA;UAAK2G,KAAK,EAAE;YAAE2B,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE,SAAS;YAAER,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,EAAC;QAAC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjFjI,OAAA;UAAAsH,QAAA,EAAI;QAAmB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BjI,OAAA;UAAG2G,KAAK,EAAE;YAAE0B,KAAK,EAAE,MAAM;YAAER,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,EAAEpG;QAAO;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEhEjI,OAAA;UAAQkI,OAAO,EAAE/H,OAAQ;UAACuG,SAAS,EAAC,iBAAiB;UAAAY,QAAA,EAAC;QAEtD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5H,EAAA,CAjXIJ,WAAW;EAAA,QAoDuCN,WAAW;AAAA;AAAAwJ,EAAA,GApD7DlJ,WAAW;AAmXjB,eAAeA,WAAW;AAAC,IAAAkJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}