import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const Navbar = () => {
  const location = useLocation();

  const isActive = (path) => {
    return location.pathname === path ? 'nav-link active' : 'nav-link';
  };

  return (
    <nav className="navbar">
      <div className="navbar-brand">
        💼 نظام المحاسبة المتكامل
      </div>
      <ul className="navbar-nav">
        <li>
          <Link to="/" className={isActive('/')}>
            🏠 الرئيسية
          </Link>
        </li>
        <li>
          <Link to="/customers" className={isActive('/customers')}>
            👥 العملاء
          </Link>
        </li>
        <li>
          <Link to="/suppliers" className={isActive('/suppliers')}>
            🏭 الموردين
          </Link>
        </li>
        <li>
          <Link to="/products" className={isActive('/products')}>
            📦 المنتجات
          </Link>
        </li>
        <li>
          <Link to="/sales-invoices" className={isActive('/sales-invoices')}>
            📄 فواتير المبيعات
          </Link>
        </li>
        <li>
          <Link to="/purchase-invoices" className={isActive('/purchase-invoices')}>
            📋 فواتير المشتريات
          </Link>
        </li>
        <li>
          <Link to="/treasury" className={isActive('/treasury')}>
            💰 الخزينة
          </Link>
        </li>
        <li>
          <Link to="/expenses" className={isActive('/expenses')}>
            💸 المصاريف
          </Link>
        </li>
        <li>
          <Link to="/inventory" className={isActive('/inventory')}>
            📦 المخزون
          </Link>
        </li>
        <li>
          <Link to="/backup" className={isActive('/backup')}>
            💾 النسخ الاحتياطي
          </Link>
        </li>
        <li>
          <Link to="/reports" className={isActive('/reports')}>
            📊 التقارير
          </Link>
        </li>
      </ul>
    </nav>
  );
};

export default Navbar;
