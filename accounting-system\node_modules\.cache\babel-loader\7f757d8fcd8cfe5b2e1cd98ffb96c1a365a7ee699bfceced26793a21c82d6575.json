{"ast": null, "code": "var _jsxFileName = \"F:\\\\\\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\\\\accounting-system\\\\src\\\\components\\\\Expenses.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Expenses = () => {\n  _s();\n  const [expenses, setExpenses] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showForm, setShowForm] = useState(false);\n  const [editingExpense, setEditingExpense] = useState(null);\n  const [formData, setFormData] = useState({\n    category: '',\n    description: '',\n    amount: '',\n    expense_date: '',\n    payment_method: '',\n    receipt_number: '',\n    notes: ''\n  });\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n  const expenseCategories = ['مصاريف إدارية', 'مصاريف تشغيلية', 'مصاريف تسويق', 'مصاريف صيانة', 'مصاريف مواصلات', 'مصاريف اتصالات', 'مصاريف كهرباء', 'مصاريف إيجار', 'مصاريف أخرى'];\n  useEffect(() => {\n    fetchExpenses();\n  }, []);\n  const fetchExpenses = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/expenses');\n      setExpenses(response.data);\n    } catch (error) {\n      setError('خطأ في جلب بيانات المصاريف');\n      console.error('Error fetching expenses:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      const expenseData = {\n        ...formData,\n        amount: parseFloat(formData.amount) || 0\n      };\n      if (editingExpense) {\n        await axios.put(`http://localhost:5000/api/expenses/${editingExpense.id}`, expenseData);\n        setMessage('تم تحديث المصروف بنجاح');\n      } else {\n        await axios.post('http://localhost:5000/api/expenses', expenseData);\n        setMessage('تم إضافة المصروف بنجاح');\n      }\n      setFormData({\n        category: '',\n        description: '',\n        amount: '',\n        expense_date: '',\n        payment_method: '',\n        receipt_number: '',\n        notes: ''\n      });\n      setShowForm(false);\n      setEditingExpense(null);\n      fetchExpenses();\n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      setError('خطأ في حفظ بيانات المصروف');\n      console.error('Error saving expense:', error);\n    }\n  };\n  const handleEdit = expense => {\n    var _expense$amount;\n    setEditingExpense(expense);\n    setFormData({\n      category: expense.category,\n      description: expense.description,\n      amount: ((_expense$amount = expense.amount) === null || _expense$amount === void 0 ? void 0 : _expense$amount.toString()) || '',\n      expense_date: expense.expense_date,\n      payment_method: expense.payment_method || '',\n      receipt_number: expense.receipt_number || '',\n      notes: expense.notes || ''\n    });\n    setShowForm(true);\n  };\n  const resetForm = () => {\n    setFormData({\n      category: '',\n      description: '',\n      amount: '',\n      expense_date: '',\n      payment_method: '',\n      receipt_number: '',\n      notes: ''\n    });\n    setShowForm(false);\n    setEditingExpense(null);\n  };\n  const getTotalExpenses = () => {\n    return expenses.reduce((total, expense) => total + (expense.amount || 0), 0);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0635\\u0627\\u0631\\u064A\\u0641...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"page-title\",\n      children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u0635\\u0627\\u0631\\u064A\\u0641\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 19\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          style: {\n            background: 'linear-gradient(135deg, #f44336 0%, #da190b 100%)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-number\",\n            children: getTotalExpenses().toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0635\\u0627\\u0631\\u064A\\u0641 (\\u062C.\\u0645)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-number\",\n            children: expenses.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0645\\u0635\\u0627\\u0631\\u064A\\u0641\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          style: {\n            background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-number\",\n            children: expenses.length > 0 ? (getTotalExpenses() / expenses.length).toFixed(0) : 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-label\",\n            children: \"\\u0645\\u062A\\u0648\\u0633\\u0637 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641 (\\u062C.\\u0645)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"card-title\",\n            children: \"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0635\\u0627\\u0631\\u064A\\u0641\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: () => setShowForm(!showForm),\n            children: showForm ? 'إلغاء' : 'إضافة مصروف جديد'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), showForm && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0641\\u0626\\u0629 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641 *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"category\",\n                  value: formData.category,\n                  onChange: handleInputChange,\n                  className: \"form-control\",\n                  required: true,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"\\u0627\\u062E\\u062A\\u0631 \\u0641\\u0626\\u0629 \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 23\n                  }, this), expenseCategories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: category,\n                    children: category\n                  }, category, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 192,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.01\",\n                  name: \"amount\",\n                  value: formData.amount,\n                  onChange: handleInputChange,\n                  className: \"form-control\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u0645\\u0635\\u0631\\u0648\\u0641 *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"expense_date\",\n                  value: formData.expense_date,\n                  onChange: handleInputChange,\n                  className: \"form-control\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0637\\u0631\\u064A\\u0642\\u0629 \\u0627\\u0644\\u062F\\u0641\\u0639\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"payment_method\",\n                  value: formData.payment_method,\n                  onChange: handleInputChange,\n                  className: \"form-control\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"\\u0627\\u062E\\u062A\\u0631 \\u0637\\u0631\\u064A\\u0642\\u0629 \\u0627\\u0644\\u062F\\u0641\\u0639\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\\u0646\\u0642\\u062F\\u064A\",\n                    children: \"\\u0646\\u0642\\u062F\\u064A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\\u0634\\u064A\\u0643\",\n                    children: \"\\u0634\\u064A\\u0643\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\\u062A\\u062D\\u0648\\u064A\\u0644 \\u0628\\u0646\\u0643\\u064A\",\n                    children: \"\\u062A\\u062D\\u0648\\u064A\\u0644 \\u0628\\u0646\\u0643\\u064A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\\u0628\\u0637\\u0627\\u0642\\u0629 \\u0627\\u0626\\u062A\\u0645\\u0627\\u0646\",\n                    children: \"\\u0628\\u0637\\u0627\\u0642\\u0629 \\u0627\\u0626\\u062A\\u0645\\u0627\\u0646\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0625\\u064A\\u0635\\u0627\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"receipt_number\",\n                  value: formData.receipt_number,\n                  onChange: handleInputChange,\n                  className: \"form-control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0644\\u0648\\u0635\\u0641 *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"description\",\n                  value: formData.description,\n                  onChange: handleInputChange,\n                  className: \"form-control\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  name: \"notes\",\n                  value: formData.notes,\n                  onChange: handleInputChange,\n                  className: \"form-control\",\n                  rows: \"3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-success\",\n              children: editingExpense ? 'تحديث المصروف' : 'إضافة المصروف'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-danger\",\n              onClick: resetForm,\n              children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: expenses.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0645\\u0635\\u0627\\u0631\\u064A\\u0641 \\u0645\\u0633\\u062C\\u0644\\u0629 \\u062D\\u062A\\u0649 \\u0627\\u0644\\u0622\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0641\\u0626\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0648\\u0635\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0637\\u0631\\u064A\\u0642\\u0629 \\u0627\\u0644\\u062F\\u0641\\u0639\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0625\\u064A\\u0635\\u0627\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: expenses.map(expense => {\n              var _expense$amount2;\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: new Date(expense.expense_date).toLocaleDateString('ar-EG')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: expense.category\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: expense.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    color: 'red'\n                  },\n                  children: [(_expense$amount2 = expense.amount) === null || _expense$amount2 === void 0 ? void 0 : _expense$amount2.toLocaleString(), \" \\u062C.\\u0645\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: expense.payment_method || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: expense.receipt_number || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-warning\",\n                    onClick: () => handleEdit(expense),\n                    style: {\n                      marginLeft: '0.5rem'\n                    },\n                    children: \"\\u062A\\u0639\\u062F\\u064A\\u0644\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 21\n                }, this)]\n              }, expense.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this);\n};\n_s(Expenses, \"Dd69orl3qcBk+wemXPrPHKfg4fc=\");\n_c = Expenses;\nexport default Expenses;\nvar _c;\n$RefreshReg$(_c, \"Expenses\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "Expenses", "_s", "expenses", "setExpenses", "loading", "setLoading", "showForm", "setShowForm", "editingExpense", "setEditingExpense", "formData", "setFormData", "category", "description", "amount", "expense_date", "payment_method", "receipt_number", "notes", "message", "setMessage", "error", "setError", "expenseCategories", "fetchExpenses", "response", "get", "data", "console", "handleInputChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "expenseData", "parseFloat", "put", "id", "post", "setTimeout", "handleEdit", "expense", "_expense$amount", "toString", "resetForm", "getTotalExpenses", "reduce", "total", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "background", "toLocaleString", "length", "toFixed", "display", "justifyContent", "alignItems", "onClick", "onSubmit", "onChange", "required", "map", "type", "step", "rows", "gap", "_expense$amount2", "Date", "toLocaleDateString", "color", "marginLeft", "_c", "$RefreshReg$"], "sources": ["F:/القيمة المضافة/accounting-system/src/components/Expenses.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst Expenses = () => {\n  const [expenses, setExpenses] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showForm, setShowForm] = useState(false);\n  const [editingExpense, setEditingExpense] = useState(null);\n  const [formData, setFormData] = useState({\n    category: '',\n    description: '',\n    amount: '',\n    expense_date: '',\n    payment_method: '',\n    receipt_number: '',\n    notes: ''\n  });\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n\n  const expenseCategories = [\n    'مصاريف إدارية',\n    'مصاريف تشغيلية',\n    'مصاريف تسويق',\n    'مصاريف صيانة',\n    'مصاريف مواصلات',\n    'مصاريف اتصالات',\n    'مصاريف كهرباء',\n    'مصاريف إيجار',\n    'مصاريف أخرى'\n  ];\n\n  useEffect(() => {\n    fetchExpenses();\n  }, []);\n\n  const fetchExpenses = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/expenses');\n      setExpenses(response.data);\n    } catch (error) {\n      setError('خطأ في جلب بيانات المصاريف');\n      console.error('Error fetching expenses:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      const expenseData = {\n        ...formData,\n        amount: parseFloat(formData.amount) || 0\n      };\n\n      if (editingExpense) {\n        await axios.put(`http://localhost:5000/api/expenses/${editingExpense.id}`, expenseData);\n        setMessage('تم تحديث المصروف بنجاح');\n      } else {\n        await axios.post('http://localhost:5000/api/expenses', expenseData);\n        setMessage('تم إضافة المصروف بنجاح');\n      }\n      \n      setFormData({\n        category: '',\n        description: '',\n        amount: '',\n        expense_date: '',\n        payment_method: '',\n        receipt_number: '',\n        notes: ''\n      });\n      setShowForm(false);\n      setEditingExpense(null);\n      fetchExpenses();\n      \n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      setError('خطأ في حفظ بيانات المصروف');\n      console.error('Error saving expense:', error);\n    }\n  };\n\n  const handleEdit = (expense) => {\n    setEditingExpense(expense);\n    setFormData({\n      category: expense.category,\n      description: expense.description,\n      amount: expense.amount?.toString() || '',\n      expense_date: expense.expense_date,\n      payment_method: expense.payment_method || '',\n      receipt_number: expense.receipt_number || '',\n      notes: expense.notes || ''\n    });\n    setShowForm(true);\n  };\n\n  const resetForm = () => {\n    setFormData({\n      category: '',\n      description: '',\n      amount: '',\n      expense_date: '',\n      payment_method: '',\n      receipt_number: '',\n      notes: ''\n    });\n    setShowForm(false);\n    setEditingExpense(null);\n  };\n\n  const getTotalExpenses = () => {\n    return expenses.reduce((total, expense) => total + (expense.amount || 0), 0);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"container\">\n        <div className=\"loading\">جاري تحميل بيانات المصاريف...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container\">\n      <h1 className=\"page-title\">إدارة المصاريف</h1>\n      \n      {message && <div className=\"success\">{message}</div>}\n      {error && <div className=\"error\">{error}</div>}\n      \n      <div className=\"row\">\n        <div className=\"col-md-4\">\n          <div className=\"stat-card\" style={{ background: 'linear-gradient(135deg, #f44336 0%, #da190b 100%)' }}>\n            <div className=\"stat-number\">{getTotalExpenses().toLocaleString()}</div>\n            <div className=\"stat-label\">إجمالي المصاريف (ج.م)</div>\n          </div>\n        </div>\n        <div className=\"col-md-4\">\n          <div className=\"stat-card\">\n            <div className=\"stat-number\">{expenses.length}</div>\n            <div className=\"stat-label\">عدد المصاريف</div>\n          </div>\n        </div>\n        <div className=\"col-md-4\">\n          <div className=\"stat-card\" style={{ background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)' }}>\n            <div className=\"stat-number\">\n              {expenses.length > 0 ? (getTotalExpenses() / expenses.length).toFixed(0) : 0}\n            </div>\n            <div className=\"stat-label\">متوسط المصروف (ج.م)</div>\n          </div>\n        </div>\n      </div>\n      \n      <div className=\"card\">\n        <div className=\"card-header\">\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <h3 className=\"card-title\">قائمة المصاريف</h3>\n            <button \n              className=\"btn btn-primary\"\n              onClick={() => setShowForm(!showForm)}\n            >\n              {showForm ? 'إلغاء' : 'إضافة مصروف جديد'}\n            </button>\n          </div>\n        </div>\n        \n        {showForm && (\n          <div className=\"card-body\">\n            <form onSubmit={handleSubmit}>\n              <div className=\"row\">\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">فئة المصروف *</label>\n                    <select\n                      name=\"category\"\n                      value={formData.category}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                      required\n                    >\n                      <option value=\"\">اختر فئة المصروف</option>\n                      {expenseCategories.map(category => (\n                        <option key={category} value={category}>{category}</option>\n                      ))}\n                    </select>\n                  </div>\n                </div>\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">المبلغ *</label>\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      name=\"amount\"\n                      value={formData.amount}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                      required\n                    />\n                  </div>\n                </div>\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">تاريخ المصروف *</label>\n                    <input\n                      type=\"date\"\n                      name=\"expense_date\"\n                      value={formData.expense_date}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                      required\n                    />\n                  </div>\n                </div>\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">طريقة الدفع</label>\n                    <select\n                      name=\"payment_method\"\n                      value={formData.payment_method}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                    >\n                      <option value=\"\">اختر طريقة الدفع</option>\n                      <option value=\"نقدي\">نقدي</option>\n                      <option value=\"شيك\">شيك</option>\n                      <option value=\"تحويل بنكي\">تحويل بنكي</option>\n                      <option value=\"بطاقة ائتمان\">بطاقة ائتمان</option>\n                    </select>\n                  </div>\n                </div>\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">رقم الإيصال</label>\n                    <input\n                      type=\"text\"\n                      name=\"receipt_number\"\n                      value={formData.receipt_number}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                    />\n                  </div>\n                </div>\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">الوصف *</label>\n                    <input\n                      type=\"text\"\n                      name=\"description\"\n                      value={formData.description}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                      required\n                    />\n                  </div>\n                </div>\n                <div className=\"col\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">ملاحظات</label>\n                    <textarea\n                      name=\"notes\"\n                      value={formData.notes}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                      rows=\"3\"\n                    ></textarea>\n                  </div>\n                </div>\n              </div>\n              <div style={{ display: 'flex', gap: '1rem' }}>\n                <button type=\"submit\" className=\"btn btn-success\">\n                  {editingExpense ? 'تحديث المصروف' : 'إضافة المصروف'}\n                </button>\n                <button type=\"button\" className=\"btn btn-danger\" onClick={resetForm}>\n                  إلغاء\n                </button>\n              </div>\n            </form>\n          </div>\n        )}\n        \n        <div className=\"card-body\">\n          {expenses.length === 0 ? (\n            <p>لا توجد مصاريف مسجلة حتى الآن</p>\n          ) : (\n            <table className=\"table\">\n              <thead>\n                <tr>\n                  <th>التاريخ</th>\n                  <th>الفئة</th>\n                  <th>الوصف</th>\n                  <th>المبلغ</th>\n                  <th>طريقة الدفع</th>\n                  <th>رقم الإيصال</th>\n                  <th>الإجراءات</th>\n                </tr>\n              </thead>\n              <tbody>\n                {expenses.map(expense => (\n                  <tr key={expense.id}>\n                    <td>{new Date(expense.expense_date).toLocaleDateString('ar-EG')}</td>\n                    <td>{expense.category}</td>\n                    <td>{expense.description}</td>\n                    <td style={{ color: 'red' }}>{expense.amount?.toLocaleString()} ج.م</td>\n                    <td>{expense.payment_method || '-'}</td>\n                    <td>{expense.receipt_number || '-'}</td>\n                    <td>\n                      <button \n                        className=\"btn btn-warning\"\n                        onClick={() => handleEdit(expense)}\n                        style={{ marginLeft: '0.5rem' }}\n                      >\n                        تعديل\n                      </button>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Expenses;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC;IACvCiB,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE,EAAE;IAClBC,cAAc,EAAE,EAAE;IAClBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM4B,iBAAiB,GAAG,CACxB,eAAe,EACf,gBAAgB,EAChB,cAAc,EACd,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAChB,eAAe,EACf,cAAc,EACd,aAAa,CACd;EAED3B,SAAS,CAAC,MAAM;IACd4B,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMoB,QAAQ,GAAG,MAAM5B,KAAK,CAAC6B,GAAG,CAAC,oCAAoC,CAAC;MACtEvB,WAAW,CAACsB,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAON,KAAK,EAAE;MACdC,QAAQ,CAAC,4BAA4B,CAAC;MACtCM,OAAO,CAACP,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,iBAAiB,GAAIC,CAAC,IAAK;IAC/BnB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACoB,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMC,WAAW,GAAG;QAClB,GAAG1B,QAAQ;QACXI,MAAM,EAAEuB,UAAU,CAAC3B,QAAQ,CAACI,MAAM,CAAC,IAAI;MACzC,CAAC;MAED,IAAIN,cAAc,EAAE;QAClB,MAAMX,KAAK,CAACyC,GAAG,CAAC,sCAAsC9B,cAAc,CAAC+B,EAAE,EAAE,EAAEH,WAAW,CAAC;QACvFhB,UAAU,CAAC,wBAAwB,CAAC;MACtC,CAAC,MAAM;QACL,MAAMvB,KAAK,CAAC2C,IAAI,CAAC,oCAAoC,EAAEJ,WAAW,CAAC;QACnEhB,UAAU,CAAC,wBAAwB,CAAC;MACtC;MAEAT,WAAW,CAAC;QACVC,QAAQ,EAAE,EAAE;QACZC,WAAW,EAAE,EAAE;QACfC,MAAM,EAAE,EAAE;QACVC,YAAY,EAAE,EAAE;QAChBC,cAAc,EAAE,EAAE;QAClBC,cAAc,EAAE,EAAE;QAClBC,KAAK,EAAE;MACT,CAAC,CAAC;MACFX,WAAW,CAAC,KAAK,CAAC;MAClBE,iBAAiB,CAAC,IAAI,CAAC;MACvBe,aAAa,CAAC,CAAC;MAEfiB,UAAU,CAAC,MAAMrB,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,QAAQ,CAAC,2BAA2B,CAAC;MACrCM,OAAO,CAACP,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMqB,UAAU,GAAIC,OAAO,IAAK;IAAA,IAAAC,eAAA;IAC9BnC,iBAAiB,CAACkC,OAAO,CAAC;IAC1BhC,WAAW,CAAC;MACVC,QAAQ,EAAE+B,OAAO,CAAC/B,QAAQ;MAC1BC,WAAW,EAAE8B,OAAO,CAAC9B,WAAW;MAChCC,MAAM,EAAE,EAAA8B,eAAA,GAAAD,OAAO,CAAC7B,MAAM,cAAA8B,eAAA,uBAAdA,eAAA,CAAgBC,QAAQ,CAAC,CAAC,KAAI,EAAE;MACxC9B,YAAY,EAAE4B,OAAO,CAAC5B,YAAY;MAClCC,cAAc,EAAE2B,OAAO,CAAC3B,cAAc,IAAI,EAAE;MAC5CC,cAAc,EAAE0B,OAAO,CAAC1B,cAAc,IAAI,EAAE;MAC5CC,KAAK,EAAEyB,OAAO,CAACzB,KAAK,IAAI;IAC1B,CAAC,CAAC;IACFX,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMuC,SAAS,GAAGA,CAAA,KAAM;IACtBnC,WAAW,CAAC;MACVC,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,EAAE;MACfC,MAAM,EAAE,EAAE;MACVC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE,EAAE;MAClBC,KAAK,EAAE;IACT,CAAC,CAAC;IACFX,WAAW,CAAC,KAAK,CAAC;IAClBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMsC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,OAAO7C,QAAQ,CAAC8C,MAAM,CAAC,CAACC,KAAK,EAAEN,OAAO,KAAKM,KAAK,IAAIN,OAAO,CAAC7B,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC9E,CAAC;EAED,IAAIV,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKmD,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBpD,OAAA;QAAKmD,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC;EAEV;EAEA,oBACExD,OAAA;IAAKmD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBpD,OAAA;MAAImD,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAE7CpC,OAAO,iBAAIpB,OAAA;MAAKmD,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAEhC;IAAO;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACnDlC,KAAK,iBAAItB,OAAA;MAAKmD,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAE9B;IAAK;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE9CxD,OAAA;MAAKmD,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClBpD,OAAA;QAAKmD,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBpD,OAAA;UAAKmD,SAAS,EAAC,WAAW;UAACM,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAoD,CAAE;UAAAN,QAAA,gBACpGpD,OAAA;YAAKmD,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEJ,gBAAgB,CAAC,CAAC,CAACW,cAAc,CAAC;UAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxExD,OAAA;YAAKmD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNxD,OAAA;QAAKmD,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBpD,OAAA;UAAKmD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBpD,OAAA;YAAKmD,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEjD,QAAQ,CAACyD;UAAM;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpDxD,OAAA;YAAKmD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNxD,OAAA;QAAKmD,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBpD,OAAA;UAAKmD,SAAS,EAAC,WAAW;UAACM,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAoD,CAAE;UAAAN,QAAA,gBACpGpD,OAAA;YAAKmD,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzBjD,QAAQ,CAACyD,MAAM,GAAG,CAAC,GAAG,CAACZ,gBAAgB,CAAC,CAAC,GAAG7C,QAAQ,CAACyD,MAAM,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG;UAAC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eACNxD,OAAA;YAAKmD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxD,OAAA;MAAKmD,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBpD,OAAA;QAAKmD,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BpD,OAAA;UAAKyD,KAAK,EAAE;YAAEK,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAZ,QAAA,gBACrFpD,OAAA;YAAImD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9CxD,OAAA;YACEmD,SAAS,EAAC,iBAAiB;YAC3Bc,OAAO,EAAEA,CAAA,KAAMzD,WAAW,CAAC,CAACD,QAAQ,CAAE;YAAA6C,QAAA,EAErC7C,QAAQ,GAAG,OAAO,GAAG;UAAkB;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELjD,QAAQ,iBACPP,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBpD,OAAA;UAAMkE,QAAQ,EAAE/B,YAAa;UAAAiB,QAAA,gBAC3BpD,OAAA;YAAKmD,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBpD,OAAA;cAAKmD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBpD,OAAA;gBAAKmD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpD,OAAA;kBAAOmD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnDxD,OAAA;kBACEiC,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAEvB,QAAQ,CAACE,QAAS;kBACzBsD,QAAQ,EAAErC,iBAAkB;kBAC5BqB,SAAS,EAAC,cAAc;kBACxBiB,QAAQ;kBAAAhB,QAAA,gBAERpD,OAAA;oBAAQkC,KAAK,EAAC,EAAE;oBAAAkB,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACzChC,iBAAiB,CAAC6C,GAAG,CAACxD,QAAQ,iBAC7Bb,OAAA;oBAAuBkC,KAAK,EAAErB,QAAS;oBAAAuC,QAAA,EAAEvC;kBAAQ,GAApCA,QAAQ;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqC,CAC3D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxD,OAAA;cAAKmD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBpD,OAAA;gBAAKmD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpD,OAAA;kBAAOmD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9CxD,OAAA;kBACEsE,IAAI,EAAC,QAAQ;kBACbC,IAAI,EAAC,MAAM;kBACXtC,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAEvB,QAAQ,CAACI,MAAO;kBACvBoD,QAAQ,EAAErC,iBAAkB;kBAC5BqB,SAAS,EAAC,cAAc;kBACxBiB,QAAQ;gBAAA;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxD,OAAA;cAAKmD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBpD,OAAA;gBAAKmD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpD,OAAA;kBAAOmD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrDxD,OAAA;kBACEsE,IAAI,EAAC,MAAM;kBACXrC,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAEvB,QAAQ,CAACK,YAAa;kBAC7BmD,QAAQ,EAAErC,iBAAkB;kBAC5BqB,SAAS,EAAC,cAAc;kBACxBiB,QAAQ;gBAAA;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxD,OAAA;cAAKmD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBpD,OAAA;gBAAKmD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpD,OAAA;kBAAOmD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjDxD,OAAA;kBACEiC,IAAI,EAAC,gBAAgB;kBACrBC,KAAK,EAAEvB,QAAQ,CAACM,cAAe;kBAC/BkD,QAAQ,EAAErC,iBAAkB;kBAC5BqB,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAExBpD,OAAA;oBAAQkC,KAAK,EAAC,EAAE;oBAAAkB,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC1CxD,OAAA;oBAAQkC,KAAK,EAAC,0BAAM;oBAAAkB,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClCxD,OAAA;oBAAQkC,KAAK,EAAC,oBAAK;oBAAAkB,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChCxD,OAAA;oBAAQkC,KAAK,EAAC,yDAAY;oBAAAkB,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9CxD,OAAA;oBAAQkC,KAAK,EAAC,qEAAc;oBAAAkB,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxD,OAAA;cAAKmD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBpD,OAAA;gBAAKmD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpD,OAAA;kBAAOmD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjDxD,OAAA;kBACEsE,IAAI,EAAC,MAAM;kBACXrC,IAAI,EAAC,gBAAgB;kBACrBC,KAAK,EAAEvB,QAAQ,CAACO,cAAe;kBAC/BiD,QAAQ,EAAErC,iBAAkB;kBAC5BqB,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxD,OAAA;cAAKmD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBpD,OAAA;gBAAKmD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpD,OAAA;kBAAOmD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7CxD,OAAA;kBACEsE,IAAI,EAAC,MAAM;kBACXrC,IAAI,EAAC,aAAa;kBAClBC,KAAK,EAAEvB,QAAQ,CAACG,WAAY;kBAC5BqD,QAAQ,EAAErC,iBAAkB;kBAC5BqB,SAAS,EAAC,cAAc;kBACxBiB,QAAQ;gBAAA;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNxD,OAAA;cAAKmD,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClBpD,OAAA;gBAAKmD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBpD,OAAA;kBAAOmD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7CxD,OAAA;kBACEiC,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAEvB,QAAQ,CAACQ,KAAM;kBACtBgD,QAAQ,EAAErC,iBAAkB;kBAC5BqB,SAAS,EAAC,cAAc;kBACxBqB,IAAI,EAAC;gBAAG;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxD,OAAA;YAAKyD,KAAK,EAAE;cAAEK,OAAO,EAAE,MAAM;cAAEW,GAAG,EAAE;YAAO,CAAE;YAAArB,QAAA,gBAC3CpD,OAAA;cAAQsE,IAAI,EAAC,QAAQ;cAACnB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC9C3C,cAAc,GAAG,eAAe,GAAG;YAAe;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACTxD,OAAA;cAAQsE,IAAI,EAAC,QAAQ;cAACnB,SAAS,EAAC,gBAAgB;cAACc,OAAO,EAAElB,SAAU;cAAAK,QAAA,EAAC;YAErE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,eAEDxD,OAAA;QAAKmD,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBjD,QAAQ,CAACyD,MAAM,KAAK,CAAC,gBACpB5D,OAAA;UAAAoD,QAAA,EAAG;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAEpCxD,OAAA;UAAOmD,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACtBpD,OAAA;YAAAoD,QAAA,eACEpD,OAAA;cAAAoD,QAAA,gBACEpD,OAAA;gBAAAoD,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBxD,OAAA;gBAAAoD,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdxD,OAAA;gBAAAoD,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdxD,OAAA;gBAAAoD,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfxD,OAAA;gBAAAoD,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBxD,OAAA;gBAAAoD,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBxD,OAAA;gBAAAoD,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRxD,OAAA;YAAAoD,QAAA,EACGjD,QAAQ,CAACkE,GAAG,CAACzB,OAAO;cAAA,IAAA8B,gBAAA;cAAA,oBACnB1E,OAAA;gBAAAoD,QAAA,gBACEpD,OAAA;kBAAAoD,QAAA,EAAK,IAAIuB,IAAI,CAAC/B,OAAO,CAAC5B,YAAY,CAAC,CAAC4D,kBAAkB,CAAC,OAAO;gBAAC;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrExD,OAAA;kBAAAoD,QAAA,EAAKR,OAAO,CAAC/B;gBAAQ;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3BxD,OAAA;kBAAAoD,QAAA,EAAKR,OAAO,CAAC9B;gBAAW;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9BxD,OAAA;kBAAIyD,KAAK,EAAE;oBAAEoB,KAAK,EAAE;kBAAM,CAAE;kBAAAzB,QAAA,IAAAsB,gBAAA,GAAE9B,OAAO,CAAC7B,MAAM,cAAA2D,gBAAA,uBAAdA,gBAAA,CAAgBf,cAAc,CAAC,CAAC,EAAC,gBAAI;gBAAA;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxExD,OAAA;kBAAAoD,QAAA,EAAKR,OAAO,CAAC3B,cAAc,IAAI;gBAAG;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxCxD,OAAA;kBAAAoD,QAAA,EAAKR,OAAO,CAAC1B,cAAc,IAAI;gBAAG;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxCxD,OAAA;kBAAAoD,QAAA,eACEpD,OAAA;oBACEmD,SAAS,EAAC,iBAAiB;oBAC3Bc,OAAO,EAAEA,CAAA,KAAMtB,UAAU,CAACC,OAAO,CAAE;oBACnCa,KAAK,EAAE;sBAAEqB,UAAU,EAAE;oBAAS,CAAE;oBAAA1B,QAAA,EACjC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GAfEZ,OAAO,CAACJ,EAAE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBf,CAAC;YAAA,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CA1UID,QAAQ;AAAA8E,EAAA,GAAR9E,QAAQ;AA4Ud,eAAeA,QAAQ;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}