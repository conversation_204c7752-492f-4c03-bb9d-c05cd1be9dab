import React, { useState, useEffect } from 'react';
import axios from 'axios';

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalCustomers: 0,
    totalSuppliers: 0,
    totalProducts: 0,
    totalSalesInvoices: 0,
    totalPurchaseInvoices: 0,
    treasuryBalance: 0,
    totalExpenses: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // جلب إحصائيات مختلفة من الخادم
      const [
        customersRes,
        suppliersRes,
        productsRes,
        salesRes,
        purchasesRes
      ] = await Promise.all([
        axios.get('http://localhost:5000/api/customers'),
        axios.get('http://localhost:5000/api/suppliers'),
        axios.get('http://localhost:5000/api/products'),
        axios.get('http://localhost:5000/api/sales-invoices'),
        axios.get('http://localhost:5000/api/purchase-invoices')
      ]);

      setStats({
        totalCustomers: customersRes.data.length,
        totalSuppliers: suppliersRes.data.length,
        totalProducts: productsRes.data.length,
        totalSalesInvoices: salesRes.data.length,
        totalPurchaseInvoices: purchasesRes.data.length,
        treasuryBalance: 0, // سيتم حسابها لاحقاً
        totalExpenses: 0 // سيتم حسابها لاحقاً
      });
    } catch (error) {
      console.error('خطأ في جلب بيانات لوحة التحكم:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">جاري تحميل البيانات...</div>
      </div>
    );
  }

  return (
    <div className="container">
      <h1 className="page-title">لوحة التحكم الرئيسية</h1>
      
      <div className="dashboard-stats">
        <div className="stat-card">
          <div className="stat-number">{stats.totalCustomers}</div>
          <div className="stat-label">إجمالي العملاء</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-number">{stats.totalSuppliers}</div>
          <div className="stat-label">إجمالي الموردين</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-number">{stats.totalProducts}</div>
          <div className="stat-label">إجمالي المنتجات</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-number">{stats.totalSalesInvoices}</div>
          <div className="stat-label">فواتير المبيعات</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-number">{stats.totalPurchaseInvoices}</div>
          <div className="stat-label">فواتير المشتريات</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-number">{stats.treasuryBalance.toLocaleString()}</div>
          <div className="stat-label">رصيد الخزينة</div>
        </div>
      </div>

      <div className="row">
        <div className="col-md-6">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">الإجراءات السريعة</h3>
            </div>
            <div className="card-body">
              <div className="row">
                <div className="col-md-6">
                  <a href="/customers" className="btn btn-primary" style={{width: '100%', marginBottom: '1rem'}}>
                    إضافة عميل جديد
                  </a>
                </div>
                <div className="col-md-6">
                  <a href="/suppliers" className="btn btn-success" style={{width: '100%', marginBottom: '1rem'}}>
                    إضافة مورد جديد
                  </a>
                </div>
                <div className="col-md-6">
                  <a href="/sales-invoices" className="btn btn-warning" style={{width: '100%', marginBottom: '1rem'}}>
                    فاتورة مبيعات جديدة
                  </a>
                </div>
                <div className="col-md-6">
                  <a href="/purchase-invoices" className="btn btn-danger" style={{width: '100%', marginBottom: '1rem'}}>
                    فاتورة مشتريات جديدة
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="col-md-6">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">آخر العمليات</h3>
            </div>
            <div className="card-body">
              <p>سيتم عرض آخر العمليات هنا...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
