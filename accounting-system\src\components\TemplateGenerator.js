import * as XLSX from 'xlsx';

export const generateCustomersTemplate = () => {
  const headers = [
    'اسم العميل',
    'رقم الهاتف',
    'البريد الإلكتروني',
    'العنوان',
    'الرقم الضريبي'
  ];
  
  const sampleData = [
    ['أحمد محمد علي', '01234567890', '<EMAIL>', 'القاهرة، مصر', '123456789'],
    ['فاطمة أحمد', '01987654321', '<EMAIL>', 'الإسكندرية، مصر', '987654321'],
    ['محمد حسن', '01555666777', '<EMAIL>', 'الجيزة، مصر', '555666777']
  ];
  
  const data = [headers, ...sampleData];
  
  const ws = XLSX.utils.aoa_to_sheet(data);
  
  // تنسيق الأعمدة
  const colWidths = [
    { wch: 20 }, // اسم العميل
    { wch: 15 }, // رقم الهاتف
    { wch: 25 }, // البريد الإلكتروني
    { wch: 30 }, // العنوان
    { wch: 15 }  // الرقم الضريبي
  ];
  ws['!cols'] = colWidths;
  
  // تنسيق الرأس
  const headerStyle = {
    font: { bold: true, color: { rgb: "FFFFFF" } },
    fill: { fgColor: { rgb: "4472C4" } },
    alignment: { horizontal: "center" }
  };
  
  // تطبيق التنسيق على الرأس
  for (let i = 0; i < headers.length; i++) {
    const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });
    if (!ws[cellRef]) ws[cellRef] = {};
    ws[cellRef].s = headerStyle;
  }
  
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, 'العملاء');
  
  // إضافة ورقة التعليمات
  const instructionsData = [
    ['تعليمات استيراد العملاء'],
    [''],
    ['1. املأ البيانات في ورقة "العملاء"'],
    ['2. اسم العميل مطلوب'],
    ['3. باقي الحقول اختيارية'],
    ['4. احفظ الملف بصيغة Excel (.xlsx)'],
    ['5. ارفع الملف في نظام المحاسبة'],
    [''],
    ['ملاحظات:'],
    ['- تأكد من صحة أرقام الهواتف'],
    ['- تأكد من صحة عناوين البريد الإلكتروني'],
    ['- يمكن ترك الحقول فارغة إذا لم تكن متوفرة']
  ];
  
  const instructionsWs = XLSX.utils.aoa_to_sheet(instructionsData);
  instructionsWs['!cols'] = [{ wch: 50 }];
  
  XLSX.utils.book_append_sheet(wb, instructionsWs, 'التعليمات');
  
  return wb;
};

export const generateSuppliersTemplate = () => {
  const headers = [
    'اسم المورد',
    'رقم الهاتف',
    'البريد الإلكتروني',
    'العنوان',
    'الرقم الضريبي'
  ];
  
  const sampleData = [
    ['شركة التوريدات المتقدمة', '0223456789', '<EMAIL>', 'القاهرة الجديدة، مصر', '111222333'],
    ['مؤسسة الخدمات التجارية', '0212345678', '<EMAIL>', 'المعادي، القاهرة', '444555666'],
    ['شركة المواد الخام', '0201234567', '<EMAIL>', 'مدينة نصر، القاهرة', '777888999']
  ];
  
  const data = [headers, ...sampleData];
  const ws = XLSX.utils.aoa_to_sheet(data);
  
  ws['!cols'] = [
    { wch: 25 }, { wch: 15 }, { wch: 25 }, { wch: 30 }, { wch: 15 }
  ];
  
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, 'الموردين');
  
  return wb;
};

export const generateProductsTemplate = () => {
  const headers = [
    'اسم المنتج',
    'الوصف',
    'الوحدة',
    'سعر البيع',
    'سعر التكلفة',
    'الكمية المتاحة'
  ];
  
  const sampleData = [
    ['لابتوب Dell', 'لابتوب Dell Inspiron 15', 'قطعة', '15000', '12000', '10'],
    ['طابعة HP', 'طابعة HP LaserJet', 'قطعة', '3000', '2500', '5'],
    ['ورق A4', 'ورق طباعة A4 أبيض', 'رزمة', '50', '40', '100'],
    ['قلم حبر جاف', 'قلم حبر جاف أزرق', 'قطعة', '2', '1.5', '500']
  ];
  
  const data = [headers, ...sampleData];
  const ws = XLSX.utils.aoa_to_sheet(data);
  
  ws['!cols'] = [
    { wch: 20 }, { wch: 30 }, { wch: 10 }, { wch: 12 }, { wch: 12 }, { wch: 15 }
  ];
  
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, 'المنتجات');
  
  // إضافة ورقة التعليمات
  const instructionsData = [
    ['تعليمات استيراد المنتجات'],
    [''],
    ['1. املأ البيانات في ورقة "المنتجات"'],
    ['2. اسم المنتج مطلوب'],
    ['3. الأسعار يجب أن تكون أرقام'],
    ['4. الكمية يجب أن تكون رقم صحيح'],
    [''],
    ['أمثلة على الوحدات:'],
    ['- قطعة، كيلو، متر، لتر، رزمة، علبة، كرتونة'],
    [''],
    ['ملاحظات:'],
    ['- تأكد من صحة الأسعار'],
    ['- سعر التكلفة يجب أن يكون أقل من سعر البيع'],
    ['- الكمية المتاحة تمثل المخزون الحالي']
  ];
  
  const instructionsWs = XLSX.utils.aoa_to_sheet(instructionsData);
  instructionsWs['!cols'] = [{ wch: 50 }];
  
  XLSX.utils.book_append_sheet(wb, instructionsWs, 'التعليمات');
  
  return wb;
};

export const generateExpensesTemplate = () => {
  const headers = [
    'فئة المصروف',
    'الوصف',
    'المبلغ',
    'تاريخ المصروف',
    'طريقة الدفع',
    'رقم الإيصال',
    'ملاحظات'
  ];
  
  const sampleData = [
    ['مصاريف إدارية', 'فاتورة كهرباء', '500', '2024-01-15', 'نقدي', 'R001', 'فاتورة شهر يناير'],
    ['مصاريف تشغيلية', 'صيانة أجهزة', '1200', '2024-01-16', 'شيك', 'R002', 'صيانة دورية'],
    ['مصاريف تسويق', 'إعلانات فيسبوك', '800', '2024-01-17', 'تحويل بنكي', 'R003', 'حملة إعلانية'],
    ['مصاريف مواصلات', 'وقود السيارات', '300', '2024-01-18', 'نقدي', 'R004', 'وقود الأسبوع']
  ];
  
  const data = [headers, ...sampleData];
  const ws = XLSX.utils.aoa_to_sheet(data);
  
  ws['!cols'] = [
    { wch: 18 }, { wch: 25 }, { wch: 12 }, { wch: 15 }, { wch: 15 }, { wch: 15 }, { wch: 30 }
  ];
  
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, 'المصاريف');
  
  // إضافة ورقة فئات المصاريف
  const categoriesData = [
    ['فئات المصاريف المتاحة'],
    [''],
    ['مصاريف إدارية'],
    ['مصاريف تشغيلية'],
    ['مصاريف تسويق'],
    ['مصاريف صيانة'],
    ['مصاريف مواصلات'],
    ['مصاريف اتصالات'],
    ['مصاريف كهرباء'],
    ['مصاريف إيجار'],
    ['مصاريف أخرى'],
    [''],
    ['طرق الدفع المتاحة:'],
    [''],
    ['نقدي'],
    ['شيك'],
    ['تحويل بنكي'],
    ['بطاقة ائتمان']
  ];
  
  const categoriesWs = XLSX.utils.aoa_to_sheet(categoriesData);
  categoriesWs['!cols'] = [{ wch: 30 }];
  
  XLSX.utils.book_append_sheet(wb, categoriesWs, 'الفئات المتاحة');
  
  return wb;
};

export const downloadTemplate = (type) => {
  let workbook;
  let filename;
  
  switch (type) {
    case 'customers':
      workbook = generateCustomersTemplate();
      filename = 'قالب_العملاء.xlsx';
      break;
    case 'suppliers':
      workbook = generateSuppliersTemplate();
      filename = 'قالب_الموردين.xlsx';
      break;
    case 'products':
      workbook = generateProductsTemplate();
      filename = 'قالب_المنتجات.xlsx';
      break;
    case 'expenses':
      workbook = generateExpensesTemplate();
      filename = 'قالب_المصاريف.xlsx';
      break;
    default:
      console.error('نوع القالب غير مدعوم:', type);
      return;
  }
  
  XLSX.writeFile(workbook, filename);
};

// دالة لإنشاء قالب شامل يحتوي على جميع الأقسام
export const generateCompleteTemplate = () => {
  const wb = XLSX.utils.book_new();
  
  // إضافة ورقة العملاء
  const customersWb = generateCustomersTemplate();
  const customersWs = customersWb.Sheets['العملاء'];
  XLSX.utils.book_append_sheet(wb, customersWs, 'العملاء');
  
  // إضافة ورقة الموردين
  const suppliersWb = generateSuppliersTemplate();
  const suppliersWs = suppliersWb.Sheets['الموردين'];
  XLSX.utils.book_append_sheet(wb, suppliersWs, 'الموردين');
  
  // إضافة ورقة المنتجات
  const productsWb = generateProductsTemplate();
  const productsWs = productsWb.Sheets['المنتجات'];
  XLSX.utils.book_append_sheet(wb, productsWs, 'المنتجات');
  
  // إضافة ورقة المصاريف
  const expensesWb = generateExpensesTemplate();
  const expensesWs = expensesWb.Sheets['المصاريف'];
  XLSX.utils.book_append_sheet(wb, expensesWs, 'المصاريف');
  
  // إضافة ورقة التعليمات العامة
  const generalInstructionsData = [
    ['دليل استخدام قوالب النظام المحاسبي'],
    [''],
    ['مرحباً بك في النظام المحاسبي المتكامل'],
    [''],
    ['هذا الملف يحتوي على قوالب لاستيراد البيانات:'],
    [''],
    ['1. ورقة العملاء - لاستيراد بيانات العملاء'],
    ['2. ورقة الموردين - لاستيراد بيانات الموردين'],
    ['3. ورقة المنتجات - لاستيراد بيانات المنتجات'],
    ['4. ورقة المصاريف - لاستيراد بيانات المصاريف'],
    [''],
    ['تعليمات عامة:'],
    [''],
    ['• لا تغير أسماء الأعمدة في الصف الأول'],
    ['• املأ البيانات بدءاً من الصف الثاني'],
    ['• احذف البيانات التجريبية قبل إدخال بياناتك'],
    ['• تأكد من صحة التواريخ (DD/MM/YYYY)'],
    ['• تأكد من صحة الأرقام (بدون فواصل أو رموز)'],
    ['• احفظ الملف بصيغة Excel (.xlsx)'],
    [''],
    ['للدعم الفني: <EMAIL>'],
    ['الموقع الإلكتروني: www.accounting-system.com']
  ];
  
  const generalInstructionsWs = XLSX.utils.aoa_to_sheet(generalInstructionsData);
  generalInstructionsWs['!cols'] = [{ wch: 60 }];
  
  // إدراج ورقة التعليمات في البداية
  const sheets = wb.SheetNames;
  wb.SheetNames = ['التعليمات العامة', ...sheets];
  wb.Sheets['التعليمات العامة'] = generalInstructionsWs;
  
  return wb;
};

export const downloadCompleteTemplate = () => {
  const workbook = generateCompleteTemplate();
  XLSX.writeFile(workbook, 'قوالب_النظام_المحاسبي_الشاملة.xlsx');
};
