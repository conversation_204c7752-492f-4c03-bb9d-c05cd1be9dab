{"ast": null, "code": "var _jsxFileName = \"F:\\\\\\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\\\\accounting-system\\\\src\\\\components\\\\Reports.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Reports = () => {\n  _s();\n  const [reportData, setReportData] = useState({\n    totalSales: 0,\n    totalPurchases: 0,\n    totalExpenses: 0,\n    netProfit: 0,\n    customersCount: 0,\n    suppliersCount: 0,\n    productsCount: 0,\n    treasuryBalance: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [dateRange, setDateRange] = useState({\n    startDate: '',\n    endDate: ''\n  });\n  const [error, setError] = useState('');\n  useEffect(() => {\n    fetchReportData();\n  }, []);\n  const fetchReportData = async () => {\n    try {\n      setLoading(true);\n\n      // جلب البيانات من مختلف الـ APIs\n      const [customersRes, suppliersRes, productsRes, salesRes, purchasesRes, expensesRes, treasuryRes] = await Promise.all([axios.get('http://localhost:5000/api/customers'), axios.get('http://localhost:5000/api/suppliers'), axios.get('http://localhost:5000/api/products'), axios.get('http://localhost:5000/api/sales-invoices'), axios.get('http://localhost:5000/api/purchase-invoices'), axios.get('http://localhost:5000/api/expenses'), axios.get('http://localhost:5000/api/treasury-transactions')]);\n\n      // حساب الإحصائيات\n      const totalSales = salesRes.data.reduce((sum, invoice) => sum + (invoice.net_amount || 0), 0);\n      const totalPurchases = purchasesRes.data.reduce((sum, invoice) => sum + (invoice.net_amount || 0), 0);\n      const totalExpenses = expensesRes.data.reduce((sum, expense) => sum + (expense.amount || 0), 0);\n      const treasuryIncome = treasuryRes.data.filter(t => t.transaction_type === 'income').reduce((sum, t) => sum + t.amount, 0);\n      const treasuryExpense = treasuryRes.data.filter(t => t.transaction_type === 'expense').reduce((sum, t) => sum + t.amount, 0);\n      setReportData({\n        totalSales,\n        totalPurchases,\n        totalExpenses,\n        netProfit: totalSales - totalPurchases - totalExpenses,\n        customersCount: customersRes.data.length,\n        suppliersCount: suppliersRes.data.length,\n        productsCount: productsRes.data.length,\n        treasuryBalance: treasuryIncome - treasuryExpense\n      });\n    } catch (error) {\n      setError('خطأ في جلب بيانات التقارير');\n      console.error('Error fetching report data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDateRangeChange = e => {\n    setDateRange({\n      ...dateRange,\n      [e.target.name]: e.target.value\n    });\n  };\n  const generateReport = () => {\n    // هنا يمكن إضافة منطق تصفية البيانات حسب التاريخ\n    fetchReportData();\n  };\n  const printReport = () => {\n    window.print();\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"page-title\",\n      children: \"\\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0627\\u0644\\u064A\\u0629\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\u0641\\u0644\\u0627\\u062A\\u0631 \\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"\\u0645\\u0646 \\u062A\\u0627\\u0631\\u064A\\u062E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                name: \"startDate\",\n                value: dateRange.startDate,\n                onChange: handleDateRangeChange,\n                className: \"form-control\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"\\u0625\\u0644\\u0649 \\u062A\\u0627\\u0631\\u064A\\u062E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                name: \"endDate\",\n                value: dateRange.endDate,\n                onChange: handleDateRangeChange,\n                className: \"form-control\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"\\xA0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-primary\",\n                  onClick: generateReport,\n                  style: {\n                    marginLeft: '1rem'\n                  },\n                  children: \"\\u062A\\u062D\\u062F\\u064A\\u062B \\u0627\\u0644\\u062A\\u0642\\u0631\\u064A\\u0631\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-success\",\n                  onClick: printReport,\n                  children: \"\\u0637\\u0628\\u0627\\u0639\\u0629 \\u0627\\u0644\\u062A\\u0642\\u0631\\u064A\\u0631\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-stats\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        style: {\n          background: 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-number\",\n          children: reportData.totalSales.toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A (\\u062C.\\u0645)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        style: {\n          background: 'linear-gradient(135deg, #f44336 0%, #da190b 100%)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-number\",\n          children: reportData.totalPurchases.toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A (\\u062C.\\u0645)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        style: {\n          background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-number\",\n          children: reportData.totalExpenses.toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0635\\u0627\\u0631\\u064A\\u0641 (\\u062C.\\u0645)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        style: {\n          background: reportData.netProfit >= 0 ? 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)' : 'linear-gradient(135deg, #f44336 0%, #da190b 100%)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-number\",\n          children: reportData.netProfit.toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0635\\u0627\\u0641\\u064A \\u0627\\u0644\\u0631\\u0628\\u062D (\\u062C.\\u0645)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-number\",\n          children: reportData.treasuryBalance.toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0631\\u0635\\u064A\\u062F \\u0627\\u0644\\u062E\\u0632\\u064A\\u0646\\u0629 (\\u062C.\\u0645)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-number\",\n          children: reportData.customersCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"card-title\",\n              children: \"\\u0645\\u0644\\u062E\\u0635 \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A \\u0648\\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table\",\n              children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      color: 'green'\n                    },\n                    children: [reportData.totalSales.toLocaleString(), \" \\u062C.\\u0645\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 210,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      color: 'red'\n                    },\n                    children: [reportData.totalPurchases.toLocaleString(), \" \\u062C.\\u0645\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0635\\u0627\\u0631\\u064A\\u0641\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      color: 'red'\n                    },\n                    children: [reportData.totalExpenses.toLocaleString(), \" \\u062C.\\u0645\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  style: {\n                    borderTop: '2px solid #ddd'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u0635\\u0627\\u0641\\u064A \\u0627\\u0644\\u0631\\u0628\\u062D\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 218,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      color: reportData.netProfit >= 0 ? 'green' : 'red',\n                      fontWeight: 'bold',\n                      fontSize: '1.1rem'\n                    },\n                    children: [reportData.netProfit.toLocaleString(), \" \\u062C.\\u0645\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"card-title\",\n              children: \"\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A \\u0639\\u0627\\u0645\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"table\",\n              children: /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: reportData.customersCount\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: reportData.suppliersCount\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 250,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: reportData.productsCount\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u0631\\u0635\\u064A\\u062F \\u0627\\u0644\\u062E\\u0632\\u064A\\u0646\\u0629\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 254,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      color: reportData.treasuryBalance >= 0 ? 'green' : 'red',\n                      fontWeight: 'bold'\n                    },\n                    children: [reportData.treasuryBalance.toLocaleString(), \" \\u062C.\\u0645\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\u0646\\u0635\\u0627\\u0626\\u062D \\u0648\\u062A\\u0648\\u0635\\u064A\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"\\uD83D\\uDCC8 \\u062A\\u062D\\u0644\\u064A\\u0644 \\u0627\\u0644\\u0623\\u062F\\u0627\\u0621:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: reportData.netProfit >= 0 ? '✅ الشركة تحقق أرباحاً جيدة' : '⚠️ الشركة تواجه خسائر - يجب مراجعة المصاريف'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: reportData.totalSales > reportData.totalPurchases ? '✅ هامش ربح إيجابي من المبيعات' : '⚠️ تكلفة المشتريات عالية مقارنة بالمبيعات'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: reportData.treasuryBalance > 0 ? '✅ السيولة النقدية متاحة' : '⚠️ نقص في السيولة النقدية'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"\\uD83D\\uDCA1 \\u062A\\u0648\\u0635\\u064A\\u0627\\u062A:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0645\\u0631\\u0627\\u062C\\u0639\\u0629 \\u0627\\u0644\\u0645\\u0635\\u0627\\u0631\\u064A\\u0641 \\u0627\\u0644\\u0634\\u0647\\u0631\\u064A\\u0629 \\u0648\\u062A\\u062D\\u0633\\u064A\\u0646 \\u0627\\u0644\\u0643\\u0641\\u0627\\u0621\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u062A\\u062D\\u0644\\u064A\\u0644 \\u0623\\u062F\\u0627\\u0621 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0627\\u0644\\u0623\\u0643\\u062B\\u0631 \\u0631\\u0628\\u062D\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0645\\u062A\\u0627\\u0628\\u0639\\u0629 \\u0645\\u0633\\u062A\\u062D\\u0642\\u0627\\u062A \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621 \\u0628\\u0627\\u0646\\u062A\\u0638\\u0627\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u062A\\u062D\\u0633\\u064A\\u0646 \\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n};\n_s(Reports, \"PxH98AiSygA0OyCfllxeuw61/3c=\");\n_c = Reports;\nexport default Reports;\nvar _c;\n$RefreshReg$(_c, \"Reports\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "Reports", "_s", "reportData", "setReportData", "totalSales", "totalPurchases", "totalExpenses", "netProfit", "customersCount", "suppliersCount", "productsCount", "treasuryBalance", "loading", "setLoading", "date<PERSON><PERSON><PERSON>", "setDateRange", "startDate", "endDate", "error", "setError", "fetchReportData", "customersRes", "suppliersRes", "productsRes", "salesRes", "purchasesRes", "expensesRes", "treasuryRes", "Promise", "all", "get", "data", "reduce", "sum", "invoice", "net_amount", "expense", "amount", "treasuryIncome", "filter", "t", "transaction_type", "treasuryExpense", "length", "console", "handleDateRangeChange", "e", "target", "name", "value", "generateReport", "printReport", "window", "print", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onChange", "onClick", "style", "marginLeft", "background", "toLocaleString", "color", "borderTop", "fontWeight", "fontSize", "_c", "$RefreshReg$"], "sources": ["F:/القيمة المضافة/accounting-system/src/components/Reports.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst Reports = () => {\n  const [reportData, setReportData] = useState({\n    totalSales: 0,\n    totalPurchases: 0,\n    totalExpenses: 0,\n    netProfit: 0,\n    customersCount: 0,\n    suppliersCount: 0,\n    productsCount: 0,\n    treasuryBalance: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [dateRange, setDateRange] = useState({\n    startDate: '',\n    endDate: ''\n  });\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    fetchReportData();\n  }, []);\n\n  const fetchReportData = async () => {\n    try {\n      setLoading(true);\n      \n      // جلب البيانات من مختلف الـ APIs\n      const [\n        customersRes,\n        suppliersRes,\n        productsRes,\n        salesRes,\n        purchasesRes,\n        expensesRes,\n        treasuryRes\n      ] = await Promise.all([\n        axios.get('http://localhost:5000/api/customers'),\n        axios.get('http://localhost:5000/api/suppliers'),\n        axios.get('http://localhost:5000/api/products'),\n        axios.get('http://localhost:5000/api/sales-invoices'),\n        axios.get('http://localhost:5000/api/purchase-invoices'),\n        axios.get('http://localhost:5000/api/expenses'),\n        axios.get('http://localhost:5000/api/treasury-transactions')\n      ]);\n\n      // حساب الإحصائيات\n      const totalSales = salesRes.data.reduce((sum, invoice) => sum + (invoice.net_amount || 0), 0);\n      const totalPurchases = purchasesRes.data.reduce((sum, invoice) => sum + (invoice.net_amount || 0), 0);\n      const totalExpenses = expensesRes.data.reduce((sum, expense) => sum + (expense.amount || 0), 0);\n      \n      const treasuryIncome = treasuryRes.data\n        .filter(t => t.transaction_type === 'income')\n        .reduce((sum, t) => sum + t.amount, 0);\n      \n      const treasuryExpense = treasuryRes.data\n        .filter(t => t.transaction_type === 'expense')\n        .reduce((sum, t) => sum + t.amount, 0);\n\n      setReportData({\n        totalSales,\n        totalPurchases,\n        totalExpenses,\n        netProfit: totalSales - totalPurchases - totalExpenses,\n        customersCount: customersRes.data.length,\n        suppliersCount: suppliersRes.data.length,\n        productsCount: productsRes.data.length,\n        treasuryBalance: treasuryIncome - treasuryExpense\n      });\n    } catch (error) {\n      setError('خطأ في جلب بيانات التقارير');\n      console.error('Error fetching report data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDateRangeChange = (e) => {\n    setDateRange({\n      ...dateRange,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const generateReport = () => {\n    // هنا يمكن إضافة منطق تصفية البيانات حسب التاريخ\n    fetchReportData();\n  };\n\n  const printReport = () => {\n    window.print();\n  };\n\n  if (loading) {\n    return (\n      <div className=\"container\">\n        <div className=\"loading\">جاري تحميل بيانات التقارير...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container\">\n      <h1 className=\"page-title\">التقارير المالية</h1>\n      \n      {error && <div className=\"error\">{error}</div>}\n      \n      {/* فلاتر التقارير */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">فلاتر التقارير</h3>\n        </div>\n        <div className=\"card-body\">\n          <div className=\"row\">\n            <div className=\"col-md-4\">\n              <div className=\"form-group\">\n                <label className=\"form-label\">من تاريخ</label>\n                <input\n                  type=\"date\"\n                  name=\"startDate\"\n                  value={dateRange.startDate}\n                  onChange={handleDateRangeChange}\n                  className=\"form-control\"\n                />\n              </div>\n            </div>\n            <div className=\"col-md-4\">\n              <div className=\"form-group\">\n                <label className=\"form-label\">إلى تاريخ</label>\n                <input\n                  type=\"date\"\n                  name=\"endDate\"\n                  value={dateRange.endDate}\n                  onChange={handleDateRangeChange}\n                  className=\"form-control\"\n                />\n              </div>\n            </div>\n            <div className=\"col-md-4\">\n              <div className=\"form-group\">\n                <label className=\"form-label\">&nbsp;</label>\n                <div>\n                  <button className=\"btn btn-primary\" onClick={generateReport} style={{ marginLeft: '1rem' }}>\n                    تحديث التقرير\n                  </button>\n                  <button className=\"btn btn-success\" onClick={printReport}>\n                    طباعة التقرير\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* الإحصائيات الرئيسية */}\n      <div className=\"dashboard-stats\">\n        <div className=\"stat-card\" style={{ background: 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)' }}>\n          <div className=\"stat-number\">{reportData.totalSales.toLocaleString()}</div>\n          <div className=\"stat-label\">إجمالي المبيعات (ج.م)</div>\n        </div>\n        \n        <div className=\"stat-card\" style={{ background: 'linear-gradient(135deg, #f44336 0%, #da190b 100%)' }}>\n          <div className=\"stat-number\">{reportData.totalPurchases.toLocaleString()}</div>\n          <div className=\"stat-label\">إجمالي المشتريات (ج.م)</div>\n        </div>\n        \n        <div className=\"stat-card\" style={{ background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)' }}>\n          <div className=\"stat-number\">{reportData.totalExpenses.toLocaleString()}</div>\n          <div className=\"stat-label\">إجمالي المصاريف (ج.م)</div>\n        </div>\n        \n        <div className=\"stat-card\" style={{ \n          background: reportData.netProfit >= 0 \n            ? 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)' \n            : 'linear-gradient(135deg, #f44336 0%, #da190b 100%)'\n        }}>\n          <div className=\"stat-number\">{reportData.netProfit.toLocaleString()}</div>\n          <div className=\"stat-label\">صافي الربح (ج.م)</div>\n        </div>\n        \n        <div className=\"stat-card\">\n          <div className=\"stat-number\">{reportData.treasuryBalance.toLocaleString()}</div>\n          <div className=\"stat-label\">رصيد الخزينة (ج.م)</div>\n        </div>\n        \n        <div className=\"stat-card\">\n          <div className=\"stat-number\">{reportData.customersCount}</div>\n          <div className=\"stat-label\">عدد العملاء</div>\n        </div>\n      </div>\n\n      {/* تفاصيل التقرير */}\n      <div className=\"row\">\n        <div className=\"col-md-6\">\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"card-title\">ملخص المبيعات والمشتريات</h3>\n            </div>\n            <div className=\"card-body\">\n              <table className=\"table\">\n                <tbody>\n                  <tr>\n                    <td><strong>إجمالي المبيعات</strong></td>\n                    <td style={{ color: 'green' }}>{reportData.totalSales.toLocaleString()} ج.م</td>\n                  </tr>\n                  <tr>\n                    <td><strong>إجمالي المشتريات</strong></td>\n                    <td style={{ color: 'red' }}>{reportData.totalPurchases.toLocaleString()} ج.م</td>\n                  </tr>\n                  <tr>\n                    <td><strong>إجمالي المصاريف</strong></td>\n                    <td style={{ color: 'red' }}>{reportData.totalExpenses.toLocaleString()} ج.م</td>\n                  </tr>\n                  <tr style={{ borderTop: '2px solid #ddd' }}>\n                    <td><strong>صافي الربح</strong></td>\n                    <td style={{ \n                      color: reportData.netProfit >= 0 ? 'green' : 'red',\n                      fontWeight: 'bold',\n                      fontSize: '1.1rem'\n                    }}>\n                      {reportData.netProfit.toLocaleString()} ج.م\n                    </td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n        \n        <div className=\"col-md-6\">\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"card-title\">إحصائيات عامة</h3>\n            </div>\n            <div className=\"card-body\">\n              <table className=\"table\">\n                <tbody>\n                  <tr>\n                    <td><strong>عدد العملاء</strong></td>\n                    <td>{reportData.customersCount}</td>\n                  </tr>\n                  <tr>\n                    <td><strong>عدد الموردين</strong></td>\n                    <td>{reportData.suppliersCount}</td>\n                  </tr>\n                  <tr>\n                    <td><strong>عدد المنتجات</strong></td>\n                    <td>{reportData.productsCount}</td>\n                  </tr>\n                  <tr>\n                    <td><strong>رصيد الخزينة</strong></td>\n                    <td style={{ \n                      color: reportData.treasuryBalance >= 0 ? 'green' : 'red',\n                      fontWeight: 'bold'\n                    }}>\n                      {reportData.treasuryBalance.toLocaleString()} ج.م\n                    </td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* نصائح وتوصيات */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">نصائح وتوصيات</h3>\n        </div>\n        <div className=\"card-body\">\n          <div className=\"row\">\n            <div className=\"col-md-6\">\n              <h5>📈 تحليل الأداء:</h5>\n              <ul>\n                <li>\n                  {reportData.netProfit >= 0 \n                    ? '✅ الشركة تحقق أرباحاً جيدة' \n                    : '⚠️ الشركة تواجه خسائر - يجب مراجعة المصاريف'}\n                </li>\n                <li>\n                  {reportData.totalSales > reportData.totalPurchases \n                    ? '✅ هامش ربح إيجابي من المبيعات' \n                    : '⚠️ تكلفة المشتريات عالية مقارنة بالمبيعات'}\n                </li>\n                <li>\n                  {reportData.treasuryBalance > 0 \n                    ? '✅ السيولة النقدية متاحة' \n                    : '⚠️ نقص في السيولة النقدية'}\n                </li>\n              </ul>\n            </div>\n            <div className=\"col-md-6\">\n              <h5>💡 توصيات:</h5>\n              <ul>\n                <li>مراجعة المصاريف الشهرية وتحسين الكفاءة</li>\n                <li>تحليل أداء المنتجات الأكثر ربحية</li>\n                <li>متابعة مستحقات العملاء بانتظام</li>\n                <li>تحسين إدارة المخزون</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Reports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGR,QAAQ,CAAC;IAC3CS,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,CAAC;IAChBC,SAAS,EAAE,CAAC;IACZC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,CAAC;IAChBC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC;IACzCqB,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACdwB,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM,CACJQ,YAAY,EACZC,YAAY,EACZC,WAAW,EACXC,QAAQ,EACRC,YAAY,EACZC,WAAW,EACXC,WAAW,CACZ,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpBhC,KAAK,CAACiC,GAAG,CAAC,qCAAqC,CAAC,EAChDjC,KAAK,CAACiC,GAAG,CAAC,qCAAqC,CAAC,EAChDjC,KAAK,CAACiC,GAAG,CAAC,oCAAoC,CAAC,EAC/CjC,KAAK,CAACiC,GAAG,CAAC,0CAA0C,CAAC,EACrDjC,KAAK,CAACiC,GAAG,CAAC,6CAA6C,CAAC,EACxDjC,KAAK,CAACiC,GAAG,CAAC,oCAAoC,CAAC,EAC/CjC,KAAK,CAACiC,GAAG,CAAC,iDAAiD,CAAC,CAC7D,CAAC;;MAEF;MACA,MAAM1B,UAAU,GAAGoB,QAAQ,CAACO,IAAI,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,IAAIC,OAAO,CAACC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAC7F,MAAM9B,cAAc,GAAGoB,YAAY,CAACM,IAAI,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,IAAIC,OAAO,CAACC,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MACrG,MAAM7B,aAAa,GAAGoB,WAAW,CAACK,IAAI,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEG,OAAO,KAAKH,GAAG,IAAIG,OAAO,CAACC,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;MAE/F,MAAMC,cAAc,GAAGX,WAAW,CAACI,IAAI,CACpCQ,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,KAAK,QAAQ,CAAC,CAC5CT,MAAM,CAAC,CAACC,GAAG,EAAEO,CAAC,KAAKP,GAAG,GAAGO,CAAC,CAACH,MAAM,EAAE,CAAC,CAAC;MAExC,MAAMK,eAAe,GAAGf,WAAW,CAACI,IAAI,CACrCQ,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,gBAAgB,KAAK,SAAS,CAAC,CAC7CT,MAAM,CAAC,CAACC,GAAG,EAAEO,CAAC,KAAKP,GAAG,GAAGO,CAAC,CAACH,MAAM,EAAE,CAAC,CAAC;MAExClC,aAAa,CAAC;QACZC,UAAU;QACVC,cAAc;QACdC,aAAa;QACbC,SAAS,EAAEH,UAAU,GAAGC,cAAc,GAAGC,aAAa;QACtDE,cAAc,EAAEa,YAAY,CAACU,IAAI,CAACY,MAAM;QACxClC,cAAc,EAAEa,YAAY,CAACS,IAAI,CAACY,MAAM;QACxCjC,aAAa,EAAEa,WAAW,CAACQ,IAAI,CAACY,MAAM;QACtChC,eAAe,EAAE2B,cAAc,GAAGI;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdC,QAAQ,CAAC,4BAA4B,CAAC;MACtCyB,OAAO,CAAC1B,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD,CAAC,SAAS;MACRL,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgC,qBAAqB,GAAIC,CAAC,IAAK;IACnC/B,YAAY,CAAC;MACX,GAAGD,SAAS;MACZ,CAACgC,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA9B,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,MAAM+B,WAAW,GAAGA,CAAA,KAAM;IACxBC,MAAM,CAACC,KAAK,CAAC,CAAC;EAChB,CAAC;EAED,IAAIzC,OAAO,EAAE;IACX,oBACEb,OAAA;MAAKuD,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBxD,OAAA;QAAKuD,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC;EAEV;EAEA,oBACE5D,OAAA;IAAKuD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBxD,OAAA;MAAIuD,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAE/CzC,KAAK,iBAAInB,OAAA;MAAKuD,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAErC;IAAK;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAG9C5D,OAAA;MAAKuD,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBxD,OAAA;QAAKuD,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BxD,OAAA;UAAIuD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACN5D,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBxD,OAAA;UAAKuD,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBxD,OAAA;YAAKuD,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBxD,OAAA;cAAKuD,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBxD,OAAA;gBAAOuD,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9C5D,OAAA;gBACE6D,IAAI,EAAC,MAAM;gBACXZ,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAEnC,SAAS,CAACE,SAAU;gBAC3B6C,QAAQ,EAAEhB,qBAAsB;gBAChCS,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5D,OAAA;YAAKuD,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBxD,OAAA;cAAKuD,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBxD,OAAA;gBAAOuD,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/C5D,OAAA;gBACE6D,IAAI,EAAC,MAAM;gBACXZ,IAAI,EAAC,SAAS;gBACdC,KAAK,EAAEnC,SAAS,CAACG,OAAQ;gBACzB4C,QAAQ,EAAEhB,qBAAsB;gBAChCS,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5D,OAAA;YAAKuD,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBxD,OAAA;cAAKuD,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBxD,OAAA;gBAAOuD,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5C5D,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAQuD,SAAS,EAAC,iBAAiB;kBAACQ,OAAO,EAAEZ,cAAe;kBAACa,KAAK,EAAE;oBAAEC,UAAU,EAAE;kBAAO,CAAE;kBAAAT,QAAA,EAAC;gBAE5F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT5D,OAAA;kBAAQuD,SAAS,EAAC,iBAAiB;kBAACQ,OAAO,EAAEX,WAAY;kBAAAI,QAAA,EAAC;gBAE1D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKuD,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BxD,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAACS,KAAK,EAAE;UAAEE,UAAU,EAAE;QAAoD,CAAE;QAAAV,QAAA,gBACpGxD,OAAA;UAAKuD,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAErD,UAAU,CAACE,UAAU,CAAC8D,cAAc,CAAC;QAAC;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3E5D,OAAA;UAAKuD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eAEN5D,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAACS,KAAK,EAAE;UAAEE,UAAU,EAAE;QAAoD,CAAE;QAAAV,QAAA,gBACpGxD,OAAA;UAAKuD,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAErD,UAAU,CAACG,cAAc,CAAC6D,cAAc,CAAC;QAAC;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/E5D,OAAA;UAAKuD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eAEN5D,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAACS,KAAK,EAAE;UAAEE,UAAU,EAAE;QAAoD,CAAE;QAAAV,QAAA,gBACpGxD,OAAA;UAAKuD,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAErD,UAAU,CAACI,aAAa,CAAC4D,cAAc,CAAC;QAAC;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9E5D,OAAA;UAAKuD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eAEN5D,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAACS,KAAK,EAAE;UAChCE,UAAU,EAAE/D,UAAU,CAACK,SAAS,IAAI,CAAC,GACjC,mDAAmD,GACnD;QACN,CAAE;QAAAgD,QAAA,gBACAxD,OAAA;UAAKuD,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAErD,UAAU,CAACK,SAAS,CAAC2D,cAAc,CAAC;QAAC;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1E5D,OAAA;UAAKuD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eAEN5D,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxD,OAAA;UAAKuD,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAErD,UAAU,CAACS,eAAe,CAACuD,cAAc,CAAC;QAAC;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAChF5D,OAAA;UAAKuD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eAEN5D,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBxD,OAAA;UAAKuD,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAErD,UAAU,CAACM;QAAc;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9D5D,OAAA;UAAKuD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKuD,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClBxD,OAAA;QAAKuD,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBxD,OAAA;UAAKuD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBxD,OAAA;YAAKuD,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BxD,OAAA;cAAIuD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACN5D,OAAA;YAAKuD,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBxD,OAAA;cAAOuD,SAAS,EAAC,OAAO;cAAAC,QAAA,eACtBxD,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAAwD,QAAA,gBACExD,OAAA;oBAAAwD,QAAA,eAAIxD,OAAA;sBAAAwD,QAAA,EAAQ;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzC5D,OAAA;oBAAIgE,KAAK,EAAE;sBAAEI,KAAK,EAAE;oBAAQ,CAAE;oBAAAZ,QAAA,GAAErD,UAAU,CAACE,UAAU,CAAC8D,cAAc,CAAC,CAAC,EAAC,gBAAI;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC,eACL5D,OAAA;kBAAAwD,QAAA,gBACExD,OAAA;oBAAAwD,QAAA,eAAIxD,OAAA;sBAAAwD,QAAA,EAAQ;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1C5D,OAAA;oBAAIgE,KAAK,EAAE;sBAAEI,KAAK,EAAE;oBAAM,CAAE;oBAAAZ,QAAA,GAAErD,UAAU,CAACG,cAAc,CAAC6D,cAAc,CAAC,CAAC,EAAC,gBAAI;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACL5D,OAAA;kBAAAwD,QAAA,gBACExD,OAAA;oBAAAwD,QAAA,eAAIxD,OAAA;sBAAAwD,QAAA,EAAQ;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzC5D,OAAA;oBAAIgE,KAAK,EAAE;sBAAEI,KAAK,EAAE;oBAAM,CAAE;oBAAAZ,QAAA,GAAErD,UAAU,CAACI,aAAa,CAAC4D,cAAc,CAAC,CAAC,EAAC,gBAAI;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC,eACL5D,OAAA;kBAAIgE,KAAK,EAAE;oBAAEK,SAAS,EAAE;kBAAiB,CAAE;kBAAAb,QAAA,gBACzCxD,OAAA;oBAAAwD,QAAA,eAAIxD,OAAA;sBAAAwD,QAAA,EAAQ;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpC5D,OAAA;oBAAIgE,KAAK,EAAE;sBACTI,KAAK,EAAEjE,UAAU,CAACK,SAAS,IAAI,CAAC,GAAG,OAAO,GAAG,KAAK;sBAClD8D,UAAU,EAAE,MAAM;sBAClBC,QAAQ,EAAE;oBACZ,CAAE;oBAAAf,QAAA,GACCrD,UAAU,CAACK,SAAS,CAAC2D,cAAc,CAAC,CAAC,EAAC,gBACzC;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5D,OAAA;QAAKuD,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBxD,OAAA;UAAKuD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBxD,OAAA;YAAKuD,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BxD,OAAA;cAAIuD,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACN5D,OAAA;YAAKuD,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBxD,OAAA;cAAOuD,SAAS,EAAC,OAAO;cAAAC,QAAA,eACtBxD,OAAA;gBAAAwD,QAAA,gBACExD,OAAA;kBAAAwD,QAAA,gBACExD,OAAA;oBAAAwD,QAAA,eAAIxD,OAAA;sBAAAwD,QAAA,EAAQ;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrC5D,OAAA;oBAAAwD,QAAA,EAAKrD,UAAU,CAACM;kBAAc;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eACL5D,OAAA;kBAAAwD,QAAA,gBACExD,OAAA;oBAAAwD,QAAA,eAAIxD,OAAA;sBAAAwD,QAAA,EAAQ;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtC5D,OAAA;oBAAAwD,QAAA,EAAKrD,UAAU,CAACO;kBAAc;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC,eACL5D,OAAA;kBAAAwD,QAAA,gBACExD,OAAA;oBAAAwD,QAAA,eAAIxD,OAAA;sBAAAwD,QAAA,EAAQ;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtC5D,OAAA;oBAAAwD,QAAA,EAAKrD,UAAU,CAACQ;kBAAa;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACL5D,OAAA;kBAAAwD,QAAA,gBACExD,OAAA;oBAAAwD,QAAA,eAAIxD,OAAA;sBAAAwD,QAAA,EAAQ;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtC5D,OAAA;oBAAIgE,KAAK,EAAE;sBACTI,KAAK,EAAEjE,UAAU,CAACS,eAAe,IAAI,CAAC,GAAG,OAAO,GAAG,KAAK;sBACxD0D,UAAU,EAAE;oBACd,CAAE;oBAAAd,QAAA,GACCrD,UAAU,CAACS,eAAe,CAACuD,cAAc,CAAC,CAAC,EAAC,gBAC/C;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKuD,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBxD,OAAA;QAAKuD,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BxD,OAAA;UAAIuD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACN5D,OAAA;QAAKuD,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBxD,OAAA;UAAKuD,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBxD,OAAA;YAAKuD,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBxD,OAAA;cAAAwD,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzB5D,OAAA;cAAAwD,QAAA,gBACExD,OAAA;gBAAAwD,QAAA,EACGrD,UAAU,CAACK,SAAS,IAAI,CAAC,GACtB,4BAA4B,GAC5B;cAA6C;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACL5D,OAAA;gBAAAwD,QAAA,EACGrD,UAAU,CAACE,UAAU,GAAGF,UAAU,CAACG,cAAc,GAC9C,+BAA+B,GAC/B;cAA2C;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACL5D,OAAA;gBAAAwD,QAAA,EACGrD,UAAU,CAACS,eAAe,GAAG,CAAC,GAC3B,yBAAyB,GACzB;cAA2B;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACN5D,OAAA;YAAKuD,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBxD,OAAA;cAAAwD,QAAA,EAAI;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnB5D,OAAA;cAAAwD,QAAA,gBACExD,OAAA;gBAAAwD,QAAA,EAAI;cAAsC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/C5D,OAAA;gBAAAwD,QAAA,EAAI;cAAgC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzC5D,OAAA;gBAAAwD,QAAA,EAAI;cAA8B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvC5D,OAAA;gBAAAwD,QAAA,EAAI;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1D,EAAA,CAlTID,OAAO;AAAAuE,EAAA,GAAPvE,OAAO;AAoTb,eAAeA,OAAO;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}