{"ast": null, "code": "var _jsxFileName = \"F:\\\\\\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\\\\accounting-system\\\\src\\\\components\\\\Backup.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Backup = () => {\n  _s();\n  var _backupData$data$cust, _backupData$data$supp, _backupData$data$prod;\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n  const [backupData, setBackupData] = useState(null);\n  const createBackup = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // جلب جميع البيانات\n      const [customersRes, suppliersRes, productsRes, salesRes, purchasesRes, expensesRes, treasuryRes, inventoryRes] = await Promise.all([axios.get('http://localhost:5000/api/customers'), axios.get('http://localhost:5000/api/suppliers'), axios.get('http://localhost:5000/api/products'), axios.get('http://localhost:5000/api/sales-invoices'), axios.get('http://localhost:5000/api/purchase-invoices'), axios.get('http://localhost:5000/api/expenses'), axios.get('http://localhost:5000/api/treasury-transactions'), axios.get('http://localhost:5000/api/inventory-movements')]);\n      const backup = {\n        timestamp: new Date().toISOString(),\n        version: '1.0',\n        data: {\n          customers: customersRes.data,\n          suppliers: suppliersRes.data,\n          products: productsRes.data,\n          sales_invoices: salesRes.data,\n          purchase_invoices: purchasesRes.data,\n          expenses: expensesRes.data,\n          treasury_transactions: treasuryRes.data,\n          inventory_movements: inventoryRes.data\n        }\n      };\n      setBackupData(backup);\n      setMessage('تم إنشاء النسخة الاحتياطية بنجاح');\n    } catch (error) {\n      setError('خطأ في إنشاء النسخة الاحتياطية: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const downloadBackup = () => {\n    if (!backupData) return;\n    const dataStr = JSON.stringify(backupData, null, 2);\n    const dataBlob = new Blob([dataStr], {\n      type: 'application/json'\n    });\n    const url = URL.createObjectURL(dataBlob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `accounting-backup-${new Date().toISOString().split('T')[0]}.json`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n  const handleFileUpload = event => {\n    const file = event.target.files[0];\n    if (!file) return;\n    const reader = new FileReader();\n    reader.onload = e => {\n      try {\n        const backupData = JSON.parse(e.target.result);\n        setBackupData(backupData);\n        setMessage('تم تحميل ملف النسخة الاحتياطية بنجاح');\n      } catch (error) {\n        setError('خطأ في قراءة ملف النسخة الاحتياطية');\n      }\n    };\n    reader.readAsText(file);\n  };\n  const restoreBackup = async () => {\n    if (!backupData || !backupData.data) {\n      setError('لا توجد بيانات للاستعادة');\n      return;\n    }\n    if (!window.confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.')) {\n      return;\n    }\n    try {\n      setLoading(true);\n      setError('');\n\n      // استعادة البيانات (هذا يتطلب APIs خاصة في الخادم)\n      await axios.post('http://localhost:5000/api/restore-backup', backupData);\n      setMessage('تم استعادة النسخة الاحتياطية بنجاح');\n    } catch (error) {\n      setError('خطأ في استعادة النسخة الاحتياطية: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const exportToExcel = async () => {\n    try {\n      setLoading(true);\n\n      // إنشاء ملف Excel (يتطلب مكتبة إضافية)\n      const response = await axios.get('http://localhost:5000/api/export-excel', {\n        responseType: 'blob'\n      });\n      const blob = new Blob([response.data], {\n        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n      });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `accounting-data-${new Date().toISOString().split('T')[0]}.xlsx`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n      setMessage('تم تصدير البيانات إلى Excel بنجاح');\n    } catch (error) {\n      setError('خطأ في تصدير البيانات: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"page-title\",\n      children: \"\\u0627\\u0644\\u0646\\u0633\\u062E \\u0627\\u0644\\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A \\u0648\\u0627\\u0644\\u0627\\u0633\\u062A\\u0639\\u0627\\u062F\\u0629\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 19\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"card-title\",\n              children: \"\\uD83D\\uDCBE \\u0625\\u0646\\u0634\\u0627\\u0621 \\u0646\\u0633\\u062E\\u0629 \\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0642\\u0645 \\u0628\\u0625\\u0646\\u0634\\u0627\\u0621 \\u0646\\u0633\\u062E\\u0629 \\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\\u0629 \\u0645\\u0646 \\u062C\\u0645\\u064A\\u0639 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u064A.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-primary\",\n                onClick: createBackup,\n                disabled: loading,\n                style: {\n                  width: '100%',\n                  marginBottom: '1rem'\n                },\n                children: loading ? 'جاري الإنشاء...' : 'إنشاء نسخة احتياطية'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), backupData && /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-success\",\n                onClick: downloadBackup,\n                style: {\n                  width: '100%'\n                },\n                children: \"\\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0646\\u0633\\u062E\\u0629 \\u0627\\u0644\\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), backupData && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"alert alert-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0646\\u0633\\u062E\\u0629 \\u0627\\u0644\\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\\u0629:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 62\n              }, this), \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E: \", new Date(backupData.timestamp).toLocaleString('ar-EG'), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 84\n              }, this), \"\\u0627\\u0644\\u0625\\u0635\\u062F\\u0627\\u0631: \", backupData.version, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 48\n              }, this), \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621: \", ((_backupData$data$cust = backupData.data.customers) === null || _backupData$data$cust === void 0 ? void 0 : _backupData$data$cust.length) || 0, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 72\n              }, this), \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646: \", ((_backupData$data$supp = backupData.data.suppliers) === null || _backupData$data$supp === void 0 ? void 0 : _backupData$data$supp.length) || 0, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 73\n              }, this), \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A: \", ((_backupData$data$prod = backupData.data.products) === null || _backupData$data$prod === void 0 ? void 0 : _backupData$data$prod.length) || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"card-title\",\n              children: \"\\uD83D\\uDCC2 \\u0627\\u0633\\u062A\\u0639\\u0627\\u062F\\u0629 \\u0646\\u0633\\u062E\\u0629 \\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0642\\u0645 \\u0628\\u062A\\u062D\\u0645\\u064A\\u0644 \\u0645\\u0644\\u0641 \\u0646\\u0633\\u062E\\u0629 \\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\\u0629 \\u0644\\u0627\\u0633\\u062A\\u0639\\u0627\\u062F\\u0629 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"\\u0627\\u062E\\u062A\\u0631 \\u0645\\u0644\\u0641 \\u0627\\u0644\\u0646\\u0633\\u062E\\u0629 \\u0627\\u0644\\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                accept: \".json\",\n                onChange: handleFileUpload,\n                className: \"form-control\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), backupData && /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-warning\",\n              onClick: restoreBackup,\n              disabled: loading,\n              style: {\n                width: '100%'\n              },\n              children: loading ? 'جاري الاستعادة...' : 'استعادة النسخة الاحتياطية'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\uD83D\\uDCCA \\u062A\\u0635\\u062F\\u064A\\u0631 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-4\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-success\",\n              onClick: exportToExcel,\n              disabled: loading,\n              style: {\n                width: '100%'\n              },\n              children: \"\\u062A\\u0635\\u062F\\u064A\\u0631 \\u0625\\u0644\\u0649 Excel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-4\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-info\",\n              onClick: () => {\n                if (backupData) {\n                  const csvData = convertToCSV(backupData.data);\n                  downloadCSV(csvData, 'accounting-data.csv');\n                } else {\n                  setError('يجب إنشاء نسخة احتياطية أولاً');\n                }\n              },\n              style: {\n                width: '100%'\n              },\n              children: \"\\u062A\\u0635\\u062F\\u064A\\u0631 \\u0625\\u0644\\u0649 CSV\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-4\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn btn-secondary\",\n              onClick: () => {\n                if (backupData) {\n                  downloadBackup();\n                } else {\n                  setError('يجب إنشاء نسخة احتياطية أولاً');\n                }\n              },\n              style: {\n                width: '100%'\n              },\n              children: \"\\u062A\\u0635\\u062F\\u064A\\u0631 \\u0625\\u0644\\u0649 JSON\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\uD83D\\uDD12 \\u0646\\u0635\\u0627\\u0626\\u062D \\u0627\\u0644\\u0623\\u0645\\u0627\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"\\uD83D\\uDCCB \\u0623\\u0641\\u0636\\u0644 \\u0627\\u0644\\u0645\\u0645\\u0627\\u0631\\u0633\\u0627\\u062A:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0642\\u0645 \\u0628\\u0625\\u0646\\u0634\\u0627\\u0621 \\u0646\\u0633\\u062E\\u0629 \\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\\u0629 \\u064A\\u0648\\u0645\\u064A\\u0627\\u064B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0627\\u062D\\u062A\\u0641\\u0638 \\u0628\\u0646\\u0633\\u062E \\u0645\\u062A\\u0639\\u062F\\u062F\\u0629 \\u0641\\u064A \\u0623\\u0645\\u0627\\u0643\\u0646 \\u0645\\u062E\\u062A\\u0644\\u0641\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0627\\u062E\\u062A\\u0628\\u0631 \\u0627\\u0633\\u062A\\u0639\\u0627\\u062F\\u0629 \\u0627\\u0644\\u0646\\u0633\\u062E \\u0627\\u0644\\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\\u0629 \\u0628\\u0627\\u0646\\u062A\\u0638\\u0627\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0627\\u062D\\u0645 \\u0645\\u0644\\u0641\\u0627\\u062A \\u0627\\u0644\\u0646\\u0633\\u062E \\u0627\\u0644\\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\\u0629 \\u0628\\u0643\\u0644\\u0645\\u0629 \\u0645\\u0631\\u0648\\u0631\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"\\u26A0\\uFE0F \\u062A\\u062D\\u0630\\u064A\\u0631\\u0627\\u062A \\u0645\\u0647\\u0645\\u0629:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0627\\u0633\\u062A\\u0639\\u0627\\u062F\\u0629 \\u0627\\u0644\\u0646\\u0633\\u062E\\u0629 \\u0627\\u0644\\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\\u0629 \\u062A\\u0633\\u062A\\u0628\\u062F\\u0644 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u062A\\u0623\\u0643\\u062F \\u0645\\u0646 \\u0635\\u062D\\u0629 \\u0645\\u0644\\u0641 \\u0627\\u0644\\u0646\\u0633\\u062E\\u0629 \\u0627\\u0644\\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\\u0629 \\u0642\\u0628\\u0644 \\u0627\\u0644\\u0627\\u0633\\u062A\\u0639\\u0627\\u062F\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0642\\u0645 \\u0628\\u0625\\u0646\\u0634\\u0627\\u0621 \\u0646\\u0633\\u062E\\u0629 \\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\\u0629 \\u0642\\u0628\\u0644 \\u0623\\u064A \\u062A\\u062D\\u062F\\u064A\\u062B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0644\\u0627 \\u062A\\u0634\\u0627\\u0631\\u0643 \\u0645\\u0644\\u0641\\u0627\\u062A \\u0627\\u0644\\u0646\\u0633\\u062E \\u0627\\u0644\\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\\u0629 \\u0645\\u0639 \\u0623\\u0634\\u062E\\u0627\\u0635 \\u063A\\u064A\\u0631 \\u0645\\u062E\\u0648\\u0644\\u064A\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 5\n  }, this);\n};\n\n// دالة مساعدة لتحويل البيانات إلى CSV\n_s(Backup, \"oO1DwAmxYoXstAJ02OR5sY0WCU4=\");\n_c = Backup;\nconst convertToCSV = data => {\n  // تحويل بسيط للعملاء كمثال\n  if (!data.customers) return '';\n  const headers = ['الاسم', 'الهاتف', 'البريد الإلكتروني', 'العنوان'];\n  const rows = data.customers.map(customer => [customer.name, customer.phone || '', customer.email || '', customer.address || '']);\n  return [headers, ...rows].map(row => row.join(',')).join('\\n');\n};\n\n// دالة مساعدة لتحميل ملف CSV\nconst downloadCSV = (csvData, filename) => {\n  const blob = new Blob([csvData], {\n    type: 'text/csv;charset=utf-8;'\n  });\n  const url = URL.createObjectURL(blob);\n  const link = document.createElement('a');\n  link.href = url;\n  link.download = filename;\n  document.body.appendChild(link);\n  link.click();\n  document.body.removeChild(link);\n  URL.revokeObjectURL(url);\n};\nexport default Backup;\nvar _c;\n$RefreshReg$(_c, \"Backup\");", "map": {"version": 3, "names": ["React", "useState", "axios", "jsxDEV", "_jsxDEV", "Backup", "_s", "_backupData$data$cust", "_backupData$data$supp", "_backupData$data$prod", "loading", "setLoading", "message", "setMessage", "error", "setError", "backupData", "setBackupData", "createBackup", "customersRes", "suppliersRes", "productsRes", "salesRes", "purchasesRes", "expensesRes", "treasuryRes", "inventoryRes", "Promise", "all", "get", "backup", "timestamp", "Date", "toISOString", "version", "data", "customers", "suppliers", "products", "sales_invoices", "purchase_invoices", "expenses", "treasury_transactions", "inventory_movements", "downloadBackup", "dataStr", "JSON", "stringify", "dataBlob", "Blob", "type", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "split", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleFileUpload", "event", "file", "target", "files", "reader", "FileReader", "onload", "e", "parse", "result", "readAsText", "restoreBackup", "window", "confirm", "post", "exportToExcel", "response", "responseType", "blob", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginBottom", "onClick", "disabled", "width", "toLocaleString", "length", "accept", "onChange", "csvData", "convertToCSV", "downloadCSV", "_c", "headers", "rows", "map", "customer", "name", "phone", "email", "address", "row", "join", "filename", "$RefreshReg$"], "sources": ["F:/القيمة المضافة/accounting-system/src/components/Backup.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport axios from 'axios';\n\nconst Backup = () => {\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n  const [backupData, setBackupData] = useState(null);\n\n  const createBackup = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      \n      // جلب جميع البيانات\n      const [\n        customersRes,\n        suppliersRes,\n        productsRes,\n        salesRes,\n        purchasesRes,\n        expensesRes,\n        treasuryRes,\n        inventoryRes\n      ] = await Promise.all([\n        axios.get('http://localhost:5000/api/customers'),\n        axios.get('http://localhost:5000/api/suppliers'),\n        axios.get('http://localhost:5000/api/products'),\n        axios.get('http://localhost:5000/api/sales-invoices'),\n        axios.get('http://localhost:5000/api/purchase-invoices'),\n        axios.get('http://localhost:5000/api/expenses'),\n        axios.get('http://localhost:5000/api/treasury-transactions'),\n        axios.get('http://localhost:5000/api/inventory-movements')\n      ]);\n\n      const backup = {\n        timestamp: new Date().toISOString(),\n        version: '1.0',\n        data: {\n          customers: customersRes.data,\n          suppliers: suppliersRes.data,\n          products: productsRes.data,\n          sales_invoices: salesRes.data,\n          purchase_invoices: purchasesRes.data,\n          expenses: expensesRes.data,\n          treasury_transactions: treasuryRes.data,\n          inventory_movements: inventoryRes.data\n        }\n      };\n\n      setBackupData(backup);\n      setMessage('تم إنشاء النسخة الاحتياطية بنجاح');\n    } catch (error) {\n      setError('خطأ في إنشاء النسخة الاحتياطية: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const downloadBackup = () => {\n    if (!backupData) return;\n\n    const dataStr = JSON.stringify(backupData, null, 2);\n    const dataBlob = new Blob([dataStr], { type: 'application/json' });\n    const url = URL.createObjectURL(dataBlob);\n    \n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `accounting-backup-${new Date().toISOString().split('T')[0]}.json`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n\n  const handleFileUpload = (event) => {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      try {\n        const backupData = JSON.parse(e.target.result);\n        setBackupData(backupData);\n        setMessage('تم تحميل ملف النسخة الاحتياطية بنجاح');\n      } catch (error) {\n        setError('خطأ في قراءة ملف النسخة الاحتياطية');\n      }\n    };\n    reader.readAsText(file);\n  };\n\n  const restoreBackup = async () => {\n    if (!backupData || !backupData.data) {\n      setError('لا توجد بيانات للاستعادة');\n      return;\n    }\n\n    if (!window.confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.')) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError('');\n\n      // استعادة البيانات (هذا يتطلب APIs خاصة في الخادم)\n      await axios.post('http://localhost:5000/api/restore-backup', backupData);\n      \n      setMessage('تم استعادة النسخة الاحتياطية بنجاح');\n    } catch (error) {\n      setError('خطأ في استعادة النسخة الاحتياطية: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const exportToExcel = async () => {\n    try {\n      setLoading(true);\n      \n      // إنشاء ملف Excel (يتطلب مكتبة إضافية)\n      const response = await axios.get('http://localhost:5000/api/export-excel', {\n        responseType: 'blob'\n      });\n      \n      const blob = new Blob([response.data], { \n        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' \n      });\n      \n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `accounting-data-${new Date().toISOString().split('T')[0]}.xlsx`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n      \n      setMessage('تم تصدير البيانات إلى Excel بنجاح');\n    } catch (error) {\n      setError('خطأ في تصدير البيانات: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"container\">\n      <h1 className=\"page-title\">النسخ الاحتياطي والاستعادة</h1>\n      \n      {message && <div className=\"success\">{message}</div>}\n      {error && <div className=\"error\">{error}</div>}\n      \n      <div className=\"row\">\n        {/* إنشاء نسخة احتياطية */}\n        <div className=\"col-md-6\">\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"card-title\">💾 إنشاء نسخة احتياطية</h3>\n            </div>\n            <div className=\"card-body\">\n              <p>قم بإنشاء نسخة احتياطية من جميع بيانات النظام المحاسبي.</p>\n              <div style={{ marginBottom: '1rem' }}>\n                <button \n                  className=\"btn btn-primary\" \n                  onClick={createBackup}\n                  disabled={loading}\n                  style={{ width: '100%', marginBottom: '1rem' }}\n                >\n                  {loading ? 'جاري الإنشاء...' : 'إنشاء نسخة احتياطية'}\n                </button>\n                \n                {backupData && (\n                  <button \n                    className=\"btn btn-success\" \n                    onClick={downloadBackup}\n                    style={{ width: '100%' }}\n                  >\n                    تحميل النسخة الاحتياطية\n                  </button>\n                )}\n              </div>\n              \n              {backupData && (\n                <div className=\"alert alert-info\">\n                  <strong>معلومات النسخة الاحتياطية:</strong><br />\n                  التاريخ: {new Date(backupData.timestamp).toLocaleString('ar-EG')}<br />\n                  الإصدار: {backupData.version}<br />\n                  عدد العملاء: {backupData.data.customers?.length || 0}<br />\n                  عدد الموردين: {backupData.data.suppliers?.length || 0}<br />\n                  عدد المنتجات: {backupData.data.products?.length || 0}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n        \n        {/* استعادة نسخة احتياطية */}\n        <div className=\"col-md-6\">\n          <div className=\"card\">\n            <div className=\"card-header\">\n              <h3 className=\"card-title\">📂 استعادة نسخة احتياطية</h3>\n            </div>\n            <div className=\"card-body\">\n              <p>قم بتحميل ملف نسخة احتياطية لاستعادة البيانات.</p>\n              \n              <div className=\"form-group\">\n                <label className=\"form-label\">اختر ملف النسخة الاحتياطية</label>\n                <input\n                  type=\"file\"\n                  accept=\".json\"\n                  onChange={handleFileUpload}\n                  className=\"form-control\"\n                />\n              </div>\n              \n              {backupData && (\n                <button \n                  className=\"btn btn-warning\" \n                  onClick={restoreBackup}\n                  disabled={loading}\n                  style={{ width: '100%' }}\n                >\n                  {loading ? 'جاري الاستعادة...' : 'استعادة النسخة الاحتياطية'}\n                </button>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      {/* تصدير البيانات */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">📊 تصدير البيانات</h3>\n        </div>\n        <div className=\"card-body\">\n          <div className=\"row\">\n            <div className=\"col-md-4\">\n              <button \n                className=\"btn btn-success\" \n                onClick={exportToExcel}\n                disabled={loading}\n                style={{ width: '100%' }}\n              >\n                تصدير إلى Excel\n              </button>\n            </div>\n            <div className=\"col-md-4\">\n              <button \n                className=\"btn btn-info\" \n                onClick={() => {\n                  if (backupData) {\n                    const csvData = convertToCSV(backupData.data);\n                    downloadCSV(csvData, 'accounting-data.csv');\n                  } else {\n                    setError('يجب إنشاء نسخة احتياطية أولاً');\n                  }\n                }}\n                style={{ width: '100%' }}\n              >\n                تصدير إلى CSV\n              </button>\n            </div>\n            <div className=\"col-md-4\">\n              <button \n                className=\"btn btn-secondary\" \n                onClick={() => {\n                  if (backupData) {\n                    downloadBackup();\n                  } else {\n                    setError('يجب إنشاء نسخة احتياطية أولاً');\n                  }\n                }}\n                style={{ width: '100%' }}\n              >\n                تصدير إلى JSON\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      {/* نصائح الأمان */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">🔒 نصائح الأمان</h3>\n        </div>\n        <div className=\"card-body\">\n          <div className=\"row\">\n            <div className=\"col-md-6\">\n              <h5>📋 أفضل الممارسات:</h5>\n              <ul>\n                <li>قم بإنشاء نسخة احتياطية يومياً</li>\n                <li>احتفظ بنسخ متعددة في أماكن مختلفة</li>\n                <li>اختبر استعادة النسخ الاحتياطية بانتظام</li>\n                <li>احم ملفات النسخ الاحتياطية بكلمة مرور</li>\n              </ul>\n            </div>\n            <div className=\"col-md-6\">\n              <h5>⚠️ تحذيرات مهمة:</h5>\n              <ul>\n                <li>استعادة النسخة الاحتياطية تستبدل جميع البيانات</li>\n                <li>تأكد من صحة ملف النسخة الاحتياطية قبل الاستعادة</li>\n                <li>قم بإنشاء نسخة احتياطية قبل أي تحديث</li>\n                <li>لا تشارك ملفات النسخ الاحتياطية مع أشخاص غير مخولين</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// دالة مساعدة لتحويل البيانات إلى CSV\nconst convertToCSV = (data) => {\n  // تحويل بسيط للعملاء كمثال\n  if (!data.customers) return '';\n  \n  const headers = ['الاسم', 'الهاتف', 'البريد الإلكتروني', 'العنوان'];\n  const rows = data.customers.map(customer => [\n    customer.name,\n    customer.phone || '',\n    customer.email || '',\n    customer.address || ''\n  ]);\n  \n  return [headers, ...rows].map(row => row.join(',')).join('\\n');\n};\n\n// دالة مساعدة لتحميل ملف CSV\nconst downloadCSV = (csvData, filename) => {\n  const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });\n  const url = URL.createObjectURL(blob);\n  const link = document.createElement('a');\n  link.href = url;\n  link.download = filename;\n  document.body.appendChild(link);\n  link.click();\n  document.body.removeChild(link);\n  URL.revokeObjectURL(url);\n};\n\nexport default Backup;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACnB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAElD,MAAMiB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACA,MAAM,CACJI,YAAY,EACZC,YAAY,EACZC,WAAW,EACXC,QAAQ,EACRC,YAAY,EACZC,WAAW,EACXC,WAAW,EACXC,YAAY,CACb,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACpB1B,KAAK,CAAC2B,GAAG,CAAC,qCAAqC,CAAC,EAChD3B,KAAK,CAAC2B,GAAG,CAAC,qCAAqC,CAAC,EAChD3B,KAAK,CAAC2B,GAAG,CAAC,oCAAoC,CAAC,EAC/C3B,KAAK,CAAC2B,GAAG,CAAC,0CAA0C,CAAC,EACrD3B,KAAK,CAAC2B,GAAG,CAAC,6CAA6C,CAAC,EACxD3B,KAAK,CAAC2B,GAAG,CAAC,oCAAoC,CAAC,EAC/C3B,KAAK,CAAC2B,GAAG,CAAC,iDAAiD,CAAC,EAC5D3B,KAAK,CAAC2B,GAAG,CAAC,+CAA+C,CAAC,CAC3D,CAAC;MAEF,MAAMC,MAAM,GAAG;QACbC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCC,OAAO,EAAE,KAAK;QACdC,IAAI,EAAE;UACJC,SAAS,EAAEjB,YAAY,CAACgB,IAAI;UAC5BE,SAAS,EAAEjB,YAAY,CAACe,IAAI;UAC5BG,QAAQ,EAAEjB,WAAW,CAACc,IAAI;UAC1BI,cAAc,EAAEjB,QAAQ,CAACa,IAAI;UAC7BK,iBAAiB,EAAEjB,YAAY,CAACY,IAAI;UACpCM,QAAQ,EAAEjB,WAAW,CAACW,IAAI;UAC1BO,qBAAqB,EAAEjB,WAAW,CAACU,IAAI;UACvCQ,mBAAmB,EAAEjB,YAAY,CAACS;QACpC;MACF,CAAC;MAEDlB,aAAa,CAACa,MAAM,CAAC;MACrBjB,UAAU,CAAC,kCAAkC,CAAC;IAChD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,QAAQ,CAAC,kCAAkC,GAAGD,KAAK,CAACF,OAAO,CAAC;IAC9D,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAAC5B,UAAU,EAAE;IAEjB,MAAM6B,OAAO,GAAGC,IAAI,CAACC,SAAS,CAAC/B,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;IACnD,MAAMgC,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAACJ,OAAO,CAAC,EAAE;MAAEK,IAAI,EAAE;IAAmB,CAAC,CAAC;IAClE,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,QAAQ,CAAC;IAEzC,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;IACfG,IAAI,CAACI,QAAQ,GAAG,qBAAqB,IAAI1B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC0B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;IAClFJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;IAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;IACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;IAC/BF,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;EAC1B,CAAC;EAED,MAAMc,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACF,IAAI,EAAE;IAEX,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MACrB,IAAI;QACF,MAAMzD,UAAU,GAAG8B,IAAI,CAAC4B,KAAK,CAACD,CAAC,CAACL,MAAM,CAACO,MAAM,CAAC;QAC9C1D,aAAa,CAACD,UAAU,CAAC;QACzBH,UAAU,CAAC,sCAAsC,CAAC;MACpD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,QAAQ,CAAC,oCAAoC,CAAC;MAChD;IACF,CAAC;IACDuD,MAAM,CAACM,UAAU,CAACT,IAAI,CAAC;EACzB,CAAC;EAED,MAAMU,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAC7D,UAAU,IAAI,CAACA,UAAU,CAACmB,IAAI,EAAE;MACnCpB,QAAQ,CAAC,0BAA0B,CAAC;MACpC;IACF;IAEA,IAAI,CAAC+D,MAAM,CAACC,OAAO,CAAC,gFAAgF,CAAC,EAAE;MACrG;IACF;IAEA,IAAI;MACFpE,UAAU,CAAC,IAAI,CAAC;MAChBI,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACA,MAAMb,KAAK,CAAC8E,IAAI,CAAC,0CAA0C,EAAEhE,UAAU,CAAC;MAExEH,UAAU,CAAC,oCAAoC,CAAC;IAClD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,QAAQ,CAAC,oCAAoC,GAAGD,KAAK,CAACF,OAAO,CAAC;IAChE,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsE,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFtE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMuE,QAAQ,GAAG,MAAMhF,KAAK,CAAC2B,GAAG,CAAC,wCAAwC,EAAE;QACzEsD,YAAY,EAAE;MAChB,CAAC,CAAC;MAEF,MAAMC,IAAI,GAAG,IAAInC,IAAI,CAAC,CAACiC,QAAQ,CAAC/C,IAAI,CAAC,EAAE;QACrCe,IAAI,EAAE;MACR,CAAC,CAAC;MAEF,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAAC+B,IAAI,CAAC;MACrC,MAAM9B,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;MACfG,IAAI,CAACI,QAAQ,GAAG,mBAAmB,IAAI1B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC0B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;MAChFJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;MAC/BF,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;MAExBtC,UAAU,CAAC,mCAAmC,CAAC;IACjD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,QAAQ,CAAC,yBAAyB,GAAGD,KAAK,CAACF,OAAO,CAAC;IACrD,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEP,OAAA;IAAKiF,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBlF,OAAA;MAAIiF,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAA0B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAEzD9E,OAAO,iBAAIR,OAAA;MAAKiF,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAE1E;IAAO;MAAA2E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACnD5E,KAAK,iBAAIV,OAAA;MAAKiF,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAExE;IAAK;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE9CtF,OAAA;MAAKiF,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAElBlF,OAAA;QAAKiF,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBlF,OAAA;UAAKiF,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlF,OAAA;YAAKiF,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BlF,OAAA;cAAIiF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACNtF,OAAA;YAAKiF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlF,OAAA;cAAAkF,QAAA,EAAG;YAAuD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9DtF,OAAA;cAAKuF,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAO,CAAE;cAAAN,QAAA,gBACnClF,OAAA;gBACEiF,SAAS,EAAC,iBAAiB;gBAC3BQ,OAAO,EAAE3E,YAAa;gBACtB4E,QAAQ,EAAEpF,OAAQ;gBAClBiF,KAAK,EAAE;kBAAEI,KAAK,EAAE,MAAM;kBAAEH,YAAY,EAAE;gBAAO,CAAE;gBAAAN,QAAA,EAE9C5E,OAAO,GAAG,iBAAiB,GAAG;cAAqB;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,EAER1E,UAAU,iBACTZ,OAAA;gBACEiF,SAAS,EAAC,iBAAiB;gBAC3BQ,OAAO,EAAEjD,cAAe;gBACxB+C,KAAK,EAAE;kBAAEI,KAAK,EAAE;gBAAO,CAAE;gBAAAT,QAAA,EAC1B;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAEL1E,UAAU,iBACTZ,OAAA;cAAKiF,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BlF,OAAA;gBAAAkF,QAAA,EAAQ;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAAAtF,OAAA;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,gDACxC,EAAC,IAAI1D,IAAI,CAAChB,UAAU,CAACe,SAAS,CAAC,CAACiE,cAAc,CAAC,OAAO,CAAC,eAAC5F,OAAA;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,gDAC9D,EAAC1E,UAAU,CAACkB,OAAO,eAAC9B,OAAA;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,mEACtB,EAAC,EAAAnF,qBAAA,GAAAS,UAAU,CAACmB,IAAI,CAACC,SAAS,cAAA7B,qBAAA,uBAAzBA,qBAAA,CAA2B0F,MAAM,KAAI,CAAC,eAAC7F,OAAA;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,yEAC7C,EAAC,EAAAlF,qBAAA,GAAAQ,UAAU,CAACmB,IAAI,CAACE,SAAS,cAAA7B,qBAAA,uBAAzBA,qBAAA,CAA2ByF,MAAM,KAAI,CAAC,eAAC7F,OAAA;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,yEAC9C,EAAC,EAAAjF,qBAAA,GAAAO,UAAU,CAACmB,IAAI,CAACG,QAAQ,cAAA7B,qBAAA,uBAAxBA,qBAAA,CAA0BwF,MAAM,KAAI,CAAC;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtF,OAAA;QAAKiF,SAAS,EAAC,UAAU;QAAAC,QAAA,eACvBlF,OAAA;UAAKiF,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBlF,OAAA;YAAKiF,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BlF,OAAA;cAAIiF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNtF,OAAA;YAAKiF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlF,OAAA;cAAAkF,QAAA,EAAG;YAA8C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAErDtF,OAAA;cAAKiF,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBlF,OAAA;gBAAOiF,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChEtF,OAAA;gBACE8C,IAAI,EAAC,MAAM;gBACXgD,MAAM,EAAC,OAAO;gBACdC,QAAQ,EAAElC,gBAAiB;gBAC3BoB,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAEL1E,UAAU,iBACTZ,OAAA;cACEiF,SAAS,EAAC,iBAAiB;cAC3BQ,OAAO,EAAEhB,aAAc;cACvBiB,QAAQ,EAAEpF,OAAQ;cAClBiF,KAAK,EAAE;gBAAEI,KAAK,EAAE;cAAO,CAAE;cAAAT,QAAA,EAExB5E,OAAO,GAAG,mBAAmB,GAAG;YAA2B;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtF,OAAA;MAAKiF,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBlF,OAAA;QAAKiF,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BlF,OAAA;UAAIiF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eACNtF,OAAA;QAAKiF,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBlF,OAAA;UAAKiF,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBlF,OAAA;YAAKiF,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBlF,OAAA;cACEiF,SAAS,EAAC,iBAAiB;cAC3BQ,OAAO,EAAEZ,aAAc;cACvBa,QAAQ,EAAEpF,OAAQ;cAClBiF,KAAK,EAAE;gBAAEI,KAAK,EAAE;cAAO,CAAE;cAAAT,QAAA,EAC1B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNtF,OAAA;YAAKiF,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBlF,OAAA;cACEiF,SAAS,EAAC,cAAc;cACxBQ,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAI7E,UAAU,EAAE;kBACd,MAAMoF,OAAO,GAAGC,YAAY,CAACrF,UAAU,CAACmB,IAAI,CAAC;kBAC7CmE,WAAW,CAACF,OAAO,EAAE,qBAAqB,CAAC;gBAC7C,CAAC,MAAM;kBACLrF,QAAQ,CAAC,+BAA+B,CAAC;gBAC3C;cACF,CAAE;cACF4E,KAAK,EAAE;gBAAEI,KAAK,EAAE;cAAO,CAAE;cAAAT,QAAA,EAC1B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNtF,OAAA;YAAKiF,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBlF,OAAA;cACEiF,SAAS,EAAC,mBAAmB;cAC7BQ,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAI7E,UAAU,EAAE;kBACd4B,cAAc,CAAC,CAAC;gBAClB,CAAC,MAAM;kBACL7B,QAAQ,CAAC,+BAA+B,CAAC;gBAC3C;cACF,CAAE;cACF4E,KAAK,EAAE;gBAAEI,KAAK,EAAE;cAAO,CAAE;cAAAT,QAAA,EAC1B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtF,OAAA;MAAKiF,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBlF,OAAA;QAAKiF,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BlF,OAAA;UAAIiF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACNtF,OAAA;QAAKiF,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBlF,OAAA;UAAKiF,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBlF,OAAA;YAAKiF,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBlF,OAAA;cAAAkF,QAAA,EAAI;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3BtF,OAAA;cAAAkF,QAAA,gBACElF,OAAA;gBAAAkF,QAAA,EAAI;cAA8B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvCtF,OAAA;gBAAAkF,QAAA,EAAI;cAAiC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1CtF,OAAA;gBAAAkF,QAAA,EAAI;cAAsC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/CtF,OAAA;gBAAAkF,QAAA,EAAI;cAAqC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACNtF,OAAA;YAAKiF,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBlF,OAAA;cAAAkF,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBtF,OAAA;cAAAkF,QAAA,gBACElF,OAAA;gBAAAkF,QAAA,EAAI;cAA8C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvDtF,OAAA;gBAAAkF,QAAA,EAAI;cAA+C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxDtF,OAAA;gBAAAkF,QAAA,EAAI;cAAoC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7CtF,OAAA;gBAAAkF,QAAA,EAAI;cAAmD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAApF,EAAA,CAzTMD,MAAM;AAAAkG,EAAA,GAANlG,MAAM;AA0TZ,MAAMgG,YAAY,GAAIlE,IAAI,IAAK;EAC7B;EACA,IAAI,CAACA,IAAI,CAACC,SAAS,EAAE,OAAO,EAAE;EAE9B,MAAMoE,OAAO,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,mBAAmB,EAAE,SAAS,CAAC;EACnE,MAAMC,IAAI,GAAGtE,IAAI,CAACC,SAAS,CAACsE,GAAG,CAACC,QAAQ,IAAI,CAC1CA,QAAQ,CAACC,IAAI,EACbD,QAAQ,CAACE,KAAK,IAAI,EAAE,EACpBF,QAAQ,CAACG,KAAK,IAAI,EAAE,EACpBH,QAAQ,CAACI,OAAO,IAAI,EAAE,CACvB,CAAC;EAEF,OAAO,CAACP,OAAO,EAAE,GAAGC,IAAI,CAAC,CAACC,GAAG,CAACM,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;AAChE,CAAC;;AAED;AACA,MAAMX,WAAW,GAAGA,CAACF,OAAO,EAAEc,QAAQ,KAAK;EACzC,MAAM9B,IAAI,GAAG,IAAInC,IAAI,CAAC,CAACmD,OAAO,CAAC,EAAE;IAAElD,IAAI,EAAE;EAA0B,CAAC,CAAC;EACrE,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAAC+B,IAAI,CAAC;EACrC,MAAM9B,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;EACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;EACfG,IAAI,CAACI,QAAQ,GAAGwD,QAAQ;EACxB3D,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;EAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;EACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;EAC/BF,GAAG,CAACY,eAAe,CAACb,GAAG,CAAC;AAC1B,CAAC;AAED,eAAe9C,MAAM;AAAC,IAAAkG,EAAA;AAAAY,YAAA,CAAAZ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}