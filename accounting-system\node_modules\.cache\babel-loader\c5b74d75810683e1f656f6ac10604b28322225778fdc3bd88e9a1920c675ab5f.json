{"ast": null, "code": "var _jsxFileName = \"F:\\\\\\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\\\\accounting-system\\\\src\\\\components\\\\Customers.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Customers = () => {\n  _s();\n  const [customers, setCustomers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showForm, setShowForm] = useState(false);\n  const [editingCustomer, setEditingCustomer] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    phone: '',\n    email: '',\n    address: '',\n    tax_number: ''\n  });\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n  useEffect(() => {\n    fetchCustomers();\n  }, []);\n  const fetchCustomers = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/customers');\n      setCustomers(response.data);\n    } catch (error) {\n      setError('خطأ في جلب بيانات العملاء');\n      console.error('Error fetching customers:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      if (editingCustomer) {\n        await axios.put(`http://localhost:5000/api/customers/${editingCustomer.id}`, formData);\n        setMessage('تم تحديث العميل بنجاح');\n      } else {\n        await axios.post('http://localhost:5000/api/customers', formData);\n        setMessage('تم إضافة العميل بنجاح');\n      }\n      setFormData({\n        name: '',\n        phone: '',\n        email: '',\n        address: '',\n        tax_number: ''\n      });\n      setShowForm(false);\n      setEditingCustomer(null);\n      fetchCustomers();\n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      setError('خطأ في حفظ بيانات العميل');\n      console.error('Error saving customer:', error);\n    }\n  };\n  const handleEdit = customer => {\n    setEditingCustomer(customer);\n    setFormData({\n      name: customer.name,\n      phone: customer.phone || '',\n      email: customer.email || '',\n      address: customer.address || '',\n      tax_number: customer.tax_number || ''\n    });\n    setShowForm(true);\n  };\n  const handleDelete = async id => {\n    if (window.confirm('هل أنت متأكد من حذف هذا العميل؟')) {\n      try {\n        await axios.delete(`http://localhost:5000/api/customers/${id}`);\n        setMessage('تم حذف العميل بنجاح');\n        fetchCustomers();\n        setTimeout(() => setMessage(''), 3000);\n      } catch (error) {\n        setError('خطأ في حذف العميل');\n        console.error('Error deleting customer:', error);\n      }\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      phone: '',\n      email: '',\n      address: '',\n      tax_number: ''\n    });\n    setShowForm(false);\n    setEditingCustomer(null);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"page-title\",\n      children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 19\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"card-title\",\n            children: \"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: () => setShowForm(!showForm),\n            children: showForm ? 'إلغاء' : 'إضافة عميل جديد'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), showForm && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0639\\u0645\\u064A\\u0644 *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleInputChange,\n                  className: \"form-control\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"phone\",\n                  value: formData.phone,\n                  onChange: handleInputChange,\n                  className: \"form-control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  onChange: handleInputChange,\n                  className: \"form-control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"tax_number\",\n                  value: formData.tax_number,\n                  onChange: handleInputChange,\n                  className: \"form-control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  name: \"address\",\n                  value: formData.address,\n                  onChange: handleInputChange,\n                  className: \"form-control\",\n                  rows: \"3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-success\",\n              children: editingCustomer ? 'تحديث العميل' : 'إضافة العميل'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-danger\",\n              onClick: resetForm,\n              children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: customers.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0639\\u0645\\u0644\\u0627\\u0621 \\u0645\\u0633\\u062C\\u0644\\u064A\\u0646 \\u062D\\u062A\\u0649 \\u0627\\u0644\\u0622\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0627\\u0633\\u0645\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0631\\u0635\\u064A\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: customers.map(customer => {\n              var _customer$balance;\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: customer.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: customer.phone || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: customer.email || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: customer.tax_number || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [((_customer$balance = customer.balance) === null || _customer$balance === void 0 ? void 0 : _customer$balance.toLocaleString()) || '0', \" \\u062C.\\u0645\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-warning\",\n                    onClick: () => handleEdit(customer),\n                    style: {\n                      marginLeft: '0.5rem'\n                    },\n                    children: \"\\u062A\\u0639\\u062F\\u064A\\u0644\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-danger\",\n                    onClick: () => handleDelete(customer.id),\n                    children: \"\\u062D\\u0630\\u0641\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this)]\n              }, customer.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n};\n_s(Customers, \"k9siaLsj7ModhW6yD9r/k8RkNJ4=\");\n_c = Customers;\nexport default Customers;\nvar _c;\n$RefreshReg$(_c, \"Customers\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "Customers", "_s", "customers", "setCustomers", "loading", "setLoading", "showForm", "setShowForm", "editingCustomer", "setEditingCustomer", "formData", "setFormData", "name", "phone", "email", "address", "tax_number", "message", "setMessage", "error", "setError", "fetchCustomers", "response", "get", "data", "console", "handleInputChange", "e", "target", "value", "handleSubmit", "preventDefault", "put", "id", "post", "setTimeout", "handleEdit", "customer", "handleDelete", "window", "confirm", "delete", "resetForm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "justifyContent", "alignItems", "onClick", "onSubmit", "type", "onChange", "required", "rows", "gap", "length", "map", "_customer$balance", "balance", "toLocaleString", "marginLeft", "_c", "$RefreshReg$"], "sources": ["F:/القيمة المضافة/accounting-system/src/components/Customers.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst Customers = () => {\n  const [customers, setCustomers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showForm, setShowForm] = useState(false);\n  const [editingCustomer, setEditingCustomer] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    phone: '',\n    email: '',\n    address: '',\n    tax_number: ''\n  });\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    fetchCustomers();\n  }, []);\n\n  const fetchCustomers = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/customers');\n      setCustomers(response.data);\n    } catch (error) {\n      setError('خطأ في جلب بيانات العملاء');\n      console.error('Error fetching customers:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      if (editingCustomer) {\n        await axios.put(`http://localhost:5000/api/customers/${editingCustomer.id}`, formData);\n        setMessage('تم تحديث العميل بنجاح');\n      } else {\n        await axios.post('http://localhost:5000/api/customers', formData);\n        setMessage('تم إضافة العميل بنجاح');\n      }\n      \n      setFormData({\n        name: '',\n        phone: '',\n        email: '',\n        address: '',\n        tax_number: ''\n      });\n      setShowForm(false);\n      setEditingCustomer(null);\n      fetchCustomers();\n      \n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      setError('خطأ في حفظ بيانات العميل');\n      console.error('Error saving customer:', error);\n    }\n  };\n\n  const handleEdit = (customer) => {\n    setEditingCustomer(customer);\n    setFormData({\n      name: customer.name,\n      phone: customer.phone || '',\n      email: customer.email || '',\n      address: customer.address || '',\n      tax_number: customer.tax_number || ''\n    });\n    setShowForm(true);\n  };\n\n  const handleDelete = async (id) => {\n    if (window.confirm('هل أنت متأكد من حذف هذا العميل؟')) {\n      try {\n        await axios.delete(`http://localhost:5000/api/customers/${id}`);\n        setMessage('تم حذف العميل بنجاح');\n        fetchCustomers();\n        setTimeout(() => setMessage(''), 3000);\n      } catch (error) {\n        setError('خطأ في حذف العميل');\n        console.error('Error deleting customer:', error);\n      }\n    }\n  };\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      phone: '',\n      email: '',\n      address: '',\n      tax_number: ''\n    });\n    setShowForm(false);\n    setEditingCustomer(null);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"container\">\n        <div className=\"loading\">جاري تحميل بيانات العملاء...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container\">\n      <h1 className=\"page-title\">إدارة العملاء</h1>\n      \n      {message && <div className=\"success\">{message}</div>}\n      {error && <div className=\"error\">{error}</div>}\n      \n      <div className=\"card\">\n        <div className=\"card-header\">\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <h3 className=\"card-title\">قائمة العملاء</h3>\n            <button \n              className=\"btn btn-primary\"\n              onClick={() => setShowForm(!showForm)}\n            >\n              {showForm ? 'إلغاء' : 'إضافة عميل جديد'}\n            </button>\n          </div>\n        </div>\n        \n        {showForm && (\n          <div className=\"card-body\">\n            <form onSubmit={handleSubmit}>\n              <div className=\"row\">\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">اسم العميل *</label>\n                    <input\n                      type=\"text\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                      required\n                    />\n                  </div>\n                </div>\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">رقم الهاتف</label>\n                    <input\n                      type=\"text\"\n                      name=\"phone\"\n                      value={formData.phone}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                    />\n                  </div>\n                </div>\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">البريد الإلكتروني</label>\n                    <input\n                      type=\"email\"\n                      name=\"email\"\n                      value={formData.email}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                    />\n                  </div>\n                </div>\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">الرقم الضريبي</label>\n                    <input\n                      type=\"text\"\n                      name=\"tax_number\"\n                      value={formData.tax_number}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                    />\n                  </div>\n                </div>\n                <div className=\"col\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">العنوان</label>\n                    <textarea\n                      name=\"address\"\n                      value={formData.address}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                      rows=\"3\"\n                    ></textarea>\n                  </div>\n                </div>\n              </div>\n              <div style={{ display: 'flex', gap: '1rem' }}>\n                <button type=\"submit\" className=\"btn btn-success\">\n                  {editingCustomer ? 'تحديث العميل' : 'إضافة العميل'}\n                </button>\n                <button type=\"button\" className=\"btn btn-danger\" onClick={resetForm}>\n                  إلغاء\n                </button>\n              </div>\n            </form>\n          </div>\n        )}\n        \n        <div className=\"card-body\">\n          {customers.length === 0 ? (\n            <p>لا توجد عملاء مسجلين حتى الآن</p>\n          ) : (\n            <table className=\"table\">\n              <thead>\n                <tr>\n                  <th>الاسم</th>\n                  <th>الهاتف</th>\n                  <th>البريد الإلكتروني</th>\n                  <th>الرقم الضريبي</th>\n                  <th>الرصيد</th>\n                  <th>الإجراءات</th>\n                </tr>\n              </thead>\n              <tbody>\n                {customers.map(customer => (\n                  <tr key={customer.id}>\n                    <td>{customer.name}</td>\n                    <td>{customer.phone || '-'}</td>\n                    <td>{customer.email || '-'}</td>\n                    <td>{customer.tax_number || '-'}</td>\n                    <td>{customer.balance?.toLocaleString() || '0'} ج.م</td>\n                    <td>\n                      <button \n                        className=\"btn btn-warning\"\n                        onClick={() => handleEdit(customer)}\n                        style={{ marginLeft: '0.5rem' }}\n                      >\n                        تعديل\n                      </button>\n                      <button \n                        className=\"btn btn-danger\"\n                        onClick={() => handleDelete(customer.id)}\n                      >\n                        حذف\n                      </button>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Customers;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACa,eAAe,EAAEC,kBAAkB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC;IACvCiB,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACdyB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiB,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,qCAAqC,CAAC;MACvEpB,YAAY,CAACmB,QAAQ,CAACE,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,QAAQ,CAAC,2BAA2B,CAAC;MACrCK,OAAO,CAACN,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,iBAAiB,GAAIC,CAAC,IAAK;IAC/BhB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACiB,CAAC,CAACC,MAAM,CAAChB,IAAI,GAAGe,CAAC,CAACC,MAAM,CAACC;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,IAAIvB,eAAe,EAAE;QACnB,MAAMX,KAAK,CAACmC,GAAG,CAAC,uCAAuCxB,eAAe,CAACyB,EAAE,EAAE,EAAEvB,QAAQ,CAAC;QACtFQ,UAAU,CAAC,uBAAuB,CAAC;MACrC,CAAC,MAAM;QACL,MAAMrB,KAAK,CAACqC,IAAI,CAAC,qCAAqC,EAAExB,QAAQ,CAAC;QACjEQ,UAAU,CAAC,uBAAuB,CAAC;MACrC;MAEAP,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACXC,UAAU,EAAE;MACd,CAAC,CAAC;MACFT,WAAW,CAAC,KAAK,CAAC;MAClBE,kBAAkB,CAAC,IAAI,CAAC;MACxBY,cAAc,CAAC,CAAC;MAEhBc,UAAU,CAAC,MAAMjB,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,QAAQ,CAAC,0BAA0B,CAAC;MACpCK,OAAO,CAACN,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAMiB,UAAU,GAAIC,QAAQ,IAAK;IAC/B5B,kBAAkB,CAAC4B,QAAQ,CAAC;IAC5B1B,WAAW,CAAC;MACVC,IAAI,EAAEyB,QAAQ,CAACzB,IAAI;MACnBC,KAAK,EAAEwB,QAAQ,CAACxB,KAAK,IAAI,EAAE;MAC3BC,KAAK,EAAEuB,QAAQ,CAACvB,KAAK,IAAI,EAAE;MAC3BC,OAAO,EAAEsB,QAAQ,CAACtB,OAAO,IAAI,EAAE;MAC/BC,UAAU,EAAEqB,QAAQ,CAACrB,UAAU,IAAI;IACrC,CAAC,CAAC;IACFT,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAM+B,YAAY,GAAG,MAAOL,EAAE,IAAK;IACjC,IAAIM,MAAM,CAACC,OAAO,CAAC,iCAAiC,CAAC,EAAE;MACrD,IAAI;QACF,MAAM3C,KAAK,CAAC4C,MAAM,CAAC,uCAAuCR,EAAE,EAAE,CAAC;QAC/Df,UAAU,CAAC,qBAAqB,CAAC;QACjCG,cAAc,CAAC,CAAC;QAChBc,UAAU,CAAC,MAAMjB,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,QAAQ,CAAC,mBAAmB,CAAC;QAC7BK,OAAO,CAACN,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAClD;IACF;EACF,CAAC;EAED,MAAMuB,SAAS,GAAGA,CAAA,KAAM;IACtB/B,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE;IACd,CAAC,CAAC;IACFT,WAAW,CAAC,KAAK,CAAC;IAClBE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,IAAIL,OAAO,EAAE;IACX,oBACEL,OAAA;MAAK4C,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxB7C,OAAA;QAAK4C,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAA4B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC;EAEV;EAEA,oBACEjD,OAAA;IAAK4C,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB7C,OAAA;MAAI4C,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAE5C/B,OAAO,iBAAIlB,OAAA;MAAK4C,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAE3B;IAAO;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACnD7B,KAAK,iBAAIpB,OAAA;MAAK4C,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAEzB;IAAK;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE9CjD,OAAA;MAAK4C,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB7C,OAAA;QAAK4C,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1B7C,OAAA;UAAKkD,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAR,QAAA,gBACrF7C,OAAA;YAAI4C,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7CjD,OAAA;YACE4C,SAAS,EAAC,iBAAiB;YAC3BU,OAAO,EAAEA,CAAA,KAAM9C,WAAW,CAAC,CAACD,QAAQ,CAAE;YAAAsC,QAAA,EAErCtC,QAAQ,GAAG,OAAO,GAAG;UAAiB;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL1C,QAAQ,iBACPP,OAAA;QAAK4C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB7C,OAAA;UAAMuD,QAAQ,EAAExB,YAAa;UAAAc,QAAA,gBAC3B7C,OAAA;YAAK4C,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB7C,OAAA;cAAK4C,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvB7C,OAAA;gBAAK4C,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7C,OAAA;kBAAO4C,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDjD,OAAA;kBACEwD,IAAI,EAAC,MAAM;kBACX3C,IAAI,EAAC,MAAM;kBACXiB,KAAK,EAAEnB,QAAQ,CAACE,IAAK;kBACrB4C,QAAQ,EAAE9B,iBAAkB;kBAC5BiB,SAAS,EAAC,cAAc;kBACxBc,QAAQ;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjD,OAAA;cAAK4C,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvB7C,OAAA;gBAAK4C,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7C,OAAA;kBAAO4C,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChDjD,OAAA;kBACEwD,IAAI,EAAC,MAAM;kBACX3C,IAAI,EAAC,OAAO;kBACZiB,KAAK,EAAEnB,QAAQ,CAACG,KAAM;kBACtB2C,QAAQ,EAAE9B,iBAAkB;kBAC5BiB,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjD,OAAA;cAAK4C,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvB7C,OAAA;gBAAK4C,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7C,OAAA;kBAAO4C,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvDjD,OAAA;kBACEwD,IAAI,EAAC,OAAO;kBACZ3C,IAAI,EAAC,OAAO;kBACZiB,KAAK,EAAEnB,QAAQ,CAACI,KAAM;kBACtB0C,QAAQ,EAAE9B,iBAAkB;kBAC5BiB,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjD,OAAA;cAAK4C,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvB7C,OAAA;gBAAK4C,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7C,OAAA;kBAAO4C,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnDjD,OAAA;kBACEwD,IAAI,EAAC,MAAM;kBACX3C,IAAI,EAAC,YAAY;kBACjBiB,KAAK,EAAEnB,QAAQ,CAACM,UAAW;kBAC3BwC,QAAQ,EAAE9B,iBAAkB;kBAC5BiB,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNjD,OAAA;cAAK4C,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClB7C,OAAA;gBAAK4C,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7C,OAAA;kBAAO4C,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7CjD,OAAA;kBACEa,IAAI,EAAC,SAAS;kBACdiB,KAAK,EAAEnB,QAAQ,CAACK,OAAQ;kBACxByC,QAAQ,EAAE9B,iBAAkB;kBAC5BiB,SAAS,EAAC,cAAc;kBACxBe,IAAI,EAAC;gBAAG;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNjD,OAAA;YAAKkD,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAES,GAAG,EAAE;YAAO,CAAE;YAAAf,QAAA,gBAC3C7C,OAAA;cAAQwD,IAAI,EAAC,QAAQ;cAACZ,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC9CpC,eAAe,GAAG,cAAc,GAAG;YAAc;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACTjD,OAAA;cAAQwD,IAAI,EAAC,QAAQ;cAACZ,SAAS,EAAC,gBAAgB;cAACU,OAAO,EAAEX,SAAU;cAAAE,QAAA,EAAC;YAErE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,eAEDjD,OAAA;QAAK4C,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB1C,SAAS,CAAC0D,MAAM,KAAK,CAAC,gBACrB7D,OAAA;UAAA6C,QAAA,EAAG;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAEpCjD,OAAA;UAAO4C,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACtB7C,OAAA;YAAA6C,QAAA,eACE7C,OAAA;cAAA6C,QAAA,gBACE7C,OAAA;gBAAA6C,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdjD,OAAA;gBAAA6C,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfjD,OAAA;gBAAA6C,QAAA,EAAI;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1BjD,OAAA;gBAAA6C,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtBjD,OAAA;gBAAA6C,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfjD,OAAA;gBAAA6C,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRjD,OAAA;YAAA6C,QAAA,EACG1C,SAAS,CAAC2D,GAAG,CAACxB,QAAQ;cAAA,IAAAyB,iBAAA;cAAA,oBACrB/D,OAAA;gBAAA6C,QAAA,gBACE7C,OAAA;kBAAA6C,QAAA,EAAKP,QAAQ,CAACzB;gBAAI;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxBjD,OAAA;kBAAA6C,QAAA,EAAKP,QAAQ,CAACxB,KAAK,IAAI;gBAAG;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChCjD,OAAA;kBAAA6C,QAAA,EAAKP,QAAQ,CAACvB,KAAK,IAAI;gBAAG;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChCjD,OAAA;kBAAA6C,QAAA,EAAKP,QAAQ,CAACrB,UAAU,IAAI;gBAAG;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrCjD,OAAA;kBAAA6C,QAAA,GAAK,EAAAkB,iBAAA,GAAAzB,QAAQ,CAAC0B,OAAO,cAAAD,iBAAA,uBAAhBA,iBAAA,CAAkBE,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,gBAAI;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxDjD,OAAA;kBAAA6C,QAAA,gBACE7C,OAAA;oBACE4C,SAAS,EAAC,iBAAiB;oBAC3BU,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAACC,QAAQ,CAAE;oBACpCY,KAAK,EAAE;sBAAEgB,UAAU,EAAE;oBAAS,CAAE;oBAAArB,QAAA,EACjC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTjD,OAAA;oBACE4C,SAAS,EAAC,gBAAgB;oBAC1BU,OAAO,EAAEA,CAAA,KAAMf,YAAY,CAACD,QAAQ,CAACJ,EAAE,CAAE;oBAAAW,QAAA,EAC1C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GApBEX,QAAQ,CAACJ,EAAE;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAqBhB,CAAC;YAAA,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/C,EAAA,CAnQID,SAAS;AAAAkE,EAAA,GAATlE,SAAS;AAqQf,eAAeA,SAAS;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}