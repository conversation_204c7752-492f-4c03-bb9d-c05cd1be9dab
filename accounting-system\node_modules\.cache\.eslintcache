[{"F:\\القيمة المضافة\\accounting-system\\src\\index.js": "1", "F:\\القيمة المضافة\\accounting-system\\src\\reportWebVitals.js": "2", "F:\\القيمة المضافة\\accounting-system\\src\\App.js": "3", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Customers.js": "4", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Dashboard.js": "5", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Navbar.js": "6", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Products.js": "7", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Treasury.js": "8", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Expenses.js": "9", "F:\\القيمة المضافة\\accounting-system\\src\\components\\PurchaseInvoices.js": "10", "F:\\القيمة المضافة\\accounting-system\\src\\components\\SalesInvoices.js": "11", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Suppliers.js": "12", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Reports.js": "13"}, {"size": 535, "mtime": *************, "results": "14", "hashOfConfig": "15"}, {"size": 362, "mtime": *************, "results": "16", "hashOfConfig": "15"}, {"size": 1436, "mtime": *************, "results": "17", "hashOfConfig": "15"}, {"size": 8670, "mtime": *************, "results": "18", "hashOfConfig": "15"}, {"size": 5033, "mtime": *************, "results": "19", "hashOfConfig": "15"}, {"size": 1829, "mtime": *************, "results": "20", "hashOfConfig": "15"}, {"size": 8916, "mtime": *************, "results": "21", "hashOfConfig": "15"}, {"size": 5002, "mtime": *************, "results": "22", "hashOfConfig": "15"}, {"size": 11749, "mtime": *************, "results": "23", "hashOfConfig": "15"}, {"size": 3483, "mtime": *************, "results": "24", "hashOfConfig": "15"}, {"size": 3477, "mtime": *************, "results": "25", "hashOfConfig": "15"}, {"size": 8680, "mtime": *************, "results": "26", "hashOfConfig": "15"}, {"size": 11642, "mtime": 1751801311029, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1n1uasy", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\القيمة المضافة\\accounting-system\\src\\index.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\reportWebVitals.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\App.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Customers.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Dashboard.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Navbar.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Products.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Treasury.js", ["67"], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Expenses.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\PurchaseInvoices.js", ["68"], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\SalesInvoices.js", ["69"], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Suppliers.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Reports.js", [], [], {"ruleId": "70", "severity": 1, "message": "71", "line": 8, "column": 19, "nodeType": "72", "messageId": "73", "endLine": 8, "endColumn": 29}, {"ruleId": "70", "severity": 1, "message": "71", "line": 7, "column": 19, "nodeType": "72", "messageId": "73", "endLine": 7, "endColumn": 29}, {"ruleId": "70", "severity": 1, "message": "71", "line": 7, "column": 19, "nodeType": "72", "messageId": "73", "endLine": 7, "endColumn": 29}, "no-unused-vars", "'setMessage' is assigned a value but never used.", "Identifier", "unusedVar"]