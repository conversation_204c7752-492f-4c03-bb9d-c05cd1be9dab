[{"F:\\القيمة المضافة\\accounting-system\\src\\index.js": "1", "F:\\القيمة المضافة\\accounting-system\\src\\reportWebVitals.js": "2", "F:\\القيمة المضافة\\accounting-system\\src\\App.js": "3", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Customers.js": "4", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Dashboard.js": "5", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Navbar.js": "6", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Products.js": "7", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Treasury.js": "8", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Expenses.js": "9", "F:\\القيمة المضافة\\accounting-system\\src\\components\\PurchaseInvoices.js": "10", "F:\\القيمة المضافة\\accounting-system\\src\\components\\SalesInvoices.js": "11", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Suppliers.js": "12", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Reports.js": "13", "F:\\القيمة المضافة\\accounting-system\\src\\components\\AddSalesInvoice.js": "14", "F:\\القيمة المضافة\\accounting-system\\src\\components\\AddPurchaseInvoice.js": "15", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Charts.js": "16", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Inventory.js": "17", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Backup.js": "18", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Settings.js": "19", "F:\\القيمة المضافة\\accounting-system\\src\\components\\ImportExcel.js": "20", "F:\\القيمة المضافة\\accounting-system\\src\\components\\ImportPDF.js": "21", "F:\\القيمة المضافة\\accounting-system\\src\\components\\TemplateGenerator.js": "22", "F:\\القيمة المضافة\\accounting-system\\src\\components\\DownloadCenter.js": "23"}, {"size": 535, "mtime": *************, "results": "24", "hashOfConfig": "25"}, {"size": 362, "mtime": *************, "results": "26", "hashOfConfig": "25"}, {"size": 1889, "mtime": *************, "results": "27", "hashOfConfig": "25"}, {"size": 10221, "mtime": *************, "results": "28", "hashOfConfig": "25"}, {"size": 5033, "mtime": *************, "results": "29", "hashOfConfig": "25"}, {"size": 2443, "mtime": *************, "results": "30", "hashOfConfig": "25"}, {"size": 9729, "mtime": *************, "results": "31", "hashOfConfig": "25"}, {"size": 5002, "mtime": *************, "results": "32", "hashOfConfig": "25"}, {"size": 11749, "mtime": *************, "results": "33", "hashOfConfig": "25"}, {"size": 4121, "mtime": *************, "results": "34", "hashOfConfig": "25"}, {"size": 4106, "mtime": *************, "results": "35", "hashOfConfig": "25"}, {"size": 9495, "mtime": *************, "results": "36", "hashOfConfig": "25"}, {"size": 15274, "mtime": 1751802248961, "results": "37", "hashOfConfig": "25"}, {"size": 14856, "mtime": 1751801950657, "results": "38", "hashOfConfig": "25"}, {"size": 14657, "mtime": 1751802075041, "results": "39", "hashOfConfig": "25"}, {"size": 7102, "mtime": 1751802202745, "results": "40", "hashOfConfig": "25"}, {"size": 12566, "mtime": 1751802298967, "results": "41", "hashOfConfig": "25"}, {"size": 12439, "mtime": 1751802481089, "results": "42", "hashOfConfig": "25"}, {"size": 24387, "mtime": 1751803021716, "results": "43", "hashOfConfig": "25"}, {"size": 12906, "mtime": 1751803791105, "results": "44", "hashOfConfig": "25"}, {"size": 12261, "mtime": 1751803510707, "results": "45", "hashOfConfig": "25"}, {"size": 10994, "mtime": 1751803738860, "results": "46", "hashOfConfig": "25"}, {"size": 10771, "mtime": 1751803842753, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1n1uasy", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "F:\\القيمة المضافة\\accounting-system\\src\\index.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\reportWebVitals.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\App.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Customers.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Dashboard.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Navbar.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Products.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Treasury.js", ["117"], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Expenses.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\PurchaseInvoices.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\SalesInvoices.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Suppliers.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Reports.js", ["118", "119"], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\AddSalesInvoice.js", ["120"], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\AddPurchaseInvoice.js", ["121"], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Charts.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Inventory.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Backup.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Settings.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\ImportExcel.js", ["122"], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\ImportPDF.js", ["123", "124", "125"], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\TemplateGenerator.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\DownloadCenter.js", ["126", "127", "128", "129"], [], {"ruleId": "130", "severity": 1, "message": "131", "line": 8, "column": 19, "nodeType": "132", "messageId": "133", "endLine": 8, "endColumn": 29}, {"ruleId": "130", "severity": 1, "message": "134", "line": 8, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 8, "endColumn": 20}, {"ruleId": "130", "severity": 1, "message": "135", "line": 9, "column": 3, "nodeType": "132", "messageId": "133", "endLine": 9, "endColumn": 26}, {"ruleId": "136", "severity": 1, "message": "137", "line": 36, "column": 6, "nodeType": "138", "endLine": 36, "endColumn": 65, "suggestions": "139"}, {"ruleId": "136", "severity": 1, "message": "137", "line": 36, "column": 6, "nodeType": "138", "endLine": 36, "endColumn": 65, "suggestions": "140"}, {"ruleId": "130", "severity": 1, "message": "141", "line": 8, "column": 10, "nodeType": "132", "messageId": "133", "endLine": 8, "endColumn": 14}, {"ruleId": "130", "severity": 1, "message": "141", "line": 6, "column": 10, "nodeType": "132", "messageId": "133", "endLine": 6, "endColumn": 14}, {"ruleId": "130", "severity": 1, "message": "142", "line": 88, "column": 11, "nodeType": "132", "messageId": "133", "endLine": 88, "endColumn": 19}, {"ruleId": "130", "severity": 1, "message": "143", "line": 168, "column": 9, "nodeType": "132", "messageId": "133", "endLine": 168, "endColumn": 14}, {"ruleId": "144", "severity": 1, "message": "145", "line": 258, "column": 21, "nodeType": "146", "endLine": 258, "endColumn": 62}, {"ruleId": "144", "severity": 1, "message": "145", "line": 259, "column": 21, "nodeType": "146", "endLine": 259, "endColumn": 62}, {"ruleId": "144", "severity": 1, "message": "145", "line": 260, "column": 21, "nodeType": "146", "endLine": 260, "endColumn": 62}, {"ruleId": "144", "severity": 1, "message": "145", "line": 261, "column": 21, "nodeType": "146", "endLine": 261, "endColumn": 62}, "no-unused-vars", "'setMessage' is assigned a value but never used.", "Identifier", "unusedVar", "'TopCustomersChart' is defined but never used.", "'ProductPerformanceChart' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'calculateTotals'. Either include it or remove the dependency array.", "ArrayExpression", ["147"], ["148"], "'file' is assigned a value but never used.", "'patterns' is assigned a value but never used.", "'items' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", {"desc": "149", "fix": "150"}, {"desc": "149", "fix": "151"}, "Update the dependencies array to be: [invoiceItems, formData.tax_rate, formData.discount_amount, calculateTotals]", {"range": "152", "text": "153"}, {"range": "154", "text": "153"}, [995, 1054], "[invoiceItems, formData.tax_rate, formData.discount_amount, calculateTotals]", [972, 1031]]