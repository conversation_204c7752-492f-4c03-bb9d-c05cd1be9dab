[{"F:\\القيمة المضافة\\accounting-system\\src\\index.js": "1", "F:\\القيمة المضافة\\accounting-system\\src\\reportWebVitals.js": "2", "F:\\القيمة المضافة\\accounting-system\\src\\App.js": "3", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Customers.js": "4", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Dashboard.js": "5", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Navbar.js": "6", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Products.js": "7", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Treasury.js": "8", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Expenses.js": "9", "F:\\القيمة المضافة\\accounting-system\\src\\components\\PurchaseInvoices.js": "10", "F:\\القيمة المضافة\\accounting-system\\src\\components\\SalesInvoices.js": "11", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Suppliers.js": "12", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Reports.js": "13", "F:\\القيمة المضافة\\accounting-system\\src\\components\\AddSalesInvoice.js": "14", "F:\\القيمة المضافة\\accounting-system\\src\\components\\AddPurchaseInvoice.js": "15", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Charts.js": "16", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Inventory.js": "17", "F:\\القيمة المضافة\\accounting-system\\src\\components\\Backup.js": "18"}, {"size": 535, "mtime": *************, "results": "19", "hashOfConfig": "20"}, {"size": 362, "mtime": *************, "results": "21", "hashOfConfig": "20"}, {"size": 1648, "mtime": *************, "results": "22", "hashOfConfig": "20"}, {"size": 8670, "mtime": *************, "results": "23", "hashOfConfig": "20"}, {"size": 5033, "mtime": *************, "results": "24", "hashOfConfig": "20"}, {"size": 2128, "mtime": *************, "results": "25", "hashOfConfig": "20"}, {"size": 8916, "mtime": *************, "results": "26", "hashOfConfig": "20"}, {"size": 5002, "mtime": *************, "results": "27", "hashOfConfig": "20"}, {"size": 11749, "mtime": *************, "results": "28", "hashOfConfig": "20"}, {"size": 4121, "mtime": *************, "results": "29", "hashOfConfig": "20"}, {"size": 4106, "mtime": *************, "results": "30", "hashOfConfig": "20"}, {"size": 8680, "mtime": *************, "results": "31", "hashOfConfig": "20"}, {"size": 15274, "mtime": 1751802248961, "results": "32", "hashOfConfig": "20"}, {"size": 14856, "mtime": 1751801950657, "results": "33", "hashOfConfig": "20"}, {"size": 14657, "mtime": 1751802075041, "results": "34", "hashOfConfig": "20"}, {"size": 7102, "mtime": 1751802202745, "results": "35", "hashOfConfig": "20"}, {"size": 12566, "mtime": 1751802298967, "results": "36", "hashOfConfig": "20"}, {"size": 12439, "mtime": 1751802481089, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1n1uasy", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\القيمة المضافة\\accounting-system\\src\\index.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\reportWebVitals.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\App.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Customers.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Dashboard.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Navbar.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Products.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Treasury.js", ["92"], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Expenses.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\PurchaseInvoices.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\SalesInvoices.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Suppliers.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Reports.js", ["93", "94"], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\AddSalesInvoice.js", ["95"], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\AddPurchaseInvoice.js", ["96"], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Charts.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Inventory.js", [], [], "F:\\القيمة المضافة\\accounting-system\\src\\components\\Backup.js", [], [], {"ruleId": "97", "severity": 1, "message": "98", "line": 8, "column": 19, "nodeType": "99", "messageId": "100", "endLine": 8, "endColumn": 29}, {"ruleId": "97", "severity": 1, "message": "101", "line": 8, "column": 3, "nodeType": "99", "messageId": "100", "endLine": 8, "endColumn": 20}, {"ruleId": "97", "severity": 1, "message": "102", "line": 9, "column": 3, "nodeType": "99", "messageId": "100", "endLine": 9, "endColumn": 26}, {"ruleId": "103", "severity": 1, "message": "104", "line": 36, "column": 6, "nodeType": "105", "endLine": 36, "endColumn": 65, "suggestions": "106"}, {"ruleId": "103", "severity": 1, "message": "104", "line": 36, "column": 6, "nodeType": "105", "endLine": 36, "endColumn": 65, "suggestions": "107"}, "no-unused-vars", "'setMessage' is assigned a value but never used.", "Identifier", "unusedVar", "'TopCustomersChart' is defined but never used.", "'ProductPerformanceChart' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'calculateTotals'. Either include it or remove the dependency array.", "ArrayExpression", ["108"], ["109"], {"desc": "110", "fix": "111"}, {"desc": "110", "fix": "112"}, "Update the dependencies array to be: [invoiceItems, formData.tax_rate, formData.discount_amount, calculateTotals]", {"range": "113", "text": "114"}, {"range": "115", "text": "114"}, [995, 1054], "[invoiceItems, formData.tax_rate, formData.discount_amount, calculateTotals]", [972, 1031]]