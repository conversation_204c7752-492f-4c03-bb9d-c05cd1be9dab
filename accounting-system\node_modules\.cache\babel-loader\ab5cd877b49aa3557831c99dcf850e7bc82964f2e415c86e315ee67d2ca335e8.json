{"ast": null, "code": "var _jsxFileName = \"F:\\\\\\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\\\\accounting-system\\\\src\\\\components\\\\Inventory.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Inventory = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showAdjustForm, setShowAdjustForm] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [adjustmentData, setAdjustmentData] = useState({\n    adjustment_type: 'add',\n    // add, subtract, set\n    quantity: 0,\n    reason: '',\n    notes: ''\n  });\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/products');\n      setProducts(response.data);\n    } catch (error) {\n      setError('خطأ في جلب بيانات المخزون');\n      console.error('Error fetching products:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAdjustStock = product => {\n    setSelectedProduct(product);\n    setAdjustmentData({\n      adjustment_type: 'add',\n      quantity: 0,\n      reason: '',\n      notes: ''\n    });\n    setShowAdjustForm(true);\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setAdjustmentData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmitAdjustment = async e => {\n    e.preventDefault();\n    try {\n      let newQuantity = selectedProduct.stock_quantity;\n      switch (adjustmentData.adjustment_type) {\n        case 'add':\n          newQuantity += parseInt(adjustmentData.quantity);\n          break;\n        case 'subtract':\n          newQuantity -= parseInt(adjustmentData.quantity);\n          break;\n        case 'set':\n          newQuantity = parseInt(adjustmentData.quantity);\n          break;\n        default:\n          break;\n      }\n\n      // تحديث كمية المنتج\n      await axios.put(`http://localhost:5000/api/products/${selectedProduct.id}`, {\n        ...selectedProduct,\n        stock_quantity: Math.max(0, newQuantity)\n      });\n\n      // تسجيل حركة المخزون\n      await axios.post('http://localhost:5000/api/inventory-movements', {\n        product_id: selectedProduct.id,\n        movement_type: adjustmentData.adjustment_type,\n        quantity: adjustmentData.quantity,\n        old_quantity: selectedProduct.stock_quantity,\n        new_quantity: Math.max(0, newQuantity),\n        reason: adjustmentData.reason,\n        notes: adjustmentData.notes\n      });\n      setMessage('تم تعديل المخزون بنجاح');\n      setShowAdjustForm(false);\n      fetchProducts();\n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      setError('خطأ في تعديل المخزون');\n      console.error('Error adjusting stock:', error);\n    }\n  };\n  const getStockStatus = quantity => {\n    if (quantity === 0) return {\n      status: 'نفد المخزون',\n      color: 'red'\n    };\n    if (quantity <= 10) return {\n      status: 'مخزون منخفض',\n      color: 'orange'\n    };\n    if (quantity <= 50) return {\n      status: 'مخزون متوسط',\n      color: 'blue'\n    };\n    return {\n      status: 'مخزون جيد',\n      color: 'green'\n    };\n  };\n  const getTotalInventoryValue = () => {\n    return products.reduce((total, product) => {\n      return total + product.stock_quantity * (product.cost || 0);\n    }, 0);\n  };\n  const getLowStockProducts = () => {\n    return products.filter(product => product.stock_quantity <= 10);\n  };\n  const getOutOfStockProducts = () => {\n    return products.filter(product => product.stock_quantity === 0);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"page-title\",\n      children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 19\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-stats\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-number\",\n          children: products.length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        style: {\n          background: 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-number\",\n          children: getTotalInventoryValue().toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646 (\\u062C.\\u0645)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        style: {\n          background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-number\",\n          children: getLowStockProducts().length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0645\\u062E\\u0632\\u0648\\u0646 \\u0645\\u0646\\u062E\\u0641\\u0636\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stat-card\",\n        style: {\n          background: 'linear-gradient(135deg, #f44336 0%, #da190b 100%)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-number\",\n          children: getOutOfStockProducts().length\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-label\",\n          children: \"\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0646\\u0641\\u062F \\u0645\\u062E\\u0632\\u0648\\u0646\\u0647\\u0627\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), (getLowStockProducts().length > 0 || getOutOfStockProducts().length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\u26A0\\uFE0F \\u062A\\u0646\\u0628\\u064A\\u0647\\u0627\\u062A \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: [getOutOfStockProducts().length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert alert-danger\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0646\\u0641\\u062F \\u0645\\u062E\\u0632\\u0648\\u0646\\u0647\\u0627:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 17\n          }, this), \" \", getOutOfStockProducts().map(p => p.name).join(', ')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 15\n        }, this), getLowStockProducts().length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"alert alert-warning\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0645\\u062E\\u0632\\u0648\\u0646 \\u0645\\u0646\\u062E\\u0641\\u0636:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 17\n          }, this), \" \", getLowStockProducts().map(p => `${p.name} (${p.stock_quantity})`).join(', ')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: products.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0641\\u064A \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0648\\u062D\\u062F\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629 \\u0627\\u0644\\u0645\\u062A\\u0627\\u062D\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u062A\\u0643\\u0644\\u0641\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: products.map(product => {\n              const stockStatus = getStockStatus(product.stock_quantity);\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: product.unit || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontWeight: 'bold',\n                      color: stockStatus.color\n                    },\n                    children: product.stock_quantity\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: stockStatus.color\n                    },\n                    children: stockStatus.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [(product.cost || 0).toLocaleString(), \" \\u062C.\\u0645\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [((product.stock_quantity || 0) * (product.cost || 0)).toLocaleString(), \" \\u062C.\\u0645\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-primary\",\n                    onClick: () => handleAdjustStock(product),\n                    children: \"\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 23\n                }, this)]\n              }, product.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), showAdjustForm && selectedProduct && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0,0,0,0.5)',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        zIndex: 1000\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        style: {\n          backgroundColor: 'white',\n          borderRadius: '10px',\n          padding: '2rem',\n          maxWidth: '500px',\n          width: '90%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0645\\u062E\\u0632\\u0648\\u0646: \", selectedProduct.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAdjustForm(false),\n            className: \"btn btn-danger\",\n            children: \"\\u2715\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1rem',\n            padding: '1rem',\n            backgroundColor: '#f8f9fa',\n            borderRadius: '5px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [\"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629 \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\\u0629: \", selectedProduct.stock_quantity]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmitAdjustment,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0646\\u0648\\u0639 \\u0627\\u0644\\u062A\\u0639\\u062F\\u064A\\u0644\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"adjustment_type\",\n              value: adjustmentData.adjustment_type,\n              onChange: handleInputChange,\n              className: \"form-control\",\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"add\",\n                children: \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0643\\u0645\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"subtract\",\n                children: \"\\u062E\\u0635\\u0645 \\u0643\\u0645\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"set\",\n                children: \"\\u062A\\u062D\\u062F\\u064A\\u062F \\u0643\\u0645\\u064A\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: \"quantity\",\n              value: adjustmentData.quantity,\n              onChange: handleInputChange,\n              className: \"form-control\",\n              min: \"0\",\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u0633\\u0628\\u0628\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"reason\",\n              value: adjustmentData.reason,\n              onChange: handleInputChange,\n              className: \"form-control\",\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0633\\u0628\\u0628\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"purchase\",\n                children: \"\\u0634\\u0631\\u0627\\u0621 \\u062C\\u062F\\u064A\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"sale\",\n                children: \"\\u0628\\u064A\\u0639\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"damage\",\n                children: \"\\u062A\\u0644\\u0641\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"theft\",\n                children: \"\\u0641\\u0642\\u062F\\u0627\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"return\",\n                children: \"\\u0645\\u0631\\u062A\\u062C\\u0639\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"adjustment\",\n                children: \"\\u062A\\u0633\\u0648\\u064A\\u0629 \\u062C\\u0631\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"other\",\n                children: \"\\u0623\\u062E\\u0631\\u0649\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              name: \"notes\",\n              value: adjustmentData.notes,\n              onChange: handleInputChange,\n              className: \"form-control\",\n              rows: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '1rem',\n              justifyContent: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-success\",\n              children: \"\\u062A\\u0623\\u0643\\u064A\\u062F \\u0627\\u0644\\u062A\\u0639\\u062F\\u064A\\u0644\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: () => setShowAdjustForm(false),\n              className: \"btn btn-danger\",\n              children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 130,\n    columnNumber: 5\n  }, this);\n};\n_s(Inventory, \"rQRMqhCV4ghCyMf6+RKM1j0cXLo=\");\n_c = Inventory;\nexport default Inventory;\nvar _c;\n$RefreshReg$(_c, \"Inventory\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "Inventory", "_s", "products", "setProducts", "loading", "setLoading", "showAdjustForm", "setShowAdjustForm", "selectedProduct", "setSelectedProduct", "adjustmentData", "setAdjustmentData", "adjustment_type", "quantity", "reason", "notes", "message", "setMessage", "error", "setError", "fetchProducts", "response", "get", "data", "console", "handleAdjustStock", "product", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmitAdjustment", "preventDefault", "newQuantity", "stock_quantity", "parseInt", "put", "id", "Math", "max", "post", "product_id", "movement_type", "old_quantity", "new_quantity", "setTimeout", "getStockStatus", "status", "color", "getTotalInventoryValue", "reduce", "total", "cost", "getLowStockProducts", "filter", "getOutOfStockProducts", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "style", "background", "toLocaleString", "map", "p", "join", "stockStatus", "unit", "fontWeight", "onClick", "position", "top", "left", "right", "bottom", "backgroundColor", "display", "justifyContent", "alignItems", "zIndex", "borderRadius", "padding", "max<PERSON><PERSON><PERSON>", "width", "marginBottom", "onSubmit", "onChange", "required", "type", "min", "rows", "gap", "_c", "$RefreshReg$"], "sources": ["F:/القيمة المضافة/accounting-system/src/components/Inventory.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst Inventory = () => {\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showAdjustForm, setShowAdjustForm] = useState(false);\n  const [selectedProduct, setSelectedProduct] = useState(null);\n  const [adjustmentData, setAdjustmentData] = useState({\n    adjustment_type: 'add', // add, subtract, set\n    quantity: 0,\n    reason: '',\n    notes: ''\n  });\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/products');\n      setProducts(response.data);\n    } catch (error) {\n      setError('خطأ في جلب بيانات المخزون');\n      console.error('Error fetching products:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAdjustStock = (product) => {\n    setSelectedProduct(product);\n    setAdjustmentData({\n      adjustment_type: 'add',\n      quantity: 0,\n      reason: '',\n      notes: ''\n    });\n    setShowAdjustForm(true);\n  };\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setAdjustmentData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmitAdjustment = async (e) => {\n    e.preventDefault();\n    try {\n      let newQuantity = selectedProduct.stock_quantity;\n      \n      switch (adjustmentData.adjustment_type) {\n        case 'add':\n          newQuantity += parseInt(adjustmentData.quantity);\n          break;\n        case 'subtract':\n          newQuantity -= parseInt(adjustmentData.quantity);\n          break;\n        case 'set':\n          newQuantity = parseInt(adjustmentData.quantity);\n          break;\n        default:\n          break;\n      }\n\n      // تحديث كمية المنتج\n      await axios.put(`http://localhost:5000/api/products/${selectedProduct.id}`, {\n        ...selectedProduct,\n        stock_quantity: Math.max(0, newQuantity)\n      });\n\n      // تسجيل حركة المخزون\n      await axios.post('http://localhost:5000/api/inventory-movements', {\n        product_id: selectedProduct.id,\n        movement_type: adjustmentData.adjustment_type,\n        quantity: adjustmentData.quantity,\n        old_quantity: selectedProduct.stock_quantity,\n        new_quantity: Math.max(0, newQuantity),\n        reason: adjustmentData.reason,\n        notes: adjustmentData.notes\n      });\n\n      setMessage('تم تعديل المخزون بنجاح');\n      setShowAdjustForm(false);\n      fetchProducts();\n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      setError('خطأ في تعديل المخزون');\n      console.error('Error adjusting stock:', error);\n    }\n  };\n\n  const getStockStatus = (quantity) => {\n    if (quantity === 0) return { status: 'نفد المخزون', color: 'red' };\n    if (quantity <= 10) return { status: 'مخزون منخفض', color: 'orange' };\n    if (quantity <= 50) return { status: 'مخزون متوسط', color: 'blue' };\n    return { status: 'مخزون جيد', color: 'green' };\n  };\n\n  const getTotalInventoryValue = () => {\n    return products.reduce((total, product) => {\n      return total + (product.stock_quantity * (product.cost || 0));\n    }, 0);\n  };\n\n  const getLowStockProducts = () => {\n    return products.filter(product => product.stock_quantity <= 10);\n  };\n\n  const getOutOfStockProducts = () => {\n    return products.filter(product => product.stock_quantity === 0);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"container\">\n        <div className=\"loading\">جاري تحميل بيانات المخزون...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container\">\n      <h1 className=\"page-title\">إدارة المخزون</h1>\n      \n      {message && <div className=\"success\">{message}</div>}\n      {error && <div className=\"error\">{error}</div>}\n      \n      {/* إحصائيات المخزون */}\n      <div className=\"dashboard-stats\">\n        <div className=\"stat-card\">\n          <div className=\"stat-number\">{products.length}</div>\n          <div className=\"stat-label\">إجمالي المنتجات</div>\n        </div>\n        \n        <div className=\"stat-card\" style={{ background: 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)' }}>\n          <div className=\"stat-number\">{getTotalInventoryValue().toLocaleString()}</div>\n          <div className=\"stat-label\">قيمة المخزون (ج.م)</div>\n        </div>\n        \n        <div className=\"stat-card\" style={{ background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)' }}>\n          <div className=\"stat-number\">{getLowStockProducts().length}</div>\n          <div className=\"stat-label\">منتجات مخزون منخفض</div>\n        </div>\n        \n        <div className=\"stat-card\" style={{ background: 'linear-gradient(135deg, #f44336 0%, #da190b 100%)' }}>\n          <div className=\"stat-number\">{getOutOfStockProducts().length}</div>\n          <div className=\"stat-label\">منتجات نفد مخزونها</div>\n        </div>\n      </div>\n\n      {/* تنبيهات المخزون */}\n      {(getLowStockProducts().length > 0 || getOutOfStockProducts().length > 0) && (\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"card-title\">⚠️ تنبيهات المخزون</h3>\n          </div>\n          <div className=\"card-body\">\n            {getOutOfStockProducts().length > 0 && (\n              <div className=\"alert alert-danger\">\n                <strong>منتجات نفد مخزونها:</strong> {getOutOfStockProducts().map(p => p.name).join(', ')}\n              </div>\n            )}\n            {getLowStockProducts().length > 0 && (\n              <div className=\"alert alert-warning\">\n                <strong>منتجات مخزون منخفض:</strong> {getLowStockProducts().map(p => `${p.name} (${p.stock_quantity})`).join(', ')}\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n      \n      {/* جدول المخزون */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">حالة المخزون</h3>\n        </div>\n        <div className=\"card-body\">\n          {products.length === 0 ? (\n            <p>لا توجد منتجات في المخزون</p>\n          ) : (\n            <table className=\"table\">\n              <thead>\n                <tr>\n                  <th>اسم المنتج</th>\n                  <th>الوحدة</th>\n                  <th>الكمية المتاحة</th>\n                  <th>حالة المخزون</th>\n                  <th>سعر التكلفة</th>\n                  <th>قيمة المخزون</th>\n                  <th>الإجراءات</th>\n                </tr>\n              </thead>\n              <tbody>\n                {products.map(product => {\n                  const stockStatus = getStockStatus(product.stock_quantity);\n                  return (\n                    <tr key={product.id}>\n                      <td>{product.name}</td>\n                      <td>{product.unit || '-'}</td>\n                      <td>\n                        <span style={{ \n                          fontWeight: 'bold',\n                          color: stockStatus.color\n                        }}>\n                          {product.stock_quantity}\n                        </span>\n                      </td>\n                      <td>\n                        <span style={{ color: stockStatus.color }}>\n                          {stockStatus.status}\n                        </span>\n                      </td>\n                      <td>{(product.cost || 0).toLocaleString()} ج.م</td>\n                      <td>{((product.stock_quantity || 0) * (product.cost || 0)).toLocaleString()} ج.م</td>\n                      <td>\n                        <button \n                          className=\"btn btn-primary\"\n                          onClick={() => handleAdjustStock(product)}\n                        >\n                          تعديل المخزون\n                        </button>\n                      </td>\n                    </tr>\n                  );\n                })}\n              </tbody>\n            </table>\n          )}\n        </div>\n      </div>\n\n      {/* نموذج تعديل المخزون */}\n      {showAdjustForm && selectedProduct && (\n        <div className=\"modal-overlay\" style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0,0,0,0.5)',\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          zIndex: 1000\n        }}>\n          <div className=\"modal-content\" style={{\n            backgroundColor: 'white',\n            borderRadius: '10px',\n            padding: '2rem',\n            maxWidth: '500px',\n            width: '90%'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>\n              <h3>تعديل مخزون: {selectedProduct.name}</h3>\n              <button onClick={() => setShowAdjustForm(false)} className=\"btn btn-danger\">✕</button>\n            </div>\n\n            <div style={{ marginBottom: '1rem', padding: '1rem', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>\n              <strong>الكمية الحالية: {selectedProduct.stock_quantity}</strong>\n            </div>\n\n            <form onSubmit={handleSubmitAdjustment}>\n              <div className=\"form-group\">\n                <label className=\"form-label\">نوع التعديل</label>\n                <select\n                  name=\"adjustment_type\"\n                  value={adjustmentData.adjustment_type}\n                  onChange={handleInputChange}\n                  className=\"form-control\"\n                  required\n                >\n                  <option value=\"add\">إضافة كمية</option>\n                  <option value=\"subtract\">خصم كمية</option>\n                  <option value=\"set\">تحديد كمية جديدة</option>\n                </select>\n              </div>\n\n              <div className=\"form-group\">\n                <label className=\"form-label\">الكمية</label>\n                <input\n                  type=\"number\"\n                  name=\"quantity\"\n                  value={adjustmentData.quantity}\n                  onChange={handleInputChange}\n                  className=\"form-control\"\n                  min=\"0\"\n                  required\n                />\n              </div>\n\n              <div className=\"form-group\">\n                <label className=\"form-label\">السبب</label>\n                <select\n                  name=\"reason\"\n                  value={adjustmentData.reason}\n                  onChange={handleInputChange}\n                  className=\"form-control\"\n                  required\n                >\n                  <option value=\"\">اختر السبب</option>\n                  <option value=\"purchase\">شراء جديد</option>\n                  <option value=\"sale\">بيع</option>\n                  <option value=\"damage\">تلف</option>\n                  <option value=\"theft\">فقدان</option>\n                  <option value=\"return\">مرتجع</option>\n                  <option value=\"adjustment\">تسوية جرد</option>\n                  <option value=\"other\">أخرى</option>\n                </select>\n              </div>\n\n              <div className=\"form-group\">\n                <label className=\"form-label\">ملاحظات</label>\n                <textarea\n                  name=\"notes\"\n                  value={adjustmentData.notes}\n                  onChange={handleInputChange}\n                  className=\"form-control\"\n                  rows=\"3\"\n                ></textarea>\n              </div>\n\n              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>\n                <button type=\"submit\" className=\"btn btn-success\">\n                  تأكيد التعديل\n                </button>\n                <button type=\"button\" onClick={() => setShowAdjustForm(false)} className=\"btn btn-danger\">\n                  إلغاء\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Inventory;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,cAAc,EAAEC,iBAAiB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACa,eAAe,EAAEC,kBAAkB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC;IACnDiB,eAAe,EAAE,KAAK;IAAE;IACxBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACdwB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMgB,QAAQ,GAAG,MAAMxB,KAAK,CAACyB,GAAG,CAAC,oCAAoC,CAAC;MACtEnB,WAAW,CAACkB,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,QAAQ,CAAC,2BAA2B,CAAC;MACrCK,OAAO,CAACN,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,iBAAiB,GAAIC,OAAO,IAAK;IACrCjB,kBAAkB,CAACiB,OAAO,CAAC;IAC3Bf,iBAAiB,CAAC;MAChBC,eAAe,EAAE,KAAK;MACtBC,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE;IACT,CAAC,CAAC;IACFR,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMoB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCpB,iBAAiB,CAACqB,IAAI,KAAK;MACzB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,sBAAsB,GAAG,MAAOL,CAAC,IAAK;IAC1CA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,IAAIC,WAAW,GAAG3B,eAAe,CAAC4B,cAAc;MAEhD,QAAQ1B,cAAc,CAACE,eAAe;QACpC,KAAK,KAAK;UACRuB,WAAW,IAAIE,QAAQ,CAAC3B,cAAc,CAACG,QAAQ,CAAC;UAChD;QACF,KAAK,UAAU;UACbsB,WAAW,IAAIE,QAAQ,CAAC3B,cAAc,CAACG,QAAQ,CAAC;UAChD;QACF,KAAK,KAAK;UACRsB,WAAW,GAAGE,QAAQ,CAAC3B,cAAc,CAACG,QAAQ,CAAC;UAC/C;QACF;UACE;MACJ;;MAEA;MACA,MAAMhB,KAAK,CAACyC,GAAG,CAAC,sCAAsC9B,eAAe,CAAC+B,EAAE,EAAE,EAAE;QAC1E,GAAG/B,eAAe;QAClB4B,cAAc,EAAEI,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,WAAW;MACzC,CAAC,CAAC;;MAEF;MACA,MAAMtC,KAAK,CAAC6C,IAAI,CAAC,+CAA+C,EAAE;QAChEC,UAAU,EAAEnC,eAAe,CAAC+B,EAAE;QAC9BK,aAAa,EAAElC,cAAc,CAACE,eAAe;QAC7CC,QAAQ,EAAEH,cAAc,CAACG,QAAQ;QACjCgC,YAAY,EAAErC,eAAe,CAAC4B,cAAc;QAC5CU,YAAY,EAAEN,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,WAAW,CAAC;QACtCrB,MAAM,EAAEJ,cAAc,CAACI,MAAM;QAC7BC,KAAK,EAAEL,cAAc,CAACK;MACxB,CAAC,CAAC;MAEFE,UAAU,CAAC,wBAAwB,CAAC;MACpCV,iBAAiB,CAAC,KAAK,CAAC;MACxBa,aAAa,CAAC,CAAC;MACf2B,UAAU,CAAC,MAAM9B,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,QAAQ,CAAC,sBAAsB,CAAC;MAChCK,OAAO,CAACN,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAM8B,cAAc,GAAInC,QAAQ,IAAK;IACnC,IAAIA,QAAQ,KAAK,CAAC,EAAE,OAAO;MAAEoC,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAM,CAAC;IAClE,IAAIrC,QAAQ,IAAI,EAAE,EAAE,OAAO;MAAEoC,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAS,CAAC;IACrE,IAAIrC,QAAQ,IAAI,EAAE,EAAE,OAAO;MAAEoC,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE;IAAO,CAAC;IACnE,OAAO;MAAED,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAQ,CAAC;EAChD,CAAC;EAED,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnC,OAAOjD,QAAQ,CAACkD,MAAM,CAAC,CAACC,KAAK,EAAE3B,OAAO,KAAK;MACzC,OAAO2B,KAAK,GAAI3B,OAAO,CAACU,cAAc,IAAIV,OAAO,CAAC4B,IAAI,IAAI,CAAC,CAAE;IAC/D,CAAC,EAAE,CAAC,CAAC;EACP,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,OAAOrD,QAAQ,CAACsD,MAAM,CAAC9B,OAAO,IAAIA,OAAO,CAACU,cAAc,IAAI,EAAE,CAAC;EACjE,CAAC;EAED,MAAMqB,qBAAqB,GAAGA,CAAA,KAAM;IAClC,OAAOvD,QAAQ,CAACsD,MAAM,CAAC9B,OAAO,IAAIA,OAAO,CAACU,cAAc,KAAK,CAAC,CAAC;EACjE,CAAC;EAED,IAAIhC,OAAO,EAAE;IACX,oBACEL,OAAA;MAAK2D,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxB5D,OAAA;QAAK2D,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAA4B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC;EAEV;EAEA,oBACEhE,OAAA;IAAK2D,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB5D,OAAA;MAAI2D,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAE5C/C,OAAO,iBAAIjB,OAAA;MAAK2D,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAE3C;IAAO;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACnD7C,KAAK,iBAAInB,OAAA;MAAK2D,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAEzC;IAAK;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAG9ChE,OAAA;MAAK2D,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B5D,OAAA;QAAK2D,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB5D,OAAA;UAAK2D,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAEzD,QAAQ,CAAC8D;QAAM;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpDhE,OAAA;UAAK2D,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eAENhE,OAAA;QAAK2D,SAAS,EAAC,WAAW;QAACO,KAAK,EAAE;UAAEC,UAAU,EAAE;QAAoD,CAAE;QAAAP,QAAA,gBACpG5D,OAAA;UAAK2D,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAER,sBAAsB,CAAC,CAAC,CAACgB,cAAc,CAAC;QAAC;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9EhE,OAAA;UAAK2D,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eAENhE,OAAA;QAAK2D,SAAS,EAAC,WAAW;QAACO,KAAK,EAAE;UAAEC,UAAU,EAAE;QAAoD,CAAE;QAAAP,QAAA,gBACpG5D,OAAA;UAAK2D,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAEJ,mBAAmB,CAAC,CAAC,CAACS;QAAM;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjEhE,OAAA;UAAK2D,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eAENhE,OAAA;QAAK2D,SAAS,EAAC,WAAW;QAACO,KAAK,EAAE;UAAEC,UAAU,EAAE;QAAoD,CAAE;QAAAP,QAAA,gBACpG5D,OAAA;UAAK2D,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAEF,qBAAqB,CAAC,CAAC,CAACO;QAAM;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnEhE,OAAA;UAAK2D,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL,CAACR,mBAAmB,CAAC,CAAC,CAACS,MAAM,GAAG,CAAC,IAAIP,qBAAqB,CAAC,CAAC,CAACO,MAAM,GAAG,CAAC,kBACtEjE,OAAA;MAAK2D,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB5D,OAAA;QAAK2D,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1B5D,OAAA;UAAI2D,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACNhE,OAAA;QAAK2D,SAAS,EAAC,WAAW;QAAAC,QAAA,GACvBF,qBAAqB,CAAC,CAAC,CAACO,MAAM,GAAG,CAAC,iBACjCjE,OAAA;UAAK2D,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBACjC5D,OAAA;YAAA4D,QAAA,EAAQ;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACN,qBAAqB,CAAC,CAAC,CAACW,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACxC,IAAI,CAAC,CAACyC,IAAI,CAAC,IAAI,CAAC;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtF,CACN,EACAR,mBAAmB,CAAC,CAAC,CAACS,MAAM,GAAG,CAAC,iBAC/BjE,OAAA;UAAK2D,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClC5D,OAAA;YAAA4D,QAAA,EAAQ;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACR,mBAAmB,CAAC,CAAC,CAACa,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAACxC,IAAI,KAAKwC,CAAC,CAACjC,cAAc,GAAG,CAAC,CAACkC,IAAI,CAAC,IAAI,CAAC;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDhE,OAAA;MAAK2D,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB5D,OAAA;QAAK2D,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1B5D,OAAA;UAAI2D,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,eACNhE,OAAA;QAAK2D,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBzD,QAAQ,CAAC8D,MAAM,KAAK,CAAC,gBACpBjE,OAAA;UAAA4D,QAAA,EAAG;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAEhChE,OAAA;UAAO2D,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACtB5D,OAAA;YAAA4D,QAAA,eACE5D,OAAA;cAAA4D,QAAA,gBACE5D,OAAA;gBAAA4D,QAAA,EAAI;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBhE,OAAA;gBAAA4D,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfhE,OAAA;gBAAA4D,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvBhE,OAAA;gBAAA4D,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrBhE,OAAA;gBAAA4D,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBhE,OAAA;gBAAA4D,QAAA,EAAI;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrBhE,OAAA;gBAAA4D,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRhE,OAAA;YAAA4D,QAAA,EACGzD,QAAQ,CAACkE,GAAG,CAAC1C,OAAO,IAAI;cACvB,MAAM6C,WAAW,GAAGvB,cAAc,CAACtB,OAAO,CAACU,cAAc,CAAC;cAC1D,oBACErC,OAAA;gBAAA4D,QAAA,gBACE5D,OAAA;kBAAA4D,QAAA,EAAKjC,OAAO,CAACG;gBAAI;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvBhE,OAAA;kBAAA4D,QAAA,EAAKjC,OAAO,CAAC8C,IAAI,IAAI;gBAAG;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9BhE,OAAA;kBAAA4D,QAAA,eACE5D,OAAA;oBAAMkE,KAAK,EAAE;sBACXQ,UAAU,EAAE,MAAM;sBAClBvB,KAAK,EAAEqB,WAAW,CAACrB;oBACrB,CAAE;oBAAAS,QAAA,EACCjC,OAAO,CAACU;kBAAc;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLhE,OAAA;kBAAA4D,QAAA,eACE5D,OAAA;oBAAMkE,KAAK,EAAE;sBAAEf,KAAK,EAAEqB,WAAW,CAACrB;oBAAM,CAAE;oBAAAS,QAAA,EACvCY,WAAW,CAACtB;kBAAM;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACLhE,OAAA;kBAAA4D,QAAA,GAAK,CAACjC,OAAO,CAAC4B,IAAI,IAAI,CAAC,EAAEa,cAAc,CAAC,CAAC,EAAC,gBAAI;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnDhE,OAAA;kBAAA4D,QAAA,GAAK,CAAC,CAACjC,OAAO,CAACU,cAAc,IAAI,CAAC,KAAKV,OAAO,CAAC4B,IAAI,IAAI,CAAC,CAAC,EAAEa,cAAc,CAAC,CAAC,EAAC,gBAAI;gBAAA;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrFhE,OAAA;kBAAA4D,QAAA,eACE5D,OAAA;oBACE2D,SAAS,EAAC,iBAAiB;oBAC3BgB,OAAO,EAAEA,CAAA,KAAMjD,iBAAiB,CAACC,OAAO,CAAE;oBAAAiC,QAAA,EAC3C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GAzBErC,OAAO,CAACa,EAAE;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0Bf,CAAC;YAET,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLzD,cAAc,IAAIE,eAAe,iBAChCT,OAAA;MAAK2D,SAAS,EAAC,eAAe;MAACO,KAAK,EAAE;QACpCU,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,eAAe,EAAE,iBAAiB;QAClCC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE;MACV,CAAE;MAAAzB,QAAA,eACA5D,OAAA;QAAK2D,SAAS,EAAC,eAAe;QAACO,KAAK,EAAE;UACpCe,eAAe,EAAE,OAAO;UACxBK,YAAY,EAAE,MAAM;UACpBC,OAAO,EAAE,MAAM;UACfC,QAAQ,EAAE,OAAO;UACjBC,KAAK,EAAE;QACT,CAAE;QAAA7B,QAAA,gBACA5D,OAAA;UAAKkE,KAAK,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEM,YAAY,EAAE;UAAO,CAAE;UAAA9B,QAAA,gBAC3G5D,OAAA;YAAA4D,QAAA,GAAI,iEAAa,EAACnD,eAAe,CAACqB,IAAI;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5ChE,OAAA;YAAQ2E,OAAO,EAAEA,CAAA,KAAMnE,iBAAiB,CAAC,KAAK,CAAE;YAACmD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF,CAAC,eAENhE,OAAA;UAAKkE,KAAK,EAAE;YAAEwB,YAAY,EAAE,MAAM;YAAEH,OAAO,EAAE,MAAM;YAAEN,eAAe,EAAE,SAAS;YAAEK,YAAY,EAAE;UAAM,CAAE;UAAA1B,QAAA,eACrG5D,OAAA;YAAA4D,QAAA,GAAQ,mFAAgB,EAACnD,eAAe,CAAC4B,cAAc;UAAA;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eAENhE,OAAA;UAAM2F,QAAQ,EAAEzD,sBAAuB;UAAA0B,QAAA,gBACrC5D,OAAA;YAAK2D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5D,OAAA;cAAO2D,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjDhE,OAAA;cACE8B,IAAI,EAAC,iBAAiB;cACtBC,KAAK,EAAEpB,cAAc,CAACE,eAAgB;cACtC+E,QAAQ,EAAEhE,iBAAkB;cAC5B+B,SAAS,EAAC,cAAc;cACxBkC,QAAQ;cAAAjC,QAAA,gBAER5D,OAAA;gBAAQ+B,KAAK,EAAC,KAAK;gBAAA6B,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvChE,OAAA;gBAAQ+B,KAAK,EAAC,UAAU;gBAAA6B,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC1ChE,OAAA;gBAAQ+B,KAAK,EAAC,KAAK;gBAAA6B,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENhE,OAAA;YAAK2D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5D,OAAA;cAAO2D,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5ChE,OAAA;cACE8F,IAAI,EAAC,QAAQ;cACbhE,IAAI,EAAC,UAAU;cACfC,KAAK,EAAEpB,cAAc,CAACG,QAAS;cAC/B8E,QAAQ,EAAEhE,iBAAkB;cAC5B+B,SAAS,EAAC,cAAc;cACxBoC,GAAG,EAAC,GAAG;cACPF,QAAQ;YAAA;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENhE,OAAA;YAAK2D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5D,OAAA;cAAO2D,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3ChE,OAAA;cACE8B,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAEpB,cAAc,CAACI,MAAO;cAC7B6E,QAAQ,EAAEhE,iBAAkB;cAC5B+B,SAAS,EAAC,cAAc;cACxBkC,QAAQ;cAAAjC,QAAA,gBAER5D,OAAA;gBAAQ+B,KAAK,EAAC,EAAE;gBAAA6B,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpChE,OAAA;gBAAQ+B,KAAK,EAAC,UAAU;gBAAA6B,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3ChE,OAAA;gBAAQ+B,KAAK,EAAC,MAAM;gBAAA6B,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACjChE,OAAA;gBAAQ+B,KAAK,EAAC,QAAQ;gBAAA6B,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnChE,OAAA;gBAAQ+B,KAAK,EAAC,OAAO;gBAAA6B,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpChE,OAAA;gBAAQ+B,KAAK,EAAC,QAAQ;gBAAA6B,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrChE,OAAA;gBAAQ+B,KAAK,EAAC,YAAY;gBAAA6B,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7ChE,OAAA;gBAAQ+B,KAAK,EAAC,OAAO;gBAAA6B,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENhE,OAAA;YAAK2D,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB5D,OAAA;cAAO2D,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7ChE,OAAA;cACE8B,IAAI,EAAC,OAAO;cACZC,KAAK,EAAEpB,cAAc,CAACK,KAAM;cAC5B4E,QAAQ,EAAEhE,iBAAkB;cAC5B+B,SAAS,EAAC,cAAc;cACxBqC,IAAI,EAAC;YAAG;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENhE,OAAA;YAAKkE,KAAK,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAEe,GAAG,EAAE,MAAM;cAAEd,cAAc,EAAE;YAAS,CAAE;YAAAvB,QAAA,gBACrE5D,OAAA;cAAQ8F,IAAI,EAAC,QAAQ;cAACnC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAElD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThE,OAAA;cAAQ8F,IAAI,EAAC,QAAQ;cAACnB,OAAO,EAAEA,CAAA,KAAMnE,iBAAiB,CAAC,KAAK,CAAE;cAACmD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAE1F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9D,EAAA,CApVID,SAAS;AAAAiG,EAAA,GAATjG,SAAS;AAsVf,eAAeA,SAAS;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}