#!/bin/bash

echo "========================================"
echo "    نظام المحاسبة المتكامل"
echo "    Integrated Accounting System"
echo "========================================"
echo

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    echo "خطأ: Node.js غير مثبت على النظام"
    echo "يرجى تثبيت Node.js من: https://nodejs.org"
    exit 1
fi

echo "Node.js موجود ✓"
echo

echo "تثبيت المكتبات المطلوبة..."
echo

echo "تثبيت مكتبات الواجهة الأمامية..."
npm install
if [ $? -ne 0 ]; then
    echo "خطأ في تثبيت مكتبات الواجهة الأمامية"
    exit 1
fi

echo "تثبيت مكتبات الخادم..."
cd server
npm install
if [ $? -ne 0 ]; then
    echo "خطأ في تثبيت مكتبات الخادم"
    exit 1
fi

cd ..
echo
echo "تم تثبيت جميع المكتبات بنجاح ✓"
echo

echo "بدء تشغيل النظام..."
echo

# تشغيل الخادم في الخلفية
echo "تشغيل الخادم..."
cd server
node server.js &
SERVER_PID=$!
cd ..

echo "انتظار تشغيل الخادم..."
sleep 3

# تشغيل الواجهة الأمامية
echo "تشغيل الواجهة الأمامية..."
npm start &
FRONTEND_PID=$!

echo
echo "========================================"
echo "تم تشغيل النظام بنجاح!"
echo
echo "الخادم: http://localhost:5000"
echo "الواجهة: http://localhost:3000"
echo
echo "للإيقاف: اضغط Ctrl+C"
echo "========================================"

# فتح المتصفح (إذا كان متاحاً)
if command -v xdg-open &> /dev/null; then
    sleep 5
    xdg-open http://localhost:3000
elif command -v open &> /dev/null; then
    sleep 5
    open http://localhost:3000
fi

# انتظار إيقاف العمليات
trap "echo 'إيقاف النظام...'; kill $SERVER_PID $FRONTEND_PID; exit" INT

wait
