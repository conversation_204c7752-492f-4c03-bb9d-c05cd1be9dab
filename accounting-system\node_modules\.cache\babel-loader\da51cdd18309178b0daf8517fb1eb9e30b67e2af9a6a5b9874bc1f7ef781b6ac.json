{"ast": null, "code": "var _jsxFileName = \"F:\\\\\\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\\\\accounting-system\\\\src\\\\components\\\\Products.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Products = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showForm, setShowForm] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    unit: '',\n    price: '',\n    cost: '',\n    stock_quantity: ''\n  });\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/products');\n      setProducts(response.data);\n    } catch (error) {\n      setError('خطأ في جلب بيانات المنتجات');\n      console.error('Error fetching products:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    try {\n      const productData = {\n        ...formData,\n        price: parseFloat(formData.price) || 0,\n        cost: parseFloat(formData.cost) || 0,\n        stock_quantity: parseInt(formData.stock_quantity) || 0\n      };\n      if (editingProduct) {\n        await axios.put(`http://localhost:5000/api/products/${editingProduct.id}`, productData);\n        setMessage('تم تحديث المنتج بنجاح');\n      } else {\n        await axios.post('http://localhost:5000/api/products', productData);\n        setMessage('تم إضافة المنتج بنجاح');\n      }\n      setFormData({\n        name: '',\n        description: '',\n        unit: '',\n        price: '',\n        cost: '',\n        stock_quantity: ''\n      });\n      setShowForm(false);\n      setEditingProduct(null);\n      fetchProducts();\n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      setError('خطأ في حفظ بيانات المنتج');\n      console.error('Error saving product:', error);\n    }\n  };\n  const handleEdit = product => {\n    var _product$price, _product$cost, _product$stock_quanti;\n    setEditingProduct(product);\n    setFormData({\n      name: product.name,\n      description: product.description || '',\n      unit: product.unit || '',\n      price: ((_product$price = product.price) === null || _product$price === void 0 ? void 0 : _product$price.toString()) || '',\n      cost: ((_product$cost = product.cost) === null || _product$cost === void 0 ? void 0 : _product$cost.toString()) || '',\n      stock_quantity: ((_product$stock_quanti = product.stock_quantity) === null || _product$stock_quanti === void 0 ? void 0 : _product$stock_quanti.toString()) || ''\n    });\n    setShowForm(true);\n  };\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      description: '',\n      unit: '',\n      price: '',\n      cost: '',\n      stock_quantity: ''\n    });\n    setShowForm(false);\n    setEditingProduct(null);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"\\u062C\\u0627\\u0631\\u064A \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"page-title\",\n      children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 19\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"card-title\",\n            children: \"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: () => setShowForm(!showForm),\n            children: showForm ? 'إلغاء' : 'إضافة منتج جديد'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), showForm && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleInputChange,\n                  className: \"form-control\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0644\\u0648\\u062D\\u062F\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"unit\",\n                  value: formData.unit,\n                  onChange: handleInputChange,\n                  className: \"form-control\",\n                  placeholder: \"\\u0642\\u0637\\u0639\\u0629\\u060C \\u0643\\u064A\\u0644\\u0648\\u060C \\u0645\\u062A\\u0631...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0628\\u064A\\u0639\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.01\",\n                  name: \"price\",\n                  value: formData.price,\n                  onChange: handleInputChange,\n                  className: \"form-control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u062A\\u0643\\u0644\\u0641\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.01\",\n                  name: \"cost\",\n                  value: formData.cost,\n                  onChange: handleInputChange,\n                  className: \"form-control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-md-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629 \\u0627\\u0644\\u0645\\u062A\\u0627\\u062D\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  name: \"stock_quantity\",\n                  value: formData.stock_quantity,\n                  onChange: handleInputChange,\n                  className: \"form-control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label\",\n                  children: \"\\u0627\\u0644\\u0648\\u0635\\u0641\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  name: \"description\",\n                  value: formData.description,\n                  onChange: handleInputChange,\n                  className: \"form-control\",\n                  rows: \"3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"btn btn-success\",\n              children: editingProduct ? 'تحديث المنتج' : 'إضافة المنتج'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"btn btn-danger\",\n              onClick: resetForm,\n              children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: products.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0645\\u0633\\u062C\\u0644\\u0629 \\u062D\\u062A\\u0649 \\u0627\\u0644\\u0622\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0648\\u062D\\u062F\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u0628\\u064A\\u0639\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0633\\u0639\\u0631 \\u0627\\u0644\\u062A\\u0643\\u0644\\u0641\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629 \\u0627\\u0644\\u0645\\u062A\\u0627\\u062D\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: products.map(product => {\n              var _product$price2, _product$cost2;\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: product.unit || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [((_product$price2 = product.price) === null || _product$price2 === void 0 ? void 0 : _product$price2.toLocaleString()) || '0', \" \\u062C.\\u0645\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: [((_product$cost2 = product.cost) === null || _product$cost2 === void 0 ? void 0 : _product$cost2.toLocaleString()) || '0', \" \\u062C.\\u0645\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: product.stock_quantity || '0'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-warning\",\n                    onClick: () => handleEdit(product),\n                    style: {\n                      marginLeft: '0.5rem'\n                    },\n                    children: \"\\u062A\\u0639\\u062F\\u064A\\u0644\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 21\n                }, this)]\n              }, product.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n};\n_s(Products, \"n9FKhENRMSs2OrenHhIb8cYcz58=\");\n_c = Products;\nexport default Products;\nvar _c;\n$RefreshReg$(_c, \"Products\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "Products", "_s", "products", "setProducts", "loading", "setLoading", "showForm", "setShowForm", "editingProduct", "setEditingProduct", "formData", "setFormData", "name", "description", "unit", "price", "cost", "stock_quantity", "message", "setMessage", "error", "setError", "fetchProducts", "response", "get", "data", "console", "handleInputChange", "e", "target", "value", "handleSubmit", "preventDefault", "productData", "parseFloat", "parseInt", "put", "id", "post", "setTimeout", "handleEdit", "product", "_product$price", "_product$cost", "_product$stock_quanti", "toString", "resetForm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "justifyContent", "alignItems", "onClick", "onSubmit", "type", "onChange", "required", "placeholder", "step", "rows", "gap", "length", "map", "_product$price2", "_product$cost2", "toLocaleString", "marginLeft", "_c", "$RefreshReg$"], "sources": ["F:/القيمة المضافة/accounting-system/src/components/Products.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst Products = () => {\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showForm, setShowForm] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    unit: '',\n    price: '',\n    cost: '',\n    stock_quantity: ''\n  });\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('http://localhost:5000/api/products');\n      setProducts(response.data);\n    } catch (error) {\n      setError('خطأ في جلب بيانات المنتجات');\n      console.error('Error fetching products:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    try {\n      const productData = {\n        ...formData,\n        price: parseFloat(formData.price) || 0,\n        cost: parseFloat(formData.cost) || 0,\n        stock_quantity: parseInt(formData.stock_quantity) || 0\n      };\n\n      if (editingProduct) {\n        await axios.put(`http://localhost:5000/api/products/${editingProduct.id}`, productData);\n        setMessage('تم تحديث المنتج بنجاح');\n      } else {\n        await axios.post('http://localhost:5000/api/products', productData);\n        setMessage('تم إضافة المنتج بنجاح');\n      }\n      \n      setFormData({\n        name: '',\n        description: '',\n        unit: '',\n        price: '',\n        cost: '',\n        stock_quantity: ''\n      });\n      setShowForm(false);\n      setEditingProduct(null);\n      fetchProducts();\n      \n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      setError('خطأ في حفظ بيانات المنتج');\n      console.error('Error saving product:', error);\n    }\n  };\n\n  const handleEdit = (product) => {\n    setEditingProduct(product);\n    setFormData({\n      name: product.name,\n      description: product.description || '',\n      unit: product.unit || '',\n      price: product.price?.toString() || '',\n      cost: product.cost?.toString() || '',\n      stock_quantity: product.stock_quantity?.toString() || ''\n    });\n    setShowForm(true);\n  };\n\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      description: '',\n      unit: '',\n      price: '',\n      cost: '',\n      stock_quantity: ''\n    });\n    setShowForm(false);\n    setEditingProduct(null);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"container\">\n        <div className=\"loading\">جاري تحميل بيانات المنتجات...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container\">\n      <h1 className=\"page-title\">إدارة المنتجات</h1>\n      \n      {message && <div className=\"success\">{message}</div>}\n      {error && <div className=\"error\">{error}</div>}\n      \n      <div className=\"card\">\n        <div className=\"card-header\">\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <h3 className=\"card-title\">قائمة المنتجات</h3>\n            <button \n              className=\"btn btn-primary\"\n              onClick={() => setShowForm(!showForm)}\n            >\n              {showForm ? 'إلغاء' : 'إضافة منتج جديد'}\n            </button>\n          </div>\n        </div>\n        \n        {showForm && (\n          <div className=\"card-body\">\n            <form onSubmit={handleSubmit}>\n              <div className=\"row\">\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">اسم المنتج *</label>\n                    <input\n                      type=\"text\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                      required\n                    />\n                  </div>\n                </div>\n                <div className=\"col-md-6\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">الوحدة</label>\n                    <input\n                      type=\"text\"\n                      name=\"unit\"\n                      value={formData.unit}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                      placeholder=\"قطعة، كيلو، متر...\"\n                    />\n                  </div>\n                </div>\n                <div className=\"col-md-4\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">سعر البيع</label>\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      name=\"price\"\n                      value={formData.price}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                    />\n                  </div>\n                </div>\n                <div className=\"col-md-4\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">سعر التكلفة</label>\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      name=\"cost\"\n                      value={formData.cost}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                    />\n                  </div>\n                </div>\n                <div className=\"col-md-4\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">الكمية المتاحة</label>\n                    <input\n                      type=\"number\"\n                      name=\"stock_quantity\"\n                      value={formData.stock_quantity}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                    />\n                  </div>\n                </div>\n                <div className=\"col\">\n                  <div className=\"form-group\">\n                    <label className=\"form-label\">الوصف</label>\n                    <textarea\n                      name=\"description\"\n                      value={formData.description}\n                      onChange={handleInputChange}\n                      className=\"form-control\"\n                      rows=\"3\"\n                    ></textarea>\n                  </div>\n                </div>\n              </div>\n              <div style={{ display: 'flex', gap: '1rem' }}>\n                <button type=\"submit\" className=\"btn btn-success\">\n                  {editingProduct ? 'تحديث المنتج' : 'إضافة المنتج'}\n                </button>\n                <button type=\"button\" className=\"btn btn-danger\" onClick={resetForm}>\n                  إلغاء\n                </button>\n              </div>\n            </form>\n          </div>\n        )}\n        \n        <div className=\"card-body\">\n          {products.length === 0 ? (\n            <p>لا توجد منتجات مسجلة حتى الآن</p>\n          ) : (\n            <table className=\"table\">\n              <thead>\n                <tr>\n                  <th>اسم المنتج</th>\n                  <th>الوحدة</th>\n                  <th>سعر البيع</th>\n                  <th>سعر التكلفة</th>\n                  <th>الكمية المتاحة</th>\n                  <th>الإجراءات</th>\n                </tr>\n              </thead>\n              <tbody>\n                {products.map(product => (\n                  <tr key={product.id}>\n                    <td>{product.name}</td>\n                    <td>{product.unit || '-'}</td>\n                    <td>{product.price?.toLocaleString() || '0'} ج.م</td>\n                    <td>{product.cost?.toLocaleString() || '0'} ج.م</td>\n                    <td>{product.stock_quantity || '0'}</td>\n                    <td>\n                      <button \n                        className=\"btn btn-warning\"\n                        onClick={() => handleEdit(product)}\n                        style={{ marginLeft: '0.5rem' }}\n                      >\n                        تعديل\n                      </button>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Products;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACa,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC;IACvCiB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACd0B,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMkB,QAAQ,GAAG,MAAM1B,KAAK,CAAC2B,GAAG,CAAC,oCAAoC,CAAC;MACtErB,WAAW,CAACoB,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,QAAQ,CAAC,4BAA4B,CAAC;MACtCK,OAAO,CAACN,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsB,iBAAiB,GAAIC,CAAC,IAAK;IAC/BjB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACkB,CAAC,CAACC,MAAM,CAACjB,IAAI,GAAGgB,CAAC,CAACC,MAAM,CAACC;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB,IAAI;MACF,MAAMC,WAAW,GAAG;QAClB,GAAGvB,QAAQ;QACXK,KAAK,EAAEmB,UAAU,CAACxB,QAAQ,CAACK,KAAK,CAAC,IAAI,CAAC;QACtCC,IAAI,EAAEkB,UAAU,CAACxB,QAAQ,CAACM,IAAI,CAAC,IAAI,CAAC;QACpCC,cAAc,EAAEkB,QAAQ,CAACzB,QAAQ,CAACO,cAAc,CAAC,IAAI;MACvD,CAAC;MAED,IAAIT,cAAc,EAAE;QAClB,MAAMX,KAAK,CAACuC,GAAG,CAAC,sCAAsC5B,cAAc,CAAC6B,EAAE,EAAE,EAAEJ,WAAW,CAAC;QACvFd,UAAU,CAAC,uBAAuB,CAAC;MACrC,CAAC,MAAM;QACL,MAAMtB,KAAK,CAACyC,IAAI,CAAC,oCAAoC,EAAEL,WAAW,CAAC;QACnEd,UAAU,CAAC,uBAAuB,CAAC;MACrC;MAEAR,WAAW,CAAC;QACVC,IAAI,EAAE,EAAE;QACRC,WAAW,EAAE,EAAE;QACfC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTC,IAAI,EAAE,EAAE;QACRC,cAAc,EAAE;MAClB,CAAC,CAAC;MACFV,WAAW,CAAC,KAAK,CAAC;MAClBE,iBAAiB,CAAC,IAAI,CAAC;MACvBa,aAAa,CAAC,CAAC;MAEfiB,UAAU,CAAC,MAAMpB,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,QAAQ,CAAC,0BAA0B,CAAC;MACpCK,OAAO,CAACN,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMoB,UAAU,GAAIC,OAAO,IAAK;IAAA,IAAAC,cAAA,EAAAC,aAAA,EAAAC,qBAAA;IAC9BnC,iBAAiB,CAACgC,OAAO,CAAC;IAC1B9B,WAAW,CAAC;MACVC,IAAI,EAAE6B,OAAO,CAAC7B,IAAI;MAClBC,WAAW,EAAE4B,OAAO,CAAC5B,WAAW,IAAI,EAAE;MACtCC,IAAI,EAAE2B,OAAO,CAAC3B,IAAI,IAAI,EAAE;MACxBC,KAAK,EAAE,EAAA2B,cAAA,GAAAD,OAAO,CAAC1B,KAAK,cAAA2B,cAAA,uBAAbA,cAAA,CAAeG,QAAQ,CAAC,CAAC,KAAI,EAAE;MACtC7B,IAAI,EAAE,EAAA2B,aAAA,GAAAF,OAAO,CAACzB,IAAI,cAAA2B,aAAA,uBAAZA,aAAA,CAAcE,QAAQ,CAAC,CAAC,KAAI,EAAE;MACpC5B,cAAc,EAAE,EAAA2B,qBAAA,GAAAH,OAAO,CAACxB,cAAc,cAAA2B,qBAAA,uBAAtBA,qBAAA,CAAwBC,QAAQ,CAAC,CAAC,KAAI;IACxD,CAAC,CAAC;IACFtC,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMuC,SAAS,GAAGA,CAAA,KAAM;IACtBnC,WAAW,CAAC;MACVC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,EAAE;MACRC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFV,WAAW,CAAC,KAAK,CAAC;IAClBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,IAAIL,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKgD,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBjD,OAAA;QAAKgD,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC;EAEV;EAEA,oBACErD,OAAA;IAAKgD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBjD,OAAA;MAAIgD,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAE7ClC,OAAO,iBAAInB,OAAA;MAAKgD,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAE9B;IAAO;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACnDhC,KAAK,iBAAIrB,OAAA;MAAKgD,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAE5B;IAAK;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE9CrD,OAAA;MAAKgD,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBjD,OAAA;QAAKgD,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BjD,OAAA;UAAKsD,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAR,QAAA,gBACrFjD,OAAA;YAAIgD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9CrD,OAAA;YACEgD,SAAS,EAAC,iBAAiB;YAC3BU,OAAO,EAAEA,CAAA,KAAMlD,WAAW,CAAC,CAACD,QAAQ,CAAE;YAAA0C,QAAA,EAErC1C,QAAQ,GAAG,OAAO,GAAG;UAAiB;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL9C,QAAQ,iBACPP,OAAA;QAAKgD,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBjD,OAAA;UAAM2D,QAAQ,EAAE3B,YAAa;UAAAiB,QAAA,gBAC3BjD,OAAA;YAAKgD,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBjD,OAAA;cAAKgD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBjD,OAAA;gBAAKgD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjD,OAAA;kBAAOgD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClDrD,OAAA;kBACE4D,IAAI,EAAC,MAAM;kBACX/C,IAAI,EAAC,MAAM;kBACXkB,KAAK,EAAEpB,QAAQ,CAACE,IAAK;kBACrBgD,QAAQ,EAAEjC,iBAAkB;kBAC5BoB,SAAS,EAAC,cAAc;kBACxBc,QAAQ;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrD,OAAA;cAAKgD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBjD,OAAA;gBAAKgD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjD,OAAA;kBAAOgD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5CrD,OAAA;kBACE4D,IAAI,EAAC,MAAM;kBACX/C,IAAI,EAAC,MAAM;kBACXkB,KAAK,EAAEpB,QAAQ,CAACI,IAAK;kBACrB8C,QAAQ,EAAEjC,iBAAkB;kBAC5BoB,SAAS,EAAC,cAAc;kBACxBe,WAAW,EAAC;gBAAoB;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrD,OAAA;cAAKgD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBjD,OAAA;gBAAKgD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjD,OAAA;kBAAOgD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/CrD,OAAA;kBACE4D,IAAI,EAAC,QAAQ;kBACbI,IAAI,EAAC,MAAM;kBACXnD,IAAI,EAAC,OAAO;kBACZkB,KAAK,EAAEpB,QAAQ,CAACK,KAAM;kBACtB6C,QAAQ,EAAEjC,iBAAkB;kBAC5BoB,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrD,OAAA;cAAKgD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBjD,OAAA;gBAAKgD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjD,OAAA;kBAAOgD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjDrD,OAAA;kBACE4D,IAAI,EAAC,QAAQ;kBACbI,IAAI,EAAC,MAAM;kBACXnD,IAAI,EAAC,MAAM;kBACXkB,KAAK,EAAEpB,QAAQ,CAACM,IAAK;kBACrB4C,QAAQ,EAAEjC,iBAAkB;kBAC5BoB,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrD,OAAA;cAAKgD,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBjD,OAAA;gBAAKgD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjD,OAAA;kBAAOgD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpDrD,OAAA;kBACE4D,IAAI,EAAC,QAAQ;kBACb/C,IAAI,EAAC,gBAAgB;kBACrBkB,KAAK,EAAEpB,QAAQ,CAACO,cAAe;kBAC/B2C,QAAQ,EAAEjC,iBAAkB;kBAC5BoB,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrD,OAAA;cAAKgD,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClBjD,OAAA;gBAAKgD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjD,OAAA;kBAAOgD,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3CrD,OAAA;kBACEa,IAAI,EAAC,aAAa;kBAClBkB,KAAK,EAAEpB,QAAQ,CAACG,WAAY;kBAC5B+C,QAAQ,EAAEjC,iBAAkB;kBAC5BoB,SAAS,EAAC,cAAc;kBACxBiB,IAAI,EAAC;gBAAG;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNrD,OAAA;YAAKsD,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEW,GAAG,EAAE;YAAO,CAAE;YAAAjB,QAAA,gBAC3CjD,OAAA;cAAQ4D,IAAI,EAAC,QAAQ;cAACZ,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC9CxC,cAAc,GAAG,cAAc,GAAG;YAAc;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACTrD,OAAA;cAAQ4D,IAAI,EAAC,QAAQ;cAACZ,SAAS,EAAC,gBAAgB;cAACU,OAAO,EAAEX,SAAU;cAAAE,QAAA,EAAC;YAErE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,eAEDrD,OAAA;QAAKgD,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB9C,QAAQ,CAACgE,MAAM,KAAK,CAAC,gBACpBnE,OAAA;UAAAiD,QAAA,EAAG;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,gBAEpCrD,OAAA;UAAOgD,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACtBjD,OAAA;YAAAiD,QAAA,eACEjD,OAAA;cAAAiD,QAAA,gBACEjD,OAAA;gBAAAiD,QAAA,EAAI;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBrD,OAAA;gBAAAiD,QAAA,EAAI;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfrD,OAAA;gBAAAiD,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClBrD,OAAA;gBAAAiD,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBrD,OAAA;gBAAAiD,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvBrD,OAAA;gBAAAiD,QAAA,EAAI;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRrD,OAAA;YAAAiD,QAAA,EACG9C,QAAQ,CAACiE,GAAG,CAAC1B,OAAO;cAAA,IAAA2B,eAAA,EAAAC,cAAA;cAAA,oBACnBtE,OAAA;gBAAAiD,QAAA,gBACEjD,OAAA;kBAAAiD,QAAA,EAAKP,OAAO,CAAC7B;gBAAI;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvBrD,OAAA;kBAAAiD,QAAA,EAAKP,OAAO,CAAC3B,IAAI,IAAI;gBAAG;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9BrD,OAAA;kBAAAiD,QAAA,GAAK,EAAAoB,eAAA,GAAA3B,OAAO,CAAC1B,KAAK,cAAAqD,eAAA,uBAAbA,eAAA,CAAeE,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,gBAAI;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrDrD,OAAA;kBAAAiD,QAAA,GAAK,EAAAqB,cAAA,GAAA5B,OAAO,CAACzB,IAAI,cAAAqD,cAAA,uBAAZA,cAAA,CAAcC,cAAc,CAAC,CAAC,KAAI,GAAG,EAAC,gBAAI;gBAAA;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpDrD,OAAA;kBAAAiD,QAAA,EAAKP,OAAO,CAACxB,cAAc,IAAI;gBAAG;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxCrD,OAAA;kBAAAiD,QAAA,eACEjD,OAAA;oBACEgD,SAAS,EAAC,iBAAiB;oBAC3BU,OAAO,EAAEA,CAAA,KAAMjB,UAAU,CAACC,OAAO,CAAE;oBACnCY,KAAK,EAAE;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAAvB,QAAA,EACjC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA,GAdEX,OAAO,CAACJ,EAAE;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAef,CAAC;YAAA,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnD,EAAA,CAzQID,QAAQ;AAAAwE,EAAA,GAARxE,QAAQ;AA2Qd,eAAeA,QAAQ;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}