# 🛠️ دليل التثبيت - نظام المحاسبة المتكامل

## 📋 المتطلبات الأساسية

### النظام
- **نظام التشغيل**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **الذاكرة**: 4 GB RAM كحد أدنى (8 GB مُوصى به)
- **التخزين**: 2 GB مساحة فارغة
- **الشبكة**: اتصال إنترنت للتحديثات (اختياري)

### البرامج المطلوبة
- **Node.js**: الإصدار 16.0.0 أو أحدث
- **npm**: يأتي مع Node.js
- **Git**: للحصول على الكود المصدري (اختياري)

## 🔽 تحميل وتثبيت Node.js

### Windows
1. اذهب إلى [nodejs.org](https://nodejs.org)
2. حمل النسخة LTS (الموصى بها)
3. شغ<PERSON> الملف المحمل واتبع التعليمات
4. أعد تشغيل الكمبيوتر

### macOS
```bash
# باستخدام Homebrew (الطريقة المُوصى بها)
brew install node

# أو حمل من الموقع الرسمي
# https://nodejs.org
```

### Ubuntu/Linux
```bash
# تحديث النظام
sudo apt update

# تثبيت Node.js و npm
sudo apt install nodejs npm

# التحقق من الإصدار
node --version
npm --version
```

## 📥 تحميل النظام المحاسبي

### الطريقة 1: تحميل ZIP
1. حمل ملف ZIP من GitHub أو الموقع الرسمي
2. استخرج الملفات في مجلد مناسب
3. افتح Terminal/Command Prompt في مجلد المشروع

### الطريقة 2: استخدام Git
```bash
# استنساخ المشروع
git clone https://github.com/accounting-system/accounting-system.git

# الانتقال لمجلد المشروع
cd accounting-system
```

## ⚡ التثبيت السريع

### Windows
1. انقر نقراً مزدوجاً على `start.bat`
2. انتظر حتى يكتمل التثبيت والتشغيل
3. سيفتح المتصفح تلقائياً على `http://localhost:3000`

### macOS/Linux
```bash
# جعل الملف قابل للتنفيذ
chmod +x start.sh

# تشغيل النظام
./start.sh
```

## 🔧 التثبيت اليدوي

### الخطوة 1: تثبيت مكتبات الواجهة الأمامية
```bash
# في مجلد المشروع الرئيسي
npm install
```

### الخطوة 2: تثبيت مكتبات الخادم
```bash
# الانتقال لمجلد الخادم
cd server

# تثبيت المكتبات
npm install

# العودة للمجلد الرئيسي
cd ..
```

### الخطوة 3: تشغيل النظام
```bash
# تشغيل الخادم (في terminal منفصل)
cd server
node server.js

# تشغيل الواجهة الأمامية (في terminal آخر)
npm start
```

## 🌐 الوصول للنظام

بعد التشغيل الناجح:
- **الواجهة الأمامية**: http://localhost:3000
- **الخادم**: http://localhost:5000
- **قاعدة البيانات**: تُنشأ تلقائياً في `server/accounting.db`

## ⚙️ إعدادات متقدمة

### تغيير المنافذ
إذا كانت المنافذ الافتراضية مستخدمة:

#### تغيير منفذ الخادم
```bash
# في ملف server/server.js
const PORT = process.env.PORT || 5001; // غير 5000 إلى 5001
```

#### تغيير منفذ الواجهة الأمامية
```bash
# إنشاء ملف .env في المجلد الرئيسي
echo "PORT=3001" > .env
```

### إعدادات قاعدة البيانات
```bash
# في ملف server/database.js
# يمكن تغيير مسار قاعدة البيانات
const dbPath = path.join(__dirname, 'custom_accounting.db');
```

## 🔍 التحقق من التثبيت

### فحص Node.js و npm
```bash
node --version  # يجب أن يظهر v16.0.0 أو أحدث
npm --version   # يجب أن يظهر 8.0.0 أو أحدث
```

### فحص المكتبات
```bash
# في المجلد الرئيسي
npm list

# في مجلد الخادم
cd server
npm list
```

### فحص الخادم
```bash
# اختبار الخادم
curl http://localhost:5000/api/test

# يجب أن يرجع: {"message": "الخادم يعمل بنجاح"}
```

## 🐛 حل مشاكل التثبيت

### مشكلة: "node is not recognized"
**الحل:**
1. تأكد من تثبيت Node.js بشكل صحيح
2. أعد تشغيل Terminal/Command Prompt
3. أعد تشغيل الكمبيوتر إذا لزم الأمر

### مشكلة: "npm install fails"
**الحل:**
```bash
# مسح cache npm
npm cache clean --force

# حذف node_modules وإعادة التثبيت
rm -rf node_modules
npm install

# أو في Windows
rmdir /s node_modules
npm install
```

### مشكلة: "Port already in use"
**الحل:**
```bash
# العثور على العملية المستخدمة للمنفذ
# Windows
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# macOS/Linux
lsof -ti:3000 | xargs kill -9
```

### مشكلة: "Database locked"
**الحل:**
```bash
# إيقاف جميع عمليات النظام
# حذف ملف قاعدة البيانات وإعادة إنشائه
rm server/accounting.db
# إعادة تشغيل الخادم
```

## 🔄 التحديث

### تحديث النظام
```bash
# تحديث الكود المصدري (إذا كنت تستخدم Git)
git pull origin main

# تحديث المكتبات
npm update
cd server
npm update
cd ..
```

### تحديث Node.js
1. حمل أحدث إصدار من [nodejs.org](https://nodejs.org)
2. ثبت الإصدار الجديد
3. أعد تثبيت مكتبات المشروع

## 🏗️ بناء النسخة الإنتاجية

```bash
# بناء الواجهة الأمامية للإنتاج
npm run build

# تشغيل الخادم في وضع الإنتاج
cd server
NODE_ENV=production node server.js
```

## 🐳 التثبيت باستخدام Docker (قريباً)

```bash
# بناء الصورة
docker build -t accounting-system .

# تشغيل الحاوية
docker run -p 3000:3000 -p 5000:5000 accounting-system
```

## 📱 التثبيت على الخادم

### متطلبات الخادم
- Ubuntu 20.04+ أو CentOS 8+
- 2 GB RAM كحد أدنى
- Node.js 16+
- Nginx (للبروكسي)
- SSL Certificate (للأمان)

### خطوات التثبيت على الخادم
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# تثبيت PM2 لإدارة العمليات
sudo npm install -g pm2

# استنساخ المشروع
git clone https://github.com/accounting-system/accounting-system.git
cd accounting-system

# تثبيت المكتبات
npm install
cd server && npm install && cd ..

# بناء الإنتاج
npm run build

# تشغيل بـ PM2
pm2 start server/server.js --name "accounting-server"
pm2 startup
pm2 save
```

## 📞 الدعم

إذا واجهت أي مشاكل في التثبيت:

### الدعم الفني
- 📧 **البريد الإلكتروني**: <EMAIL>
- 💬 **الدردشة المباشرة**: متاحة على الموقع
- 📱 **الهاتف**: +20 123 456 7890

### المصادر المفيدة
- [دليل البدء السريع](QUICK_START_GUIDE.md)
- [الدليل الكامل](README_ARABIC.md)
- [سجل التغييرات](CHANGELOG.md)
- [الأسئلة الشائعة](FAQ.md)

### المجتمع
- **منتدى المجتمع**: community.accounting-system.com
- **GitHub Issues**: للإبلاغ عن الأخطاء
- **Discord**: للدردشة المباشرة مع المطورين

---

**مبروك! تم تثبيت نظام المحاسبة المتكامل بنجاح! 🎉**

*للبدء في الاستخدام، راجع [دليل البدء السريع](QUICK_START_GUIDE.md)*
