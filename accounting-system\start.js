#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('========================================');
console.log('    نظام المحاسبة المتكامل');
console.log('    Integrated Accounting System');
console.log('========================================');
console.log();

// التحقق من وجود Node.js
function checkNodeJS() {
    return new Promise((resolve, reject) => {
        exec('node --version', (error, stdout, stderr) => {
            if (error) {
                console.error('❌ خطأ: Node.js غير مثبت على النظام');
                console.error('يرجى تثبيت Node.js من: https://nodejs.org');
                reject(error);
            } else {
                console.log('✅ Node.js موجود:', stdout.trim());
                resolve();
            }
        });
    });
}

// التحقق من وجود المكتبات
function checkDependencies() {
    const frontendPackageJson = path.join(__dirname, 'package.json');
    const serverPackageJson = path.join(__dirname, 'server', 'package.json');
    const frontendNodeModules = path.join(__dirname, 'node_modules');
    const serverNodeModules = path.join(__dirname, 'server', 'node_modules');

    if (!fs.existsSync(frontendPackageJson)) {
        throw new Error('ملف package.json للواجهة الأمامية غير موجود');
    }

    if (!fs.existsSync(serverPackageJson)) {
        throw new Error('ملف package.json للخادم غير موجود');
    }

    return {
        frontendInstalled: fs.existsSync(frontendNodeModules),
        serverInstalled: fs.existsSync(serverNodeModules)
    };
}

// تثبيت المكتبات
function installDependencies(type, cwd = __dirname) {
    return new Promise((resolve, reject) => {
        console.log(`📦 تثبيت مكتبات ${type}...`);
        
        const npmInstall = spawn('npm', ['install'], {
            cwd: cwd,
            stdio: 'inherit',
            shell: true
        });

        npmInstall.on('close', (code) => {
            if (code === 0) {
                console.log(`✅ تم تثبيت مكتبات ${type} بنجاح`);
                resolve();
            } else {
                console.error(`❌ خطأ في تثبيت مكتبات ${type}`);
                reject(new Error(`npm install failed with code ${code}`));
            }
        });

        npmInstall.on('error', (error) => {
            console.error(`❌ خطأ في تشغيل npm install: ${error.message}`);
            reject(error);
        });
    });
}

// تشغيل الخادم
function startServer() {
    return new Promise((resolve, reject) => {
        console.log('🚀 تشغيل الخادم...');
        
        const serverPath = path.join(__dirname, 'server', 'server.js');
        const server = spawn('node', [serverPath], {
            cwd: path.join(__dirname, 'server'),
            stdio: 'inherit',
            shell: true
        });

        // انتظار قصير للتأكد من تشغيل الخادم
        setTimeout(() => {
            console.log('✅ تم تشغيل الخادم على http://localhost:5000');
            resolve(server);
        }, 3000);

        server.on('error', (error) => {
            console.error(`❌ خطأ في تشغيل الخادم: ${error.message}`);
            reject(error);
        });
    });
}

// تشغيل الواجهة الأمامية
function startFrontend() {
    return new Promise((resolve, reject) => {
        console.log('🎨 تشغيل الواجهة الأمامية...');
        
        const frontend = spawn('npm', ['start'], {
            cwd: __dirname,
            stdio: 'inherit',
            shell: true,
            env: { ...process.env, BROWSER: 'none' }
        });

        // انتظار قصير للتأكد من تشغيل الواجهة
        setTimeout(() => {
            console.log('✅ تم تشغيل الواجهة الأمامية على http://localhost:3000');
            resolve(frontend);
        }, 5000);

        frontend.on('error', (error) => {
            console.error(`❌ خطأ في تشغيل الواجهة الأمامية: ${error.message}`);
            reject(error);
        });
    });
}

// فتح المتصفح
function openBrowser() {
    const url = 'http://localhost:3000';
    let command;

    switch (process.platform) {
        case 'darwin':
            command = 'open';
            break;
        case 'win32':
            command = 'start';
            break;
        default:
            command = 'xdg-open';
    }

    setTimeout(() => {
        exec(`${command} ${url}`, (error) => {
            if (error) {
                console.log(`🌐 افتح المتصفح يدوياً على: ${url}`);
            } else {
                console.log('🌐 تم فتح المتصفح تلقائياً');
            }
        });
    }, 8000);
}

// الدالة الرئيسية
async function main() {
    try {
        // التحقق من Node.js
        await checkNodeJS();
        console.log();

        // التحقق من المكتبات
        console.log('🔍 فحص المكتبات المطلوبة...');
        const deps = checkDependencies();

        // تثبيت المكتبات إذا لزم الأمر
        if (!deps.frontendInstalled) {
            await installDependencies('الواجهة الأمامية');
        } else {
            console.log('✅ مكتبات الواجهة الأمامية موجودة');
        }

        if (!deps.serverInstalled) {
            await installDependencies('الخادم', path.join(__dirname, 'server'));
        } else {
            console.log('✅ مكتبات الخادم موجودة');
        }

        console.log();
        console.log('🚀 بدء تشغيل النظام...');
        console.log();

        // تشغيل الخادم
        const serverProcess = await startServer();

        // تشغيل الواجهة الأمامية
        const frontendProcess = await startFrontend();

        // فتح المتصفح
        openBrowser();

        console.log();
        console.log('========================================');
        console.log('🎉 تم تشغيل النظام بنجاح!');
        console.log();
        console.log('🌐 الواجهة الأمامية: http://localhost:3000');
        console.log('🔧 الخادم: http://localhost:5000');
        console.log();
        console.log('⏹️  للإيقاف: اضغط Ctrl+C');
        console.log('========================================');

        // معالجة إيقاف النظام
        process.on('SIGINT', () => {
            console.log('\n🛑 إيقاف النظام...');
            
            if (serverProcess) {
                serverProcess.kill('SIGTERM');
            }
            
            if (frontendProcess) {
                frontendProcess.kill('SIGTERM');
            }
            
            setTimeout(() => {
                console.log('✅ تم إيقاف النظام بنجاح');
                process.exit(0);
            }, 2000);
        });

        // منع إنهاء البرنامج
        process.stdin.resume();

    } catch (error) {
        console.error('❌ خطأ في تشغيل النظام:', error.message);
        console.log();
        console.log('💡 نصائح لحل المشكلة:');
        console.log('1. تأكد من تثبيت Node.js (https://nodejs.org)');
        console.log('2. تأكد من وجود اتصال إنترنت');
        console.log('3. جرب تشغيل: npm cache clean --force');
        console.log('4. جرب حذف مجلد node_modules وإعادة التثبيت');
        console.log();
        console.log('📞 للدعم الفني: <EMAIL>');
        process.exit(1);
    }
}

// تشغيل البرنامج
if (require.main === module) {
    main();
}

module.exports = {
    checkNodeJS,
    checkDependencies,
    installDependencies,
    startServer,
    startFrontend,
    main
};
