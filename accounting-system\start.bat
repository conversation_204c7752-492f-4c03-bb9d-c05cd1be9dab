@echo off
echo ========================================
echo    نظام المحاسبة المتكامل
echo    Integrated Accounting System
echo ========================================
echo.

echo تحقق من وجود Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت على النظام
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo Node.js موجود ✓
echo.

echo تثبيت المكتبات المطلوبة...
echo.

echo تثبيت مكتبات الواجهة الأمامية...
call npm install
if %errorlevel% neq 0 (
    echo خطأ في تثبيت مكتبات الواجهة الأمامية
    pause
    exit /b 1
)

echo تثبيت مكتبات الخادم...
cd server
call npm install
if %errorlevel% neq 0 (
    echo خطأ في تثبيت مكتبات الخادم
    pause
    exit /b 1
)

cd ..
echo.
echo تم تثبيت جميع المكتبات بنجاح ✓
echo.

echo بدء تشغيل النظام...
echo.

echo تشغيل الخادم...
start "Accounting Server" cmd /k "cd server && node server.js"

echo انتظار تشغيل الخادم...
timeout /t 3 /nobreak >nul

echo تشغيل الواجهة الأمامية...
start "Accounting Frontend" cmd /k "npm start"

echo.
echo ========================================
echo تم تشغيل النظام بنجاح!
echo.
echo الخادم: http://localhost:5000
echo الواجهة: http://localhost:3000
echo.
echo سيتم فتح المتصفح تلقائياً...
echo ========================================

timeout /t 5 /nobreak >nul
start http://localhost:3000

pause
