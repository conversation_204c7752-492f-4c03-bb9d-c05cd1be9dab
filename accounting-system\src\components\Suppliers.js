import React, { useState, useEffect } from 'react';
import axios from 'axios';

const Suppliers = () => {
  const [suppliers, setSuppliers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingSupplier, setEditingSupplier] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    tax_number: ''
  });
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    fetchSuppliers();
  }, []);

  const fetchSuppliers = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:5000/api/suppliers');
      setSuppliers(response.data);
    } catch (error) {
      setError('خطأ في جلب بيانات الموردين');
      console.error('Error fetching suppliers:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingSupplier) {
        await axios.put(`http://localhost:5000/api/suppliers/${editingSupplier.id}`, formData);
        setMessage('تم تحديث المورد بنجاح');
      } else {
        await axios.post('http://localhost:5000/api/suppliers', formData);
        setMessage('تم إضافة المورد بنجاح');
      }
      
      setFormData({
        name: '',
        phone: '',
        email: '',
        address: '',
        tax_number: ''
      });
      setShowForm(false);
      setEditingSupplier(null);
      fetchSuppliers();
      
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      setError('خطأ في حفظ بيانات المورد');
      console.error('Error saving supplier:', error);
    }
  };

  const handleEdit = (supplier) => {
    setEditingSupplier(supplier);
    setFormData({
      name: supplier.name,
      phone: supplier.phone || '',
      email: supplier.email || '',
      address: supplier.address || '',
      tax_number: supplier.tax_number || ''
    });
    setShowForm(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المورد؟')) {
      try {
        await axios.delete(`http://localhost:5000/api/suppliers/${id}`);
        setMessage('تم حذف المورد بنجاح');
        fetchSuppliers();
        setTimeout(() => setMessage(''), 3000);
      } catch (error) {
        setError('خطأ في حذف المورد');
        console.error('Error deleting supplier:', error);
      }
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      phone: '',
      email: '',
      address: '',
      tax_number: ''
    });
    setShowForm(false);
    setEditingSupplier(null);
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">جاري تحميل بيانات الموردين...</div>
      </div>
    );
  }

  return (
    <div className="container">
      <h1 className="page-title">إدارة الموردين</h1>
      
      {message && <div className="success">{message}</div>}
      {error && <div className="error">{error}</div>}
      
      <div className="card">
        <div className="card-header">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <h3 className="card-title">قائمة الموردين</h3>
            <button 
              className="btn btn-primary"
              onClick={() => setShowForm(!showForm)}
            >
              {showForm ? 'إلغاء' : 'إضافة مورد جديد'}
            </button>
          </div>
        </div>
        
        {showForm && (
          <div className="card-body">
            <form onSubmit={handleSubmit}>
              <div className="row">
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">اسم المورد *</label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="form-control"
                      required
                    />
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">رقم الهاتف</label>
                    <input
                      type="text"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="form-control"
                    />
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">البريد الإلكتروني</label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="form-control"
                    />
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">الرقم الضريبي</label>
                    <input
                      type="text"
                      name="tax_number"
                      value={formData.tax_number}
                      onChange={handleInputChange}
                      className="form-control"
                    />
                  </div>
                </div>
                <div className="col">
                  <div className="form-group">
                    <label className="form-label">العنوان</label>
                    <textarea
                      name="address"
                      value={formData.address}
                      onChange={handleInputChange}
                      className="form-control"
                      rows="3"
                    ></textarea>
                  </div>
                </div>
              </div>
              <div style={{ display: 'flex', gap: '1rem' }}>
                <button type="submit" className="btn btn-success">
                  {editingSupplier ? 'تحديث المورد' : 'إضافة المورد'}
                </button>
                <button type="button" className="btn btn-danger" onClick={resetForm}>
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        )}
        
        <div className="card-body">
          {suppliers.length === 0 ? (
            <p>لا توجد موردين مسجلين حتى الآن</p>
          ) : (
            <table className="table">
              <thead>
                <tr>
                  <th>الاسم</th>
                  <th>الهاتف</th>
                  <th>البريد الإلكتروني</th>
                  <th>الرقم الضريبي</th>
                  <th>الرصيد</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {suppliers.map(supplier => (
                  <tr key={supplier.id}>
                    <td>{supplier.name}</td>
                    <td>{supplier.phone || '-'}</td>
                    <td>{supplier.email || '-'}</td>
                    <td>{supplier.tax_number || '-'}</td>
                    <td>{supplier.balance?.toLocaleString() || '0'} ج.م</td>
                    <td>
                      <button 
                        className="btn btn-warning"
                        onClick={() => handleEdit(supplier)}
                        style={{ marginLeft: '0.5rem' }}
                      >
                        تعديل
                      </button>
                      <button 
                        className="btn btn-danger"
                        onClick={() => handleDelete(supplier.id)}
                      >
                        حذف
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
};

export default Suppliers;
