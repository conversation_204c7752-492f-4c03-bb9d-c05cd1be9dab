{"ast": null, "code": "var _jsxFileName = \"F:\\\\\\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\\\\accounting-system\\\\src\\\\components\\\\Settings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Settings = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('company');\n  const [settings, setSettings] = useState({\n    company: {\n      name: '',\n      address: '',\n      phone: '',\n      email: '',\n      website: '',\n      tax_number: '',\n      commercial_register: '',\n      logo: ''\n    },\n    tax: {\n      default_tax_rate: 14,\n      tax_number: '',\n      tax_enabled: true,\n      tax_inclusive: false\n    },\n    currency: {\n      primary_currency: 'EGP',\n      currency_symbol: 'ج.م',\n      decimal_places: 2,\n      thousands_separator: ',',\n      decimal_separator: '.'\n    },\n    invoice: {\n      invoice_prefix: 'INV-',\n      purchase_prefix: 'PUR-',\n      auto_numbering: true,\n      invoice_terms: '',\n      invoice_footer: ''\n    },\n    system: {\n      language: 'ar',\n      date_format: 'DD/MM/YYYY',\n      time_format: '24',\n      backup_frequency: 'daily',\n      low_stock_threshold: 10\n    },\n    notifications: {\n      email_notifications: true,\n      low_stock_alerts: true,\n      payment_reminders: true,\n      backup_notifications: true\n    }\n  });\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n  useEffect(() => {\n    loadSettings();\n  }, []);\n  const loadSettings = async () => {\n    try {\n      const response = await axios.get('http://localhost:5000/api/settings');\n      if (response.data) {\n        setSettings(prev => ({\n          ...prev,\n          ...response.data\n        }));\n      }\n    } catch (error) {\n      console.error('Error loading settings:', error);\n    }\n  };\n  const handleInputChange = (section, field, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [field]: value\n      }\n    }));\n  };\n  const saveSettings = async () => {\n    try {\n      setLoading(true);\n      await axios.post('http://localhost:5000/api/settings', settings);\n      setMessage('تم حفظ الإعدادات بنجاح');\n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      setError('خطأ في حفظ الإعدادات');\n      console.error('Error saving settings:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const resetSettings = () => {\n    if (window.confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {\n      loadSettings();\n      setMessage('تم إعادة تحميل الإعدادات');\n    }\n  };\n  const exportSettings = () => {\n    const dataStr = JSON.stringify(settings, null, 2);\n    const dataBlob = new Blob([dataStr], {\n      type: 'application/json'\n    });\n    const url = URL.createObjectURL(dataBlob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `settings-backup-${new Date().toISOString().split('T')[0]}.json`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n  const importSettings = event => {\n    const file = event.target.files[0];\n    if (!file) return;\n    const reader = new FileReader();\n    reader.onload = e => {\n      try {\n        const importedSettings = JSON.parse(e.target.result);\n        setSettings(importedSettings);\n        setMessage('تم استيراد الإعدادات بنجاح');\n      } catch (error) {\n        setError('خطأ في قراءة ملف الإعدادات');\n      }\n    };\n    reader.readAsText(file);\n  };\n  const renderCompanySettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"card-title\",\n        children: \"\\uD83C\\uDFE2 \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-body\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u0631\\u0643\\u0629 *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-control\",\n              value: settings.company.name,\n              onChange: e => handleInputChange('company', 'name', e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-control\",\n              value: settings.company.tax_number,\n              onChange: e => handleInputChange('company', 'tax_number', e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-control\",\n              value: settings.company.phone,\n              onChange: e => handleInputChange('company', 'phone', e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              className: \"form-control\",\n              value: settings.company.email,\n              onChange: e => handleInputChange('company', 'email', e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u0645\\u0648\\u0642\\u0639 \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"url\",\n              className: \"form-control\",\n              value: settings.company.website,\n              onChange: e => handleInputChange('company', 'website', e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u0633\\u062C\\u0644 \\u0627\\u0644\\u062A\\u062C\\u0627\\u0631\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-control\",\n              value: settings.company.commercial_register,\n              onChange: e => handleInputChange('company', 'commercial_register', e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              className: \"form-control\",\n              rows: \"3\",\n              value: settings.company.address,\n              onChange: e => handleInputChange('company', 'address', e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 5\n  }, this);\n  const renderTaxSettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"card-title\",\n        children: \"\\uD83D\\uDCB0 \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0636\\u0631\\u0627\\u0626\\u0628\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-body\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0645\\u0639\\u062F\\u0644 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u0629 \\u0627\\u0644\\u0627\\u0641\\u062A\\u0631\\u0627\\u0636\\u064A (%)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              min: \"0\",\n              max: \"100\",\n              step: \"0.01\",\n              className: \"form-control\",\n              value: settings.tax.default_tax_rate,\n              onChange: e => handleInputChange('tax', 'default_tax_rate', parseFloat(e.target.value))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-control\",\n              value: settings.tax.tax_number,\n              onChange: e => handleInputChange('tax', 'tax_number', e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: settings.tax.tax_enabled,\n                onChange: e => handleInputChange('tax', 'tax_enabled', e.target.checked),\n                style: {\n                  marginLeft: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this), \"\\u062A\\u0641\\u0639\\u064A\\u0644 \\u0627\\u0644\\u0636\\u0631\\u0627\\u0626\\u0628\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: settings.tax.tax_inclusive,\n                onChange: e => handleInputChange('tax', 'tax_inclusive', e.target.checked),\n                style: {\n                  marginLeft: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this), \"\\u0627\\u0644\\u0623\\u0633\\u0639\\u0627\\u0631 \\u0634\\u0627\\u0645\\u0644\\u0629 \\u0627\\u0644\\u0636\\u0631\\u064A\\u0628\\u0629\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 222,\n    columnNumber: 5\n  }, this);\n  const renderCurrencySettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"card-title\",\n        children: \"\\uD83D\\uDCB1 \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0639\\u0645\\u0644\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-body\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u0639\\u0645\\u0644\\u0629 \\u0627\\u0644\\u0623\\u0633\\u0627\\u0633\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-control\",\n              value: settings.currency.primary_currency,\n              onChange: e => handleInputChange('currency', 'primary_currency', e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"EGP\",\n                children: \"\\u062C\\u0646\\u064A\\u0647 \\u0645\\u0635\\u0631\\u064A (EGP)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"SAR\",\n                children: \"\\u0631\\u064A\\u0627\\u0644 \\u0633\\u0639\\u0648\\u062F\\u064A (SAR)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"AED\",\n                children: \"\\u062F\\u0631\\u0647\\u0645 \\u0625\\u0645\\u0627\\u0631\\u0627\\u062A\\u064A (AED)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"USD\",\n                children: \"\\u062F\\u0648\\u0644\\u0627\\u0631 \\u0623\\u0645\\u0631\\u064A\\u0643\\u064A (USD)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"EUR\",\n                children: \"\\u064A\\u0648\\u0631\\u0648 (EUR)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0631\\u0645\\u0632 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-control\",\n              value: settings.currency.currency_symbol,\n              onChange: e => handleInputChange('currency', 'currency_symbol', e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0639\\u062F\\u062F \\u0627\\u0644\\u062E\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0639\\u0634\\u0631\\u064A\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-control\",\n              value: settings.currency.decimal_places,\n              onChange: e => handleInputChange('currency', 'decimal_places', parseInt(e.target.value)),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"0\",\n                children: \"0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"1\",\n                children: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"2\",\n                children: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"3\",\n                children: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0641\\u0627\\u0635\\u0644 \\u0627\\u0644\\u0622\\u0644\\u0627\\u0641\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-control\",\n              value: settings.currency.thousands_separator,\n              onChange: e => handleInputChange('currency', 'thousands_separator', e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \",\",\n                children: \",\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \".\",\n                children: \".\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \" \",\n                children: \"\\u0645\\u0633\\u0627\\u0641\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"\\u0628\\u062F\\u0648\\u0646\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0627\\u0644\\u0641\\u0627\\u0635\\u0644 \\u0627\\u0644\\u0639\\u0634\\u0631\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-control\",\n              value: settings.currency.decimal_separator,\n              onChange: e => handleInputChange('currency', 'decimal_separator', e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \".\",\n                children: \".\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \",\",\n                children: \",\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 285,\n    columnNumber: 5\n  }, this);\n  const renderInvoiceSettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"card-title\",\n        children: \"\\uD83D\\uDCC4 \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0641\\u0648\\u0627\\u062A\\u064A\\u0631\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-body\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0628\\u0627\\u062F\\u0626\\u0629 \\u0641\\u0648\\u0627\\u062A\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-control\",\n              value: settings.invoice.invoice_prefix,\n              onChange: e => handleInputChange('invoice', 'invoice_prefix', e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0628\\u0627\\u062F\\u0626\\u0629 \\u0641\\u0648\\u0627\\u062A\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              className: \"form-control\",\n              value: settings.invoice.purchase_prefix,\n              onChange: e => handleInputChange('invoice', 'purchase_prefix', e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: settings.invoice.auto_numbering,\n                onChange: e => handleInputChange('invoice', 'auto_numbering', e.target.checked),\n                style: {\n                  marginLeft: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this), \"\\u062A\\u0631\\u0642\\u064A\\u0645 \\u062A\\u0644\\u0642\\u0627\\u0626\\u064A \\u0644\\u0644\\u0641\\u0648\\u0627\\u062A\\u064A\\u0631\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0634\\u0631\\u0648\\u0637 \\u0648\\u0623\\u062D\\u0643\\u0627\\u0645 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              className: \"form-control\",\n              rows: \"3\",\n              value: settings.invoice.invoice_terms,\n              onChange: e => handleInputChange('invoice', 'invoice_terms', e.target.value),\n              placeholder: \"\\u0623\\u062F\\u062E\\u0644 \\u0627\\u0644\\u0634\\u0631\\u0648\\u0637 \\u0648\\u0627\\u0644\\u0623\\u062D\\u0643\\u0627\\u0645 \\u0627\\u0644\\u062A\\u064A \\u0633\\u062A\\u0638\\u0647\\u0631 \\u0641\\u064A \\u0627\\u0644\\u0641\\u0648\\u0627\\u062A\\u064A\\u0631...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u062A\\u0630\\u064A\\u064A\\u0644 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              className: \"form-control\",\n              rows: \"2\",\n              value: settings.invoice.invoice_footer,\n              onChange: e => handleInputChange('invoice', 'invoice_footer', e.target.value),\n              placeholder: \"\\u0646\\u0635 \\u064A\\u0638\\u0647\\u0631 \\u0641\\u064A \\u0623\\u0633\\u0641\\u0644 \\u0627\\u0644\\u0641\\u0627\\u062A\\u0648\\u0631\\u0629...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 367,\n    columnNumber: 5\n  }, this);\n  const renderSystemSettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"card-title\",\n        children: \"\\u2699\\uFE0F \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 439,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-body\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u0644\\u063A\\u0629 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-control\",\n              value: settings.system.language,\n              onChange: e => handleInputChange('system', 'language', e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"ar\",\n                children: \"\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"en\",\n                children: \"English\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u062A\\u0646\\u0633\\u064A\\u0642 \\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-control\",\n              value: settings.system.date_format,\n              onChange: e => handleInputChange('system', 'date_format', e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"DD/MM/YYYY\",\n                children: \"DD/MM/YYYY\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"MM/DD/YYYY\",\n                children: \"MM/DD/YYYY\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"YYYY-MM-DD\",\n                children: \"YYYY-MM-DD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u062A\\u0646\\u0633\\u064A\\u0642 \\u0627\\u0644\\u0648\\u0642\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-control\",\n              value: settings.system.time_format,\n              onChange: e => handleInputChange('system', 'time_format', e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"24\",\n                children: \"24 \\u0633\\u0627\\u0639\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"12\",\n                children: \"12 \\u0633\\u0627\\u0639\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u062A\\u0643\\u0631\\u0627\\u0631 \\u0627\\u0644\\u0646\\u0633\\u062E \\u0627\\u0644\\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-control\",\n              value: settings.system.backup_frequency,\n              onChange: e => handleInputChange('system', 'backup_frequency', e.target.value),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"daily\",\n                children: \"\\u064A\\u0648\\u0645\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"weekly\",\n                children: \"\\u0623\\u0633\\u0628\\u0648\\u0639\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"monthly\",\n                children: \"\\u0634\\u0647\\u0631\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"manual\",\n                children: \"\\u064A\\u062F\\u0648\\u064A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"\\u062D\\u062F \\u062A\\u0646\\u0628\\u064A\\u0647 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646 \\u0627\\u0644\\u0645\\u0646\\u062E\\u0641\\u0636\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              min: \"0\",\n              className: \"form-control\",\n              value: settings.system.low_stock_threshold,\n              onChange: e => handleInputChange('system', 'low_stock_threshold', parseInt(e.target.value))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 442,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 438,\n    columnNumber: 5\n  }, this);\n  const renderNotificationSettings = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"card\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"card-title\",\n        children: \"\\uD83D\\uDD14 \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u062A\\u0646\\u0628\\u064A\\u0647\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 518,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card-body\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: settings.notifications.email_notifications,\n                onChange: e => handleInputChange('notifications', 'email_notifications', e.target.checked),\n                style: {\n                  marginLeft: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 17\n              }, this), \"\\u062A\\u0646\\u0628\\u064A\\u0647\\u0627\\u062A \\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: settings.notifications.low_stock_alerts,\n                onChange: e => handleInputChange('notifications', 'low_stock_alerts', e.target.checked),\n                style: {\n                  marginLeft: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 17\n              }, this), \"\\u062A\\u0646\\u0628\\u064A\\u0647\\u0627\\u062A \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646 \\u0627\\u0644\\u0645\\u0646\\u062E\\u0641\\u0636\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: settings.notifications.payment_reminders,\n                onChange: e => handleInputChange('notifications', 'payment_reminders', e.target.checked),\n                style: {\n                  marginLeft: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 17\n              }, this), \"\\u062A\\u0630\\u0643\\u064A\\u0631 \\u0628\\u0627\\u0644\\u0645\\u062F\\u0641\\u0648\\u0639\\u0627\\u062A\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-md-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: settings.notifications.backup_notifications,\n                onChange: e => handleInputChange('notifications', 'backup_notifications', e.target.checked),\n                style: {\n                  marginLeft: '0.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 17\n              }, this), \"\\u062A\\u0646\\u0628\\u064A\\u0647\\u0627\\u062A \\u0627\\u0644\\u0646\\u0633\\u062E \\u0627\\u0644\\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 517,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"page-title\",\n      children: \"\\u2699\\uFE0F \\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 582,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 19\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-success\",\n            onClick: saveSettings,\n            disabled: loading,\n            children: loading ? 'جاري الحفظ...' : '💾 حفظ الإعدادات'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-warning\",\n            onClick: resetSettings,\n            children: \"\\uD83D\\uDD04 \\u0625\\u0639\\u0627\\u062F\\u0629 \\u062A\\u062D\\u0645\\u064A\\u0644\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-info\",\n            onClick: exportSettings,\n            children: \"\\uD83D\\uDCE4 \\u062A\\u0635\\u062F\\u064A\\u0631 \\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"btn btn-secondary\",\n            style: {\n              cursor: 'pointer'\n            },\n            children: [\"\\uD83D\\uDCE5 \\u0627\\u0633\\u062A\\u064A\\u0631\\u0627\\u062F \\u0627\\u0644\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A\", /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              accept: \".json\",\n              onChange: importSettings,\n              style: {\n                display: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 588,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"nav nav-tabs\",\n          style: {\n            border: 'none'\n          },\n          children: [{\n            key: 'company',\n            label: '🏢 الشركة',\n            icon: '🏢'\n          }, {\n            key: 'tax',\n            label: '💰 الضرائب',\n            icon: '💰'\n          }, {\n            key: 'currency',\n            label: '💱 العملة',\n            icon: '💱'\n          }, {\n            key: 'invoice',\n            label: '📄 الفواتير',\n            icon: '📄'\n          }, {\n            key: 'system',\n            label: '⚙️ النظام',\n            icon: '⚙️'\n          }, {\n            key: 'notifications',\n            label: '🔔 التنبيهات',\n            icon: '🔔'\n          }].map(tab => /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"nav-item\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `nav-link ${activeTab === tab.key ? 'active' : ''}`,\n              onClick: () => setActiveTab(tab.key),\n              style: {\n                border: 'none',\n                background: activeTab === tab.key ? '#667eea' : 'transparent',\n                color: activeTab === tab.key ? 'white' : '#333',\n                borderRadius: '5px 5px 0 0',\n                margin: '0 2px'\n              },\n              children: tab.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 17\n            }, this)\n          }, tab.key, false, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 615,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: [activeTab === 'company' && renderCompanySettings(), activeTab === 'tax' && renderTaxSettings(), activeTab === 'currency' && renderCurrencySettings(), activeTab === 'invoice' && renderInvoiceSettings(), activeTab === 'system' && renderSystemSettings(), activeTab === 'notifications' && renderNotificationSettings()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 643,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 614,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 581,\n    columnNumber: 5\n  }, this);\n};\n_s(Settings, \"J88Yvn4G3CI/5mAgyF/BO/lZWc0=\");\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "Settings", "_s", "activeTab", "setActiveTab", "settings", "setSettings", "company", "name", "address", "phone", "email", "website", "tax_number", "commercial_register", "logo", "tax", "default_tax_rate", "tax_enabled", "tax_inclusive", "currency", "primary_currency", "currency_symbol", "decimal_places", "thousands_separator", "decimal_separator", "invoice", "invoice_prefix", "purchase_prefix", "auto_numbering", "invoice_terms", "invoice_footer", "system", "language", "date_format", "time_format", "backup_frequency", "low_stock_threshold", "notifications", "email_notifications", "low_stock_alerts", "payment_reminders", "backup_notifications", "loading", "setLoading", "message", "setMessage", "error", "setError", "loadSettings", "response", "get", "data", "prev", "console", "handleInputChange", "section", "field", "value", "saveSettings", "post", "setTimeout", "resetSettings", "window", "confirm", "exportSettings", "dataStr", "JSON", "stringify", "dataBlob", "Blob", "type", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "Date", "toISOString", "split", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "importSettings", "event", "file", "target", "files", "reader", "FileReader", "onload", "e", "importedSettings", "parse", "result", "readAsText", "renderCompanySettings", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "rows", "renderTaxSettings", "min", "max", "step", "parseFloat", "checked", "style", "marginLeft", "renderCurrencySettings", "parseInt", "renderInvoiceSettings", "placeholder", "renderSystemSettings", "renderNotificationSettings", "display", "gap", "flexWrap", "onClick", "disabled", "cursor", "accept", "border", "key", "label", "icon", "map", "tab", "background", "color", "borderRadius", "margin", "_c", "$RefreshReg$"], "sources": ["F:/القيمة المضافة/accounting-system/src/components/Settings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\n\nconst Settings = () => {\n  const [activeTab, setActiveTab] = useState('company');\n  const [settings, setSettings] = useState({\n    company: {\n      name: '',\n      address: '',\n      phone: '',\n      email: '',\n      website: '',\n      tax_number: '',\n      commercial_register: '',\n      logo: ''\n    },\n    tax: {\n      default_tax_rate: 14,\n      tax_number: '',\n      tax_enabled: true,\n      tax_inclusive: false\n    },\n    currency: {\n      primary_currency: 'EGP',\n      currency_symbol: 'ج.م',\n      decimal_places: 2,\n      thousands_separator: ',',\n      decimal_separator: '.'\n    },\n    invoice: {\n      invoice_prefix: 'INV-',\n      purchase_prefix: 'PUR-',\n      auto_numbering: true,\n      invoice_terms: '',\n      invoice_footer: ''\n    },\n    system: {\n      language: 'ar',\n      date_format: 'DD/MM/YYYY',\n      time_format: '24',\n      backup_frequency: 'daily',\n      low_stock_threshold: 10\n    },\n    notifications: {\n      email_notifications: true,\n      low_stock_alerts: true,\n      payment_reminders: true,\n      backup_notifications: true\n    }\n  });\n  const [loading, setLoading] = useState(false);\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    loadSettings();\n  }, []);\n\n  const loadSettings = async () => {\n    try {\n      const response = await axios.get('http://localhost:5000/api/settings');\n      if (response.data) {\n        setSettings(prev => ({ ...prev, ...response.data }));\n      }\n    } catch (error) {\n      console.error('Error loading settings:', error);\n    }\n  };\n\n  const handleInputChange = (section, field, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [field]: value\n      }\n    }));\n  };\n\n  const saveSettings = async () => {\n    try {\n      setLoading(true);\n      await axios.post('http://localhost:5000/api/settings', settings);\n      setMessage('تم حفظ الإعدادات بنجاح');\n      setTimeout(() => setMessage(''), 3000);\n    } catch (error) {\n      setError('خطأ في حفظ الإعدادات');\n      console.error('Error saving settings:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const resetSettings = () => {\n    if (window.confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {\n      loadSettings();\n      setMessage('تم إعادة تحميل الإعدادات');\n    }\n  };\n\n  const exportSettings = () => {\n    const dataStr = JSON.stringify(settings, null, 2);\n    const dataBlob = new Blob([dataStr], { type: 'application/json' });\n    const url = URL.createObjectURL(dataBlob);\n    \n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `settings-backup-${new Date().toISOString().split('T')[0]}.json`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n\n  const importSettings = (event) => {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      try {\n        const importedSettings = JSON.parse(e.target.result);\n        setSettings(importedSettings);\n        setMessage('تم استيراد الإعدادات بنجاح');\n      } catch (error) {\n        setError('خطأ في قراءة ملف الإعدادات');\n      }\n    };\n    reader.readAsText(file);\n  };\n\n  const renderCompanySettings = () => (\n    <div className=\"card\">\n      <div className=\"card-header\">\n        <h3 className=\"card-title\">🏢 إعدادات الشركة</h3>\n      </div>\n      <div className=\"card-body\">\n        <div className=\"row\">\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">اسم الشركة *</label>\n              <input\n                type=\"text\"\n                className=\"form-control\"\n                value={settings.company.name}\n                onChange={(e) => handleInputChange('company', 'name', e.target.value)}\n              />\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">الرقم الضريبي</label>\n              <input\n                type=\"text\"\n                className=\"form-control\"\n                value={settings.company.tax_number}\n                onChange={(e) => handleInputChange('company', 'tax_number', e.target.value)}\n              />\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">رقم الهاتف</label>\n              <input\n                type=\"text\"\n                className=\"form-control\"\n                value={settings.company.phone}\n                onChange={(e) => handleInputChange('company', 'phone', e.target.value)}\n              />\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">البريد الإلكتروني</label>\n              <input\n                type=\"email\"\n                className=\"form-control\"\n                value={settings.company.email}\n                onChange={(e) => handleInputChange('company', 'email', e.target.value)}\n              />\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">الموقع الإلكتروني</label>\n              <input\n                type=\"url\"\n                className=\"form-control\"\n                value={settings.company.website}\n                onChange={(e) => handleInputChange('company', 'website', e.target.value)}\n              />\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">السجل التجاري</label>\n              <input\n                type=\"text\"\n                className=\"form-control\"\n                value={settings.company.commercial_register}\n                onChange={(e) => handleInputChange('company', 'commercial_register', e.target.value)}\n              />\n            </div>\n          </div>\n          <div className=\"col\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">العنوان</label>\n              <textarea\n                className=\"form-control\"\n                rows=\"3\"\n                value={settings.company.address}\n                onChange={(e) => handleInputChange('company', 'address', e.target.value)}\n              ></textarea>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderTaxSettings = () => (\n    <div className=\"card\">\n      <div className=\"card-header\">\n        <h3 className=\"card-title\">💰 إعدادات الضرائب</h3>\n      </div>\n      <div className=\"card-body\">\n        <div className=\"row\">\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">معدل الضريبة الافتراضي (%)</label>\n              <input\n                type=\"number\"\n                min=\"0\"\n                max=\"100\"\n                step=\"0.01\"\n                className=\"form-control\"\n                value={settings.tax.default_tax_rate}\n                onChange={(e) => handleInputChange('tax', 'default_tax_rate', parseFloat(e.target.value))}\n              />\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">الرقم الضريبي</label>\n              <input\n                type=\"text\"\n                className=\"form-control\"\n                value={settings.tax.tax_number}\n                onChange={(e) => handleInputChange('tax', 'tax_number', e.target.value)}\n              />\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.tax.tax_enabled}\n                  onChange={(e) => handleInputChange('tax', 'tax_enabled', e.target.checked)}\n                  style={{ marginLeft: '0.5rem' }}\n                />\n                تفعيل الضرائب\n              </label>\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.tax.tax_inclusive}\n                  onChange={(e) => handleInputChange('tax', 'tax_inclusive', e.target.checked)}\n                  style={{ marginLeft: '0.5rem' }}\n                />\n                الأسعار شاملة الضريبة\n              </label>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderCurrencySettings = () => (\n    <div className=\"card\">\n      <div className=\"card-header\">\n        <h3 className=\"card-title\">💱 إعدادات العملة</h3>\n      </div>\n      <div className=\"card-body\">\n        <div className=\"row\">\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">العملة الأساسية</label>\n              <select\n                className=\"form-control\"\n                value={settings.currency.primary_currency}\n                onChange={(e) => handleInputChange('currency', 'primary_currency', e.target.value)}\n              >\n                <option value=\"EGP\">جنيه مصري (EGP)</option>\n                <option value=\"SAR\">ريال سعودي (SAR)</option>\n                <option value=\"AED\">درهم إماراتي (AED)</option>\n                <option value=\"USD\">دولار أمريكي (USD)</option>\n                <option value=\"EUR\">يورو (EUR)</option>\n              </select>\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">رمز العملة</label>\n              <input\n                type=\"text\"\n                className=\"form-control\"\n                value={settings.currency.currency_symbol}\n                onChange={(e) => handleInputChange('currency', 'currency_symbol', e.target.value)}\n              />\n            </div>\n          </div>\n          <div className=\"col-md-4\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">عدد الخانات العشرية</label>\n              <select\n                className=\"form-control\"\n                value={settings.currency.decimal_places}\n                onChange={(e) => handleInputChange('currency', 'decimal_places', parseInt(e.target.value))}\n              >\n                <option value=\"0\">0</option>\n                <option value=\"1\">1</option>\n                <option value=\"2\">2</option>\n                <option value=\"3\">3</option>\n              </select>\n            </div>\n          </div>\n          <div className=\"col-md-4\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">فاصل الآلاف</label>\n              <select\n                className=\"form-control\"\n                value={settings.currency.thousands_separator}\n                onChange={(e) => handleInputChange('currency', 'thousands_separator', e.target.value)}\n              >\n                <option value=\",\">,</option>\n                <option value=\".\">.</option>\n                <option value=\" \">مسافة</option>\n                <option value=\"\">بدون</option>\n              </select>\n            </div>\n          </div>\n          <div className=\"col-md-4\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">الفاصل العشري</label>\n              <select\n                className=\"form-control\"\n                value={settings.currency.decimal_separator}\n                onChange={(e) => handleInputChange('currency', 'decimal_separator', e.target.value)}\n              >\n                <option value=\".\">.</option>\n                <option value=\",\">,</option>\n              </select>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderInvoiceSettings = () => (\n    <div className=\"card\">\n      <div className=\"card-header\">\n        <h3 className=\"card-title\">📄 إعدادات الفواتير</h3>\n      </div>\n      <div className=\"card-body\">\n        <div className=\"row\">\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">بادئة فواتير المبيعات</label>\n              <input\n                type=\"text\"\n                className=\"form-control\"\n                value={settings.invoice.invoice_prefix}\n                onChange={(e) => handleInputChange('invoice', 'invoice_prefix', e.target.value)}\n              />\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">بادئة فواتير المشتريات</label>\n              <input\n                type=\"text\"\n                className=\"form-control\"\n                value={settings.invoice.purchase_prefix}\n                onChange={(e) => handleInputChange('invoice', 'purchase_prefix', e.target.value)}\n              />\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.invoice.auto_numbering}\n                  onChange={(e) => handleInputChange('invoice', 'auto_numbering', e.target.checked)}\n                  style={{ marginLeft: '0.5rem' }}\n                />\n                ترقيم تلقائي للفواتير\n              </label>\n            </div>\n          </div>\n          <div className=\"col\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">شروط وأحكام الفاتورة</label>\n              <textarea\n                className=\"form-control\"\n                rows=\"3\"\n                value={settings.invoice.invoice_terms}\n                onChange={(e) => handleInputChange('invoice', 'invoice_terms', e.target.value)}\n                placeholder=\"أدخل الشروط والأحكام التي ستظهر في الفواتير...\"\n              ></textarea>\n            </div>\n          </div>\n          <div className=\"col\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">تذييل الفاتورة</label>\n              <textarea\n                className=\"form-control\"\n                rows=\"2\"\n                value={settings.invoice.invoice_footer}\n                onChange={(e) => handleInputChange('invoice', 'invoice_footer', e.target.value)}\n                placeholder=\"نص يظهر في أسفل الفاتورة...\"\n              ></textarea>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderSystemSettings = () => (\n    <div className=\"card\">\n      <div className=\"card-header\">\n        <h3 className=\"card-title\">⚙️ إعدادات النظام</h3>\n      </div>\n      <div className=\"card-body\">\n        <div className=\"row\">\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">لغة النظام</label>\n              <select\n                className=\"form-control\"\n                value={settings.system.language}\n                onChange={(e) => handleInputChange('system', 'language', e.target.value)}\n              >\n                <option value=\"ar\">العربية</option>\n                <option value=\"en\">English</option>\n              </select>\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">تنسيق التاريخ</label>\n              <select\n                className=\"form-control\"\n                value={settings.system.date_format}\n                onChange={(e) => handleInputChange('system', 'date_format', e.target.value)}\n              >\n                <option value=\"DD/MM/YYYY\">DD/MM/YYYY</option>\n                <option value=\"MM/DD/YYYY\">MM/DD/YYYY</option>\n                <option value=\"YYYY-MM-DD\">YYYY-MM-DD</option>\n              </select>\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">تنسيق الوقت</label>\n              <select\n                className=\"form-control\"\n                value={settings.system.time_format}\n                onChange={(e) => handleInputChange('system', 'time_format', e.target.value)}\n              >\n                <option value=\"24\">24 ساعة</option>\n                <option value=\"12\">12 ساعة</option>\n              </select>\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">تكرار النسخ الاحتياطي</label>\n              <select\n                className=\"form-control\"\n                value={settings.system.backup_frequency}\n                onChange={(e) => handleInputChange('system', 'backup_frequency', e.target.value)}\n              >\n                <option value=\"daily\">يومي</option>\n                <option value=\"weekly\">أسبوعي</option>\n                <option value=\"monthly\">شهري</option>\n                <option value=\"manual\">يدوي</option>\n              </select>\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">حد تنبيه المخزون المنخفض</label>\n              <input\n                type=\"number\"\n                min=\"0\"\n                className=\"form-control\"\n                value={settings.system.low_stock_threshold}\n                onChange={(e) => handleInputChange('system', 'low_stock_threshold', parseInt(e.target.value))}\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderNotificationSettings = () => (\n    <div className=\"card\">\n      <div className=\"card-header\">\n        <h3 className=\"card-title\">🔔 إعدادات التنبيهات</h3>\n      </div>\n      <div className=\"card-body\">\n        <div className=\"row\">\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.notifications.email_notifications}\n                  onChange={(e) => handleInputChange('notifications', 'email_notifications', e.target.checked)}\n                  style={{ marginLeft: '0.5rem' }}\n                />\n                تنبيهات البريد الإلكتروني\n              </label>\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.notifications.low_stock_alerts}\n                  onChange={(e) => handleInputChange('notifications', 'low_stock_alerts', e.target.checked)}\n                  style={{ marginLeft: '0.5rem' }}\n                />\n                تنبيهات المخزون المنخفض\n              </label>\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.notifications.payment_reminders}\n                  onChange={(e) => handleInputChange('notifications', 'payment_reminders', e.target.checked)}\n                  style={{ marginLeft: '0.5rem' }}\n                />\n                تذكير بالمدفوعات\n              </label>\n            </div>\n          </div>\n          <div className=\"col-md-6\">\n            <div className=\"form-group\">\n              <label className=\"form-label\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.notifications.backup_notifications}\n                  onChange={(e) => handleInputChange('notifications', 'backup_notifications', e.target.checked)}\n                  style={{ marginLeft: '0.5rem' }}\n                />\n                تنبيهات النسخ الاحتياطي\n              </label>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"container\">\n      <h1 className=\"page-title\">⚙️ إعدادات النظام</h1>\n      \n      {message && <div className=\"success\">{message}</div>}\n      {error && <div className=\"error\">{error}</div>}\n      \n      {/* أزرار الإجراءات */}\n      <div className=\"card\">\n        <div className=\"card-body\">\n          <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>\n            <button className=\"btn btn-success\" onClick={saveSettings} disabled={loading}>\n              {loading ? 'جاري الحفظ...' : '💾 حفظ الإعدادات'}\n            </button>\n            <button className=\"btn btn-warning\" onClick={resetSettings}>\n              🔄 إعادة تحميل\n            </button>\n            <button className=\"btn btn-info\" onClick={exportSettings}>\n              📤 تصدير الإعدادات\n            </button>\n            <label className=\"btn btn-secondary\" style={{ cursor: 'pointer' }}>\n              📥 استيراد الإعدادات\n              <input\n                type=\"file\"\n                accept=\".json\"\n                onChange={importSettings}\n                style={{ display: 'none' }}\n              />\n            </label>\n          </div>\n        </div>\n      </div>\n\n      {/* تبويبات الإعدادات */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <ul className=\"nav nav-tabs\" style={{ border: 'none' }}>\n            {[\n              { key: 'company', label: '🏢 الشركة', icon: '🏢' },\n              { key: 'tax', label: '💰 الضرائب', icon: '💰' },\n              { key: 'currency', label: '💱 العملة', icon: '💱' },\n              { key: 'invoice', label: '📄 الفواتير', icon: '📄' },\n              { key: 'system', label: '⚙️ النظام', icon: '⚙️' },\n              { key: 'notifications', label: '🔔 التنبيهات', icon: '🔔' }\n            ].map(tab => (\n              <li key={tab.key} className=\"nav-item\">\n                <button\n                  className={`nav-link ${activeTab === tab.key ? 'active' : ''}`}\n                  onClick={() => setActiveTab(tab.key)}\n                  style={{\n                    border: 'none',\n                    background: activeTab === tab.key ? '#667eea' : 'transparent',\n                    color: activeTab === tab.key ? 'white' : '#333',\n                    borderRadius: '5px 5px 0 0',\n                    margin: '0 2px'\n                  }}\n                >\n                  {tab.label}\n                </button>\n              </li>\n            ))}\n          </ul>\n        </div>\n        <div className=\"card-body\">\n          {activeTab === 'company' && renderCompanySettings()}\n          {activeTab === 'tax' && renderTaxSettings()}\n          {activeTab === 'currency' && renderCurrencySettings()}\n          {activeTab === 'invoice' && renderInvoiceSettings()}\n          {activeTab === 'system' && renderSystemSettings()}\n          {activeTab === 'notifications' && renderNotificationSettings()}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC;IACvCW,OAAO,EAAE;MACPC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,EAAE;MACdC,mBAAmB,EAAE,EAAE;MACvBC,IAAI,EAAE;IACR,CAAC;IACDC,GAAG,EAAE;MACHC,gBAAgB,EAAE,EAAE;MACpBJ,UAAU,EAAE,EAAE;MACdK,WAAW,EAAE,IAAI;MACjBC,aAAa,EAAE;IACjB,CAAC;IACDC,QAAQ,EAAE;MACRC,gBAAgB,EAAE,KAAK;MACvBC,eAAe,EAAE,KAAK;MACtBC,cAAc,EAAE,CAAC;MACjBC,mBAAmB,EAAE,GAAG;MACxBC,iBAAiB,EAAE;IACrB,CAAC;IACDC,OAAO,EAAE;MACPC,cAAc,EAAE,MAAM;MACtBC,eAAe,EAAE,MAAM;MACvBC,cAAc,EAAE,IAAI;MACpBC,aAAa,EAAE,EAAE;MACjBC,cAAc,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE;MACNC,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE,YAAY;MACzBC,WAAW,EAAE,IAAI;MACjBC,gBAAgB,EAAE,OAAO;MACzBC,mBAAmB,EAAE;IACvB,CAAC;IACDC,aAAa,EAAE;MACbC,mBAAmB,EAAE,IAAI;MACzBC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,IAAI;MACvBC,oBAAoB,EAAE;IACxB;EACF,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmD,KAAK,EAAEC,QAAQ,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACdoD,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMpD,KAAK,CAACqD,GAAG,CAAC,oCAAoC,CAAC;MACtE,IAAID,QAAQ,CAACE,IAAI,EAAE;QACjB9C,WAAW,CAAC+C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,GAAGH,QAAQ,CAACE;QAAK,CAAC,CAAC,CAAC;MACtD;IACF,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdO,OAAO,CAACP,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMQ,iBAAiB,GAAGA,CAACC,OAAO,EAAEC,KAAK,EAAEC,KAAK,KAAK;IACnDpD,WAAW,CAAC+C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACG,OAAO,GAAG;QACT,GAAGH,IAAI,CAACG,OAAO,CAAC;QAChB,CAACC,KAAK,GAAGC;MACX;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM9C,KAAK,CAAC8D,IAAI,CAAC,oCAAoC,EAAEvD,QAAQ,CAAC;MAChEyC,UAAU,CAAC,wBAAwB,CAAC;MACpCe,UAAU,CAAC,MAAMf,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,QAAQ,CAAC,sBAAsB,CAAC;MAChCM,OAAO,CAACP,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIC,MAAM,CAACC,OAAO,CAAC,6CAA6C,CAAC,EAAE;MACjEf,YAAY,CAAC,CAAC;MACdH,UAAU,CAAC,0BAA0B,CAAC;IACxC;EACF,CAAC;EAED,MAAMmB,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,OAAO,GAAGC,IAAI,CAACC,SAAS,CAAC/D,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;IACjD,MAAMgE,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAACJ,OAAO,CAAC,EAAE;MAAEK,IAAI,EAAE;IAAmB,CAAC,CAAC;IAClE,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,QAAQ,CAAC;IAEzC,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;IACfG,IAAI,CAACI,QAAQ,GAAG,mBAAmB,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;IAChFN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,IAAI,CAAC;IAC/BA,IAAI,CAACU,KAAK,CAAC,CAAC;IACZT,QAAQ,CAACO,IAAI,CAACG,WAAW,CAACX,IAAI,CAAC;IAC/BF,GAAG,CAACc,eAAe,CAACf,GAAG,CAAC;EAC1B,CAAC;EAED,MAAMgB,cAAc,GAAIC,KAAK,IAAK;IAChC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACF,IAAI,EAAE;IAEX,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAC,IAAK;MACrB,IAAI;QACF,MAAMC,gBAAgB,GAAG9B,IAAI,CAAC+B,KAAK,CAACF,CAAC,CAACL,MAAM,CAACQ,MAAM,CAAC;QACpD7F,WAAW,CAAC2F,gBAAgB,CAAC;QAC7BnD,UAAU,CAAC,4BAA4B,CAAC;MAC1C,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,QAAQ,CAAC,4BAA4B,CAAC;MACxC;IACF,CAAC;IACD6C,MAAM,CAACO,UAAU,CAACV,IAAI,CAAC;EACzB,CAAC;EAED,MAAMW,qBAAqB,GAAGA,CAAA,kBAC5BrG,OAAA;IAAKsG,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnBvG,OAAA;MAAKsG,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BvG,OAAA;QAAIsG,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eACN3G,OAAA;MAAKsG,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBvG,OAAA;QAAKsG,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAClBvG,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClD3G,OAAA;cACEuE,IAAI,EAAC,MAAM;cACX+B,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAACE,OAAO,CAACC,IAAK;cAC7BoG,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,SAAS,EAAE,MAAM,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK;YAAE;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnD3G,OAAA;cACEuE,IAAI,EAAC,MAAM;cACX+B,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAACE,OAAO,CAACM,UAAW;cACnC+F,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,SAAS,EAAE,YAAY,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK;YAAE;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChD3G,OAAA;cACEuE,IAAI,EAAC,MAAM;cACX+B,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAACE,OAAO,CAACG,KAAM;cAC9BkG,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK;YAAE;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvD3G,OAAA;cACEuE,IAAI,EAAC,OAAO;cACZ+B,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAACE,OAAO,CAACI,KAAM;cAC9BiG,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK;YAAE;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvD3G,OAAA;cACEuE,IAAI,EAAC,KAAK;cACV+B,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAACE,OAAO,CAACK,OAAQ;cAChCgG,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,SAAS,EAAE,SAAS,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK;YAAE;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnD3G,OAAA;cACEuE,IAAI,EAAC,MAAM;cACX+B,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAACE,OAAO,CAACO,mBAAoB;cAC5C8F,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,SAAS,EAAE,qBAAqB,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK;YAAE;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7C3G,OAAA;cACEsG,SAAS,EAAC,cAAc;cACxBO,IAAI,EAAC,GAAG;cACRnD,KAAK,EAAErD,QAAQ,CAACE,OAAO,CAACE,OAAQ;cAChCmG,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,SAAS,EAAE,SAAS,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK;YAAE;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMG,iBAAiB,GAAGA,CAAA,kBACxB9G,OAAA;IAAKsG,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnBvG,OAAA;MAAKsG,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BvG,OAAA;QAAIsG,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC,eACN3G,OAAA;MAAKsG,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBvG,OAAA;QAAKsG,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAClBvG,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChE3G,OAAA;cACEuE,IAAI,EAAC,QAAQ;cACbwC,GAAG,EAAC,GAAG;cACPC,GAAG,EAAC,KAAK;cACTC,IAAI,EAAC,MAAM;cACXX,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAACW,GAAG,CAACC,gBAAiB;cACrC2F,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,KAAK,EAAE,kBAAkB,EAAE2D,UAAU,CAAClB,CAAC,CAACL,MAAM,CAACjC,KAAK,CAAC;YAAE;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnD3G,OAAA;cACEuE,IAAI,EAAC,MAAM;cACX+B,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAACW,GAAG,CAACH,UAAW;cAC/B+F,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,KAAK,EAAE,YAAY,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK;YAAE;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,eACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAC3BvG,OAAA;gBACEuE,IAAI,EAAC,UAAU;gBACf4C,OAAO,EAAE9G,QAAQ,CAACW,GAAG,CAACE,WAAY;gBAClC0F,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,KAAK,EAAE,aAAa,EAAEyC,CAAC,CAACL,MAAM,CAACwB,OAAO,CAAE;gBAC3EC,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAAS;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,6EAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,eACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAC3BvG,OAAA;gBACEuE,IAAI,EAAC,UAAU;gBACf4C,OAAO,EAAE9G,QAAQ,CAACW,GAAG,CAACG,aAAc;gBACpCyF,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,KAAK,EAAE,eAAe,EAAEyC,CAAC,CAACL,MAAM,CAACwB,OAAO,CAAE;gBAC7EC,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAAS;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,wHAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMW,sBAAsB,GAAGA,CAAA,kBAC7BtH,OAAA;IAAKsG,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnBvG,OAAA;MAAKsG,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BvG,OAAA;QAAIsG,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eACN3G,OAAA;MAAKsG,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBvG,OAAA;QAAKsG,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAClBvG,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrD3G,OAAA;cACEsG,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAACe,QAAQ,CAACC,gBAAiB;cAC1CuF,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,UAAU,EAAE,kBAAkB,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK,CAAE;cAAA6C,QAAA,gBAEnFvG,OAAA;gBAAQ0D,KAAK,EAAC,KAAK;gBAAA6C,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5C3G,OAAA;gBAAQ0D,KAAK,EAAC,KAAK;gBAAA6C,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7C3G,OAAA;gBAAQ0D,KAAK,EAAC,KAAK;gBAAA6C,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/C3G,OAAA;gBAAQ0D,KAAK,EAAC,KAAK;gBAAA6C,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/C3G,OAAA;gBAAQ0D,KAAK,EAAC,KAAK;gBAAA6C,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChD3G,OAAA;cACEuE,IAAI,EAAC,MAAM;cACX+B,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAACe,QAAQ,CAACE,eAAgB;cACzCsF,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,UAAU,EAAE,iBAAiB,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK;YAAE;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzD3G,OAAA;cACEsG,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAACe,QAAQ,CAACG,cAAe;cACxCqF,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,UAAU,EAAE,gBAAgB,EAAEgE,QAAQ,CAACvB,CAAC,CAACL,MAAM,CAACjC,KAAK,CAAC,CAAE;cAAA6C,QAAA,gBAE3FvG,OAAA;gBAAQ0D,KAAK,EAAC,GAAG;gBAAA6C,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5B3G,OAAA;gBAAQ0D,KAAK,EAAC,GAAG;gBAAA6C,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5B3G,OAAA;gBAAQ0D,KAAK,EAAC,GAAG;gBAAA6C,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5B3G,OAAA;gBAAQ0D,KAAK,EAAC,GAAG;gBAAA6C,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjD3G,OAAA;cACEsG,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAACe,QAAQ,CAACI,mBAAoB;cAC7CoF,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,UAAU,EAAE,qBAAqB,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK,CAAE;cAAA6C,QAAA,gBAEtFvG,OAAA;gBAAQ0D,KAAK,EAAC,GAAG;gBAAA6C,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5B3G,OAAA;gBAAQ0D,KAAK,EAAC,GAAG;gBAAA6C,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5B3G,OAAA;gBAAQ0D,KAAK,EAAC,GAAG;gBAAA6C,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChC3G,OAAA;gBAAQ0D,KAAK,EAAC,EAAE;gBAAA6C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnD3G,OAAA;cACEsG,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAACe,QAAQ,CAACK,iBAAkB;cAC3CmF,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,UAAU,EAAE,mBAAmB,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK,CAAE;cAAA6C,QAAA,gBAEpFvG,OAAA;gBAAQ0D,KAAK,EAAC,GAAG;gBAAA6C,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5B3G,OAAA;gBAAQ0D,KAAK,EAAC,GAAG;gBAAA6C,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMa,qBAAqB,GAAGA,CAAA,kBAC5BxH,OAAA;IAAKsG,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnBvG,OAAA;MAAKsG,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BvG,OAAA;QAAIsG,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,eACN3G,OAAA;MAAKsG,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBvG,OAAA;QAAKsG,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAClBvG,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3D3G,OAAA;cACEuE,IAAI,EAAC,MAAM;cACX+B,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAACqB,OAAO,CAACC,cAAe;cACvCiF,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,SAAS,EAAE,gBAAgB,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK;YAAE;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5D3G,OAAA;cACEuE,IAAI,EAAC,MAAM;cACX+B,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAACqB,OAAO,CAACE,eAAgB;cACxCgF,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,SAAS,EAAE,iBAAiB,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK;YAAE;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,eACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAC3BvG,OAAA;gBACEuE,IAAI,EAAC,UAAU;gBACf4C,OAAO,EAAE9G,QAAQ,CAACqB,OAAO,CAACG,cAAe;gBACzC+E,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,SAAS,EAAE,gBAAgB,EAAEyC,CAAC,CAACL,MAAM,CAACwB,OAAO,CAAE;gBAClFC,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAAS;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,wHAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1D3G,OAAA;cACEsG,SAAS,EAAC,cAAc;cACxBO,IAAI,EAAC,GAAG;cACRnD,KAAK,EAAErD,QAAQ,CAACqB,OAAO,CAACI,aAAc;cACtC8E,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,SAAS,EAAE,eAAe,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK,CAAE;cAC/E+D,WAAW,EAAC;YAAgD;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,KAAK;UAAAC,QAAA,eAClBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpD3G,OAAA;cACEsG,SAAS,EAAC,cAAc;cACxBO,IAAI,EAAC,GAAG;cACRnD,KAAK,EAAErD,QAAQ,CAACqB,OAAO,CAACK,cAAe;cACvC6E,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,SAAS,EAAE,gBAAgB,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK,CAAE;cAChF+D,WAAW,EAAC;YAA6B;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMe,oBAAoB,GAAGA,CAAA,kBAC3B1H,OAAA;IAAKsG,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnBvG,OAAA;MAAKsG,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BvG,OAAA;QAAIsG,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eACN3G,OAAA;MAAKsG,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBvG,OAAA;QAAKsG,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAClBvG,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChD3G,OAAA;cACEsG,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAAC2B,MAAM,CAACC,QAAS;cAChC2E,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,QAAQ,EAAE,UAAU,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK,CAAE;cAAA6C,QAAA,gBAEzEvG,OAAA;gBAAQ0D,KAAK,EAAC,IAAI;gBAAA6C,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC3G,OAAA;gBAAQ0D,KAAK,EAAC,IAAI;gBAAA6C,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnD3G,OAAA;cACEsG,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAAC2B,MAAM,CAACE,WAAY;cACnC0E,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,QAAQ,EAAE,aAAa,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK,CAAE;cAAA6C,QAAA,gBAE5EvG,OAAA;gBAAQ0D,KAAK,EAAC,YAAY;gBAAA6C,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9C3G,OAAA;gBAAQ0D,KAAK,EAAC,YAAY;gBAAA6C,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9C3G,OAAA;gBAAQ0D,KAAK,EAAC,YAAY;gBAAA6C,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjD3G,OAAA;cACEsG,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAAC2B,MAAM,CAACG,WAAY;cACnCyE,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,QAAQ,EAAE,aAAa,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK,CAAE;cAAA6C,QAAA,gBAE5EvG,OAAA;gBAAQ0D,KAAK,EAAC,IAAI;gBAAA6C,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC3G,OAAA;gBAAQ0D,KAAK,EAAC,IAAI;gBAAA6C,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3D3G,OAAA;cACEsG,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAAC2B,MAAM,CAACI,gBAAiB;cACxCwE,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,QAAQ,EAAE,kBAAkB,EAAEyC,CAAC,CAACL,MAAM,CAACjC,KAAK,CAAE;cAAA6C,QAAA,gBAEjFvG,OAAA;gBAAQ0D,KAAK,EAAC,OAAO;gBAAA6C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnC3G,OAAA;gBAAQ0D,KAAK,EAAC,QAAQ;gBAAA6C,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtC3G,OAAA;gBAAQ0D,KAAK,EAAC,SAAS;gBAAA6C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACrC3G,OAAA;gBAAQ0D,KAAK,EAAC,QAAQ;gBAAA6C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9D3G,OAAA;cACEuE,IAAI,EAAC,QAAQ;cACbwC,GAAG,EAAC,GAAG;cACPT,SAAS,EAAC,cAAc;cACxB5C,KAAK,EAAErD,QAAQ,CAAC2B,MAAM,CAACK,mBAAoB;cAC3CuE,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,QAAQ,EAAE,qBAAqB,EAAEgE,QAAQ,CAACvB,CAAC,CAACL,MAAM,CAACjC,KAAK,CAAC;YAAE;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMgB,0BAA0B,GAAGA,CAAA,kBACjC3H,OAAA;IAAKsG,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACnBvG,OAAA;MAAKsG,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BvG,OAAA;QAAIsG,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eACN3G,OAAA;MAAKsG,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBvG,OAAA;QAAKsG,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAClBvG,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,eACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAC3BvG,OAAA;gBACEuE,IAAI,EAAC,UAAU;gBACf4C,OAAO,EAAE9G,QAAQ,CAACiC,aAAa,CAACC,mBAAoB;gBACpDqE,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,eAAe,EAAE,qBAAqB,EAAEyC,CAAC,CAACL,MAAM,CAACwB,OAAO,CAAE;gBAC7FC,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAAS;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,gJAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,eACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAC3BvG,OAAA;gBACEuE,IAAI,EAAC,UAAU;gBACf4C,OAAO,EAAE9G,QAAQ,CAACiC,aAAa,CAACE,gBAAiB;gBACjDoE,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,eAAe,EAAE,kBAAkB,EAAEyC,CAAC,CAACL,MAAM,CAACwB,OAAO,CAAE;gBAC1FC,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAAS;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,oIAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,eACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAC3BvG,OAAA;gBACEuE,IAAI,EAAC,UAAU;gBACf4C,OAAO,EAAE9G,QAAQ,CAACiC,aAAa,CAACG,iBAAkB;gBAClDmE,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,eAAe,EAAE,mBAAmB,EAAEyC,CAAC,CAACL,MAAM,CAACwB,OAAO,CAAE;gBAC3FC,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAAS;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,+FAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3G,OAAA;UAAKsG,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBvG,OAAA;YAAKsG,SAAS,EAAC,YAAY;YAAAC,QAAA,eACzBvG,OAAA;cAAOsG,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAC3BvG,OAAA;gBACEuE,IAAI,EAAC,UAAU;gBACf4C,OAAO,EAAE9G,QAAQ,CAACiC,aAAa,CAACI,oBAAqB;gBACrDkE,QAAQ,EAAGZ,CAAC,IAAKzC,iBAAiB,CAAC,eAAe,EAAE,sBAAsB,EAAEyC,CAAC,CAACL,MAAM,CAACwB,OAAO,CAAE;gBAC9FC,KAAK,EAAE;kBAAEC,UAAU,EAAE;gBAAS;cAAE;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,oIAEJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACE3G,OAAA;IAAKsG,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBvG,OAAA;MAAIsG,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAEhD9D,OAAO,iBAAI7C,OAAA;MAAKsG,SAAS,EAAC,SAAS;MAAAC,QAAA,EAAE1D;IAAO;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACnD5D,KAAK,iBAAI/C,OAAA;MAAKsG,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAExD;IAAK;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAG9C3G,OAAA;MAAKsG,SAAS,EAAC,MAAM;MAAAC,QAAA,eACnBvG,OAAA;QAAKsG,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBvG,OAAA;UAAKoH,KAAK,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE,MAAM;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAvB,QAAA,gBAC7DvG,OAAA;YAAQsG,SAAS,EAAC,iBAAiB;YAACyB,OAAO,EAAEpE,YAAa;YAACqE,QAAQ,EAAErF,OAAQ;YAAA4D,QAAA,EAC1E5D,OAAO,GAAG,eAAe,GAAG;UAAkB;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACT3G,OAAA;YAAQsG,SAAS,EAAC,iBAAiB;YAACyB,OAAO,EAAEjE,aAAc;YAAAyC,QAAA,EAAC;UAE5D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3G,OAAA;YAAQsG,SAAS,EAAC,cAAc;YAACyB,OAAO,EAAE9D,cAAe;YAAAsC,QAAA,EAAC;UAE1D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3G,OAAA;YAAOsG,SAAS,EAAC,mBAAmB;YAACc,KAAK,EAAE;cAAEa,MAAM,EAAE;YAAU,CAAE;YAAA1B,QAAA,GAAC,gHAEjE,eAAAvG,OAAA;cACEuE,IAAI,EAAC,MAAM;cACX2D,MAAM,EAAC,OAAO;cACdtB,QAAQ,EAAEpB,cAAe;cACzB4B,KAAK,EAAE;gBAAEQ,OAAO,EAAE;cAAO;YAAE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3G,OAAA;MAAKsG,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBvG,OAAA;QAAKsG,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BvG,OAAA;UAAIsG,SAAS,EAAC,cAAc;UAACc,KAAK,EAAE;YAAEe,MAAM,EAAE;UAAO,CAAE;UAAA5B,QAAA,EACpD,CACC;YAAE6B,GAAG,EAAE,SAAS;YAAEC,KAAK,EAAE,WAAW;YAAEC,IAAI,EAAE;UAAK,CAAC,EAClD;YAAEF,GAAG,EAAE,KAAK;YAAEC,KAAK,EAAE,YAAY;YAAEC,IAAI,EAAE;UAAK,CAAC,EAC/C;YAAEF,GAAG,EAAE,UAAU;YAAEC,KAAK,EAAE,WAAW;YAAEC,IAAI,EAAE;UAAK,CAAC,EACnD;YAAEF,GAAG,EAAE,SAAS;YAAEC,KAAK,EAAE,aAAa;YAAEC,IAAI,EAAE;UAAK,CAAC,EACpD;YAAEF,GAAG,EAAE,QAAQ;YAAEC,KAAK,EAAE,WAAW;YAAEC,IAAI,EAAE;UAAK,CAAC,EACjD;YAAEF,GAAG,EAAE,eAAe;YAAEC,KAAK,EAAE,cAAc;YAAEC,IAAI,EAAE;UAAK,CAAC,CAC5D,CAACC,GAAG,CAACC,GAAG,iBACPxI,OAAA;YAAkBsG,SAAS,EAAC,UAAU;YAAAC,QAAA,eACpCvG,OAAA;cACEsG,SAAS,EAAE,YAAYnG,SAAS,KAAKqI,GAAG,CAACJ,GAAG,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC/DL,OAAO,EAAEA,CAAA,KAAM3H,YAAY,CAACoI,GAAG,CAACJ,GAAG,CAAE;cACrChB,KAAK,EAAE;gBACLe,MAAM,EAAE,MAAM;gBACdM,UAAU,EAAEtI,SAAS,KAAKqI,GAAG,CAACJ,GAAG,GAAG,SAAS,GAAG,aAAa;gBAC7DM,KAAK,EAAEvI,SAAS,KAAKqI,GAAG,CAACJ,GAAG,GAAG,OAAO,GAAG,MAAM;gBAC/CO,YAAY,EAAE,aAAa;gBAC3BC,MAAM,EAAE;cACV,CAAE;cAAArC,QAAA,EAEDiC,GAAG,CAACH;YAAK;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC,GAbF6B,GAAG,CAACJ,GAAG;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcZ,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACN3G,OAAA;QAAKsG,SAAS,EAAC,WAAW;QAAAC,QAAA,GACvBpG,SAAS,KAAK,SAAS,IAAIkG,qBAAqB,CAAC,CAAC,EAClDlG,SAAS,KAAK,KAAK,IAAI2G,iBAAiB,CAAC,CAAC,EAC1C3G,SAAS,KAAK,UAAU,IAAImH,sBAAsB,CAAC,CAAC,EACpDnH,SAAS,KAAK,SAAS,IAAIqH,qBAAqB,CAAC,CAAC,EAClDrH,SAAS,KAAK,QAAQ,IAAIuH,oBAAoB,CAAC,CAAC,EAChDvH,SAAS,KAAK,eAAe,IAAIwH,0BAA0B,CAAC,CAAC;MAAA;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzG,EAAA,CA1oBID,QAAQ;AAAA4I,EAAA,GAAR5I,QAAQ;AA4oBd,eAAeA,QAAQ;AAAC,IAAA4I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}