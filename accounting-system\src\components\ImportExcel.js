import React, { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import * as XLSX from 'xlsx';
import axios from 'axios';
import { downloadTemplate } from './TemplateGenerator';

const ImportExcel = ({ type, onClose, onImportComplete }) => {
  const [file, setFile] = useState(null);
  const [data, setData] = useState([]);
  const [headers, setHeaders] = useState([]);
  const [mapping, setMapping] = useState({});
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1); // 1: Upload, 2: Map, 3: Preview, 4: Import
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  // تعريف الحقول المطلوبة لكل نوع
  const fieldMappings = {
    customers: {
      name: 'اسم العميل',
      phone: 'رقم الهاتف',
      email: 'ال<PERSON>ريد الإلكتروني',
      address: 'العنوان',
      tax_number: 'الرقم الضريبي'
    },
    suppliers: {
      name: 'اسم المورد',
      phone: 'رقم الهاتف',
      email: 'البريد الإلكتروني',
      address: 'العنوان',
      tax_number: 'الرقم الضريبي'
    },
    products: {
      name: 'اسم المنتج',
      description: 'الوصف',
      unit: 'الوحدة',
      price: 'سعر البيع',
      cost: 'سعر التكلفة',
      stock_quantity: 'الكمية المتاحة'
    },
    expenses: {
      category: 'فئة المصروف',
      description: 'الوصف',
      amount: 'المبلغ',
      expense_date: 'تاريخ المصروف',
      payment_method: 'طريقة الدفع',
      receipt_number: 'رقم الإيصال'
    }
  };

  const onDrop = (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (file) {
      setFile(file);
      readExcelFile(file);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'text/csv': ['.csv']
    },
    multiple: false
  });

  const readExcelFile = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        if (jsonData.length > 0) {
          const headers = jsonData[0];
          const rows = jsonData.slice(1).filter(row => row.some(cell => cell !== undefined && cell !== ''));
          
          setHeaders(headers);
          setData(rows);
          setStep(2);
        } else {
          setError('الملف فارغ أو لا يحتوي على بيانات صالحة');
        }
      } catch (error) {
        setError('خطأ في قراءة الملف: ' + error.message);
      }
    };
    reader.readAsArrayBuffer(file);
  };

  const handleMappingChange = (field, headerIndex) => {
    setMapping(prev => ({
      ...prev,
      [field]: headerIndex
    }));
  };

  const validateMapping = () => {
    const requiredFields = Object.keys(fieldMappings[type]);
    const mappedFields = Object.keys(mapping);
    
    // التحقق من وجود الحقول المطلوبة
    const missingFields = requiredFields.filter(field => 
      !mappedFields.includes(field) || mapping[field] === ''
    );
    
    if (missingFields.length > 0) {
      setError(`الحقول التالية مطلوبة: ${missingFields.map(f => fieldMappings[type][f]).join(', ')}`);
      return false;
    }
    
    return true;
  };

  const previewData = () => {
    if (!validateMapping()) return;
    
    setStep(3);
  };

  const getPreviewRows = () => {
    return data.slice(0, 5).map(row => {
      const mappedRow = {};
      Object.keys(mapping).forEach(field => {
        const headerIndex = mapping[field];
        mappedRow[field] = row[headerIndex] || '';
      });
      return mappedRow;
    });
  };

  const importData = async () => {
    try {
      setLoading(true);
      setError('');
      
      const importData = data.map(row => {
        const mappedRow = {};
        Object.keys(mapping).forEach(field => {
          const headerIndex = mapping[field];
          let value = row[headerIndex] || '';
          
          // تحويل البيانات حسب النوع
          if (['price', 'cost', 'amount'].includes(field)) {
            value = parseFloat(value) || 0;
          } else if (['stock_quantity'].includes(field)) {
            value = parseInt(value) || 0;
          }
          
          mappedRow[field] = value;
        });
        return mappedRow;
      });

      // إرسال البيانات للخادم
      const response = await axios.post(`http://localhost:5000/api/import/${type}`, {
        data: importData
      });

      setMessage(`تم استيراد ${response.data.imported} عنصر بنجاح`);
      setStep(4);
      
      if (onImportComplete) {
        onImportComplete();
      }
    } catch (error) {
      setError('خطأ في استيراد البيانات: ' + (error.response?.data?.error || error.message));
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadTemplate = () => {
    downloadTemplate(type);
  };

  const getTypeTitle = () => {
    const titles = {
      customers: 'العملاء',
      suppliers: 'الموردين',
      products: 'المنتجات',
      expenses: 'المصاريف'
    };
    return titles[type] || type;
  };

  return (
    <div className="modal-overlay" style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.5)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000
    }}>
      <div className="modal-content" style={{
        backgroundColor: 'white',
        borderRadius: '10px',
        padding: '2rem',
        maxWidth: '90vw',
        maxHeight: '90vh',
        overflow: 'auto',
        width: '800px'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
          <h2>📥 استيراد {getTypeTitle()} من Excel</h2>
          <button onClick={onClose} className="btn btn-danger">✕</button>
        </div>

        {message && <div className="success">{message}</div>}
        {error && <div className="error">{error}</div>}

        {/* مؤشر التقدم */}
        <div style={{ marginBottom: '2rem' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '1rem' }}>
            {['رفع الملف', 'ربط الحقول', 'معاينة البيانات', 'الاستيراد'].map((stepName, index) => (
              <div key={index} style={{
                padding: '0.5rem 1rem',
                borderRadius: '20px',
                backgroundColor: step > index ? '#4CAF50' : step === index + 1 ? '#2196F3' : '#f0f0f0',
                color: step >= index + 1 ? 'white' : '#666',
                fontSize: '0.9rem'
              }}>
                {index + 1}. {stepName}
              </div>
            ))}
          </div>
        </div>

        {/* الخطوة 1: رفع الملف */}
        {step === 1 && (
          <div>
            <div style={{ marginBottom: '2rem' }}>
              <button onClick={handleDownloadTemplate} className="btn btn-info">
                📄 تحميل قالب Excel جاهز
              </button>
              <p style={{ marginTop: '1rem', color: '#666' }}>
                يمكنك تحميل قالب Excel جاهز يحتوي على الحقول المطلوبة مع أمثلة وتعليمات
              </p>
            </div>

            <div
              {...getRootProps()}
              style={{
                border: '2px dashed #ccc',
                borderRadius: '10px',
                padding: '3rem',
                textAlign: 'center',
                cursor: 'pointer',
                backgroundColor: isDragActive ? '#f0f8ff' : '#fafafa'
              }}
            >
              <input {...getInputProps()} />
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📁</div>
              {isDragActive ? (
                <p>اسحب الملف هنا...</p>
              ) : (
                <div>
                  <p>اسحب ملف Excel هنا أو اضغط للاختيار</p>
                  <p style={{ color: '#666', fontSize: '0.9rem' }}>
                    الصيغ المدعومة: .xlsx, .xls, .csv
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* الخطوة 2: ربط الحقول */}
        {step === 2 && (
          <div>
            <h3>ربط حقول البيانات</h3>
            <p style={{ color: '#666', marginBottom: '2rem' }}>
              اربط كل حقل في النظام بالعمود المناسب في ملف Excel
            </p>

            <div className="row">
              {Object.entries(fieldMappings[type]).map(([field, label]) => (
                <div key={field} className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">{label} *</label>
                    <select
                      className="form-control"
                      value={mapping[field] || ''}
                      onChange={(e) => handleMappingChange(field, e.target.value)}
                    >
                      <option value="">اختر العمود</option>
                      {headers.map((header, index) => (
                        <option key={index} value={index}>{header}</option>
                      ))}
                    </select>
                  </div>
                </div>
              ))}
            </div>

            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', marginTop: '2rem' }}>
              <button onClick={() => setStep(1)} className="btn btn-secondary">
                السابق
              </button>
              <button onClick={previewData} className="btn btn-primary">
                معاينة البيانات
              </button>
            </div>
          </div>
        )}

        {/* الخطوة 3: معاينة البيانات */}
        {step === 3 && (
          <div>
            <h3>معاينة البيانات</h3>
            <p style={{ color: '#666', marginBottom: '2rem' }}>
              معاينة أول 5 صفوف من البيانات المستوردة
            </p>

            <div style={{ overflowX: 'auto' }}>
              <table className="table">
                <thead>
                  <tr>
                    {Object.values(fieldMappings[type]).map((label, index) => (
                      <th key={index}>{label}</th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {getPreviewRows().map((row, index) => (
                    <tr key={index}>
                      {Object.keys(fieldMappings[type]).map((field, fieldIndex) => (
                        <td key={fieldIndex}>{row[field]}</td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div style={{ marginTop: '2rem', padding: '1rem', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
              <strong>إجمالي الصفوف: {data.length}</strong>
            </div>

            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', marginTop: '2rem' }}>
              <button onClick={() => setStep(2)} className="btn btn-secondary">
                السابق
              </button>
              <button onClick={importData} className="btn btn-success" disabled={loading}>
                {loading ? 'جاري الاستيراد...' : 'تأكيد الاستيراد'}
              </button>
            </div>
          </div>
        )}

        {/* الخطوة 4: اكتمال الاستيراد */}
        {step === 4 && (
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '4rem', color: '#4CAF50', marginBottom: '2rem' }}>✅</div>
            <h3>تم الاستيراد بنجاح!</h3>
            <p style={{ color: '#666', marginBottom: '2rem' }}>{message}</p>
            
            <button onClick={onClose} className="btn btn-primary">
              إغلاق
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImportExcel;
