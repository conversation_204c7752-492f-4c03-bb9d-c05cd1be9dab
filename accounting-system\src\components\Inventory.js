import React, { useState, useEffect } from 'react';
import axios from 'axios';

const Inventory = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAdjustForm, setShowAdjustForm] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [adjustmentData, setAdjustmentData] = useState({
    adjustment_type: 'add', // add, subtract, set
    quantity: 0,
    reason: '',
    notes: ''
  });
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:5000/api/products');
      setProducts(response.data);
    } catch (error) {
      setError('خطأ في جلب بيانات المخزون');
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAdjustStock = (product) => {
    setSelectedProduct(product);
    setAdjustmentData({
      adjustment_type: 'add',
      quantity: 0,
      reason: '',
      notes: ''
    });
    setShowAdjustForm(true);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setAdjustmentData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmitAdjustment = async (e) => {
    e.preventDefault();
    try {
      let newQuantity = selectedProduct.stock_quantity;
      
      switch (adjustmentData.adjustment_type) {
        case 'add':
          newQuantity += parseInt(adjustmentData.quantity);
          break;
        case 'subtract':
          newQuantity -= parseInt(adjustmentData.quantity);
          break;
        case 'set':
          newQuantity = parseInt(adjustmentData.quantity);
          break;
        default:
          break;
      }

      // تحديث كمية المنتج
      await axios.put(`http://localhost:5000/api/products/${selectedProduct.id}`, {
        ...selectedProduct,
        stock_quantity: Math.max(0, newQuantity)
      });

      // تسجيل حركة المخزون
      await axios.post('http://localhost:5000/api/inventory-movements', {
        product_id: selectedProduct.id,
        movement_type: adjustmentData.adjustment_type,
        quantity: adjustmentData.quantity,
        old_quantity: selectedProduct.stock_quantity,
        new_quantity: Math.max(0, newQuantity),
        reason: adjustmentData.reason,
        notes: adjustmentData.notes
      });

      setMessage('تم تعديل المخزون بنجاح');
      setShowAdjustForm(false);
      fetchProducts();
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      setError('خطأ في تعديل المخزون');
      console.error('Error adjusting stock:', error);
    }
  };

  const getStockStatus = (quantity) => {
    if (quantity === 0) return { status: 'نفد المخزون', color: 'red' };
    if (quantity <= 10) return { status: 'مخزون منخفض', color: 'orange' };
    if (quantity <= 50) return { status: 'مخزون متوسط', color: 'blue' };
    return { status: 'مخزون جيد', color: 'green' };
  };

  const getTotalInventoryValue = () => {
    return products.reduce((total, product) => {
      return total + (product.stock_quantity * (product.cost || 0));
    }, 0);
  };

  const getLowStockProducts = () => {
    return products.filter(product => product.stock_quantity <= 10);
  };

  const getOutOfStockProducts = () => {
    return products.filter(product => product.stock_quantity === 0);
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">جاري تحميل بيانات المخزون...</div>
      </div>
    );
  }

  return (
    <div className="container">
      <h1 className="page-title">إدارة المخزون</h1>
      
      {message && <div className="success">{message}</div>}
      {error && <div className="error">{error}</div>}
      
      {/* إحصائيات المخزون */}
      <div className="dashboard-stats">
        <div className="stat-card">
          <div className="stat-number">{products.length}</div>
          <div className="stat-label">إجمالي المنتجات</div>
        </div>
        
        <div className="stat-card" style={{ background: 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)' }}>
          <div className="stat-number">{getTotalInventoryValue().toLocaleString()}</div>
          <div className="stat-label">قيمة المخزون (ج.م)</div>
        </div>
        
        <div className="stat-card" style={{ background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)' }}>
          <div className="stat-number">{getLowStockProducts().length}</div>
          <div className="stat-label">منتجات مخزون منخفض</div>
        </div>
        
        <div className="stat-card" style={{ background: 'linear-gradient(135deg, #f44336 0%, #da190b 100%)' }}>
          <div className="stat-number">{getOutOfStockProducts().length}</div>
          <div className="stat-label">منتجات نفد مخزونها</div>
        </div>
      </div>

      {/* تنبيهات المخزون */}
      {(getLowStockProducts().length > 0 || getOutOfStockProducts().length > 0) && (
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">⚠️ تنبيهات المخزون</h3>
          </div>
          <div className="card-body">
            {getOutOfStockProducts().length > 0 && (
              <div className="alert alert-danger">
                <strong>منتجات نفد مخزونها:</strong> {getOutOfStockProducts().map(p => p.name).join(', ')}
              </div>
            )}
            {getLowStockProducts().length > 0 && (
              <div className="alert alert-warning">
                <strong>منتجات مخزون منخفض:</strong> {getLowStockProducts().map(p => `${p.name} (${p.stock_quantity})`).join(', ')}
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* جدول المخزون */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">حالة المخزون</h3>
        </div>
        <div className="card-body">
          {products.length === 0 ? (
            <p>لا توجد منتجات في المخزون</p>
          ) : (
            <table className="table">
              <thead>
                <tr>
                  <th>اسم المنتج</th>
                  <th>الوحدة</th>
                  <th>الكمية المتاحة</th>
                  <th>حالة المخزون</th>
                  <th>سعر التكلفة</th>
                  <th>قيمة المخزون</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {products.map(product => {
                  const stockStatus = getStockStatus(product.stock_quantity);
                  return (
                    <tr key={product.id}>
                      <td>{product.name}</td>
                      <td>{product.unit || '-'}</td>
                      <td>
                        <span style={{ 
                          fontWeight: 'bold',
                          color: stockStatus.color
                        }}>
                          {product.stock_quantity}
                        </span>
                      </td>
                      <td>
                        <span style={{ color: stockStatus.color }}>
                          {stockStatus.status}
                        </span>
                      </td>
                      <td>{(product.cost || 0).toLocaleString()} ج.م</td>
                      <td>{((product.stock_quantity || 0) * (product.cost || 0)).toLocaleString()} ج.م</td>
                      <td>
                        <button 
                          className="btn btn-primary"
                          onClick={() => handleAdjustStock(product)}
                        >
                          تعديل المخزون
                        </button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          )}
        </div>
      </div>

      {/* نموذج تعديل المخزون */}
      {showAdjustForm && selectedProduct && (
        <div className="modal-overlay" style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.5)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 1000
        }}>
          <div className="modal-content" style={{
            backgroundColor: 'white',
            borderRadius: '10px',
            padding: '2rem',
            maxWidth: '500px',
            width: '90%'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
              <h3>تعديل مخزون: {selectedProduct.name}</h3>
              <button onClick={() => setShowAdjustForm(false)} className="btn btn-danger">✕</button>
            </div>

            <div style={{ marginBottom: '1rem', padding: '1rem', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
              <strong>الكمية الحالية: {selectedProduct.stock_quantity}</strong>
            </div>

            <form onSubmit={handleSubmitAdjustment}>
              <div className="form-group">
                <label className="form-label">نوع التعديل</label>
                <select
                  name="adjustment_type"
                  value={adjustmentData.adjustment_type}
                  onChange={handleInputChange}
                  className="form-control"
                  required
                >
                  <option value="add">إضافة كمية</option>
                  <option value="subtract">خصم كمية</option>
                  <option value="set">تحديد كمية جديدة</option>
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">الكمية</label>
                <input
                  type="number"
                  name="quantity"
                  value={adjustmentData.quantity}
                  onChange={handleInputChange}
                  className="form-control"
                  min="0"
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">السبب</label>
                <select
                  name="reason"
                  value={adjustmentData.reason}
                  onChange={handleInputChange}
                  className="form-control"
                  required
                >
                  <option value="">اختر السبب</option>
                  <option value="purchase">شراء جديد</option>
                  <option value="sale">بيع</option>
                  <option value="damage">تلف</option>
                  <option value="theft">فقدان</option>
                  <option value="return">مرتجع</option>
                  <option value="adjustment">تسوية جرد</option>
                  <option value="other">أخرى</option>
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">ملاحظات</label>
                <textarea
                  name="notes"
                  value={adjustmentData.notes}
                  onChange={handleInputChange}
                  className="form-control"
                  rows="3"
                ></textarea>
              </div>

              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
                <button type="submit" className="btn btn-success">
                  تأكيد التعديل
                </button>
                <button type="button" onClick={() => setShowAdjustForm(false)} className="btn btn-danger">
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Inventory;
