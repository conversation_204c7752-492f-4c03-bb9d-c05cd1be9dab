import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navbar from './components/Navbar';
import Dashboard from './components/Dashboard';
import Customers from './components/Customers';
import Suppliers from './components/Suppliers';
import Products from './components/Products';
import SalesInvoices from './components/SalesInvoices';
import PurchaseInvoices from './components/PurchaseInvoices';
import Treasury from './components/Treasury';
import Expenses from './components/Expenses';
import Reports from './components/Reports';
import Inventory from './components/Inventory';
import Backup from './components/Backup';
import Settings from './components/Settings';
import DownloadCenter from './components/DownloadCenter';
import './App.css';

function App() {
  return (
    <Router>
      <div className="app" dir="rtl">
        <Navbar />
        <div className="main-content">
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/customers" element={<Customers />} />
            <Route path="/suppliers" element={<Suppliers />} />
            <Route path="/products" element={<Products />} />
            <Route path="/sales-invoices" element={<SalesInvoices />} />
            <Route path="/purchase-invoices" element={<PurchaseInvoices />} />
            <Route path="/treasury" element={<Treasury />} />
            <Route path="/expenses" element={<Expenses />} />
            <Route path="/inventory" element={<Inventory />} />
            <Route path="/backup" element={<Backup />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/download-center" element={<DownloadCenter />} />
            <Route path="/reports" element={<Reports />} />
          </Routes>
        </div>
      </div>
    </Router>
  );
}

export default App;
