import React, { useState, useEffect } from 'react';
import axios from 'axios';

const Expenses = () => {
  const [expenses, setExpenses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingExpense, setEditingExpense] = useState(null);
  const [formData, setFormData] = useState({
    category: '',
    description: '',
    amount: '',
    expense_date: '',
    payment_method: '',
    receipt_number: '',
    notes: ''
  });
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const expenseCategories = [
    'مصاريف إدارية',
    'مصاريف تشغيلية',
    'مصاريف تسويق',
    'مصاريف صيانة',
    'مصاريف مواصلات',
    'مصاريف اتصالات',
    'مصاريف كهرباء',
    'مصاريف إيجار',
    'مصاريف أخرى'
  ];

  useEffect(() => {
    fetchExpenses();
  }, []);

  const fetchExpenses = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:5000/api/expenses');
      setExpenses(response.data);
    } catch (error) {
      setError('خطأ في جلب بيانات المصاريف');
      console.error('Error fetching expenses:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const expenseData = {
        ...formData,
        amount: parseFloat(formData.amount) || 0
      };

      if (editingExpense) {
        await axios.put(`http://localhost:5000/api/expenses/${editingExpense.id}`, expenseData);
        setMessage('تم تحديث المصروف بنجاح');
      } else {
        await axios.post('http://localhost:5000/api/expenses', expenseData);
        setMessage('تم إضافة المصروف بنجاح');
      }
      
      setFormData({
        category: '',
        description: '',
        amount: '',
        expense_date: '',
        payment_method: '',
        receipt_number: '',
        notes: ''
      });
      setShowForm(false);
      setEditingExpense(null);
      fetchExpenses();
      
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      setError('خطأ في حفظ بيانات المصروف');
      console.error('Error saving expense:', error);
    }
  };

  const handleEdit = (expense) => {
    setEditingExpense(expense);
    setFormData({
      category: expense.category,
      description: expense.description,
      amount: expense.amount?.toString() || '',
      expense_date: expense.expense_date,
      payment_method: expense.payment_method || '',
      receipt_number: expense.receipt_number || '',
      notes: expense.notes || ''
    });
    setShowForm(true);
  };

  const resetForm = () => {
    setFormData({
      category: '',
      description: '',
      amount: '',
      expense_date: '',
      payment_method: '',
      receipt_number: '',
      notes: ''
    });
    setShowForm(false);
    setEditingExpense(null);
  };

  const getTotalExpenses = () => {
    return expenses.reduce((total, expense) => total + (expense.amount || 0), 0);
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">جاري تحميل بيانات المصاريف...</div>
      </div>
    );
  }

  return (
    <div className="container">
      <h1 className="page-title">إدارة المصاريف</h1>
      
      {message && <div className="success">{message}</div>}
      {error && <div className="error">{error}</div>}
      
      <div className="row">
        <div className="col-md-4">
          <div className="stat-card" style={{ background: 'linear-gradient(135deg, #f44336 0%, #da190b 100%)' }}>
            <div className="stat-number">{getTotalExpenses().toLocaleString()}</div>
            <div className="stat-label">إجمالي المصاريف (ج.م)</div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="stat-card">
            <div className="stat-number">{expenses.length}</div>
            <div className="stat-label">عدد المصاريف</div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="stat-card" style={{ background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)' }}>
            <div className="stat-number">
              {expenses.length > 0 ? (getTotalExpenses() / expenses.length).toFixed(0) : 0}
            </div>
            <div className="stat-label">متوسط المصروف (ج.م)</div>
          </div>
        </div>
      </div>
      
      <div className="card">
        <div className="card-header">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <h3 className="card-title">قائمة المصاريف</h3>
            <button 
              className="btn btn-primary"
              onClick={() => setShowForm(!showForm)}
            >
              {showForm ? 'إلغاء' : 'إضافة مصروف جديد'}
            </button>
          </div>
        </div>
        
        {showForm && (
          <div className="card-body">
            <form onSubmit={handleSubmit}>
              <div className="row">
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">فئة المصروف *</label>
                    <select
                      name="category"
                      value={formData.category}
                      onChange={handleInputChange}
                      className="form-control"
                      required
                    >
                      <option value="">اختر فئة المصروف</option>
                      {expenseCategories.map(category => (
                        <option key={category} value={category}>{category}</option>
                      ))}
                    </select>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">المبلغ *</label>
                    <input
                      type="number"
                      step="0.01"
                      name="amount"
                      value={formData.amount}
                      onChange={handleInputChange}
                      className="form-control"
                      required
                    />
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">تاريخ المصروف *</label>
                    <input
                      type="date"
                      name="expense_date"
                      value={formData.expense_date}
                      onChange={handleInputChange}
                      className="form-control"
                      required
                    />
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">طريقة الدفع</label>
                    <select
                      name="payment_method"
                      value={formData.payment_method}
                      onChange={handleInputChange}
                      className="form-control"
                    >
                      <option value="">اختر طريقة الدفع</option>
                      <option value="نقدي">نقدي</option>
                      <option value="شيك">شيك</option>
                      <option value="تحويل بنكي">تحويل بنكي</option>
                      <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                    </select>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">رقم الإيصال</label>
                    <input
                      type="text"
                      name="receipt_number"
                      value={formData.receipt_number}
                      onChange={handleInputChange}
                      className="form-control"
                    />
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">الوصف *</label>
                    <input
                      type="text"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      className="form-control"
                      required
                    />
                  </div>
                </div>
                <div className="col">
                  <div className="form-group">
                    <label className="form-label">ملاحظات</label>
                    <textarea
                      name="notes"
                      value={formData.notes}
                      onChange={handleInputChange}
                      className="form-control"
                      rows="3"
                    ></textarea>
                  </div>
                </div>
              </div>
              <div style={{ display: 'flex', gap: '1rem' }}>
                <button type="submit" className="btn btn-success">
                  {editingExpense ? 'تحديث المصروف' : 'إضافة المصروف'}
                </button>
                <button type="button" className="btn btn-danger" onClick={resetForm}>
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        )}
        
        <div className="card-body">
          {expenses.length === 0 ? (
            <p>لا توجد مصاريف مسجلة حتى الآن</p>
          ) : (
            <table className="table">
              <thead>
                <tr>
                  <th>التاريخ</th>
                  <th>الفئة</th>
                  <th>الوصف</th>
                  <th>المبلغ</th>
                  <th>طريقة الدفع</th>
                  <th>رقم الإيصال</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {expenses.map(expense => (
                  <tr key={expense.id}>
                    <td>{new Date(expense.expense_date).toLocaleDateString('ar-EG')}</td>
                    <td>{expense.category}</td>
                    <td>{expense.description}</td>
                    <td style={{ color: 'red' }}>{expense.amount?.toLocaleString()} ج.م</td>
                    <td>{expense.payment_method || '-'}</td>
                    <td>{expense.receipt_number || '-'}</td>
                    <td>
                      <button 
                        className="btn btn-warning"
                        onClick={() => handleEdit(expense)}
                        style={{ marginLeft: '0.5rem' }}
                      >
                        تعديل
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
};

export default Expenses;
