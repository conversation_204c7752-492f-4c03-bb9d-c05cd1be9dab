{"version": "0.2.0", "configurations": [{"name": "تشغيل الخادم", "type": "node", "request": "launch", "program": "${workspaceFolder}/server/server.js", "cwd": "${workspaceFolder}/server", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "restart": true, "runtimeExecutable": "node", "skipFiles": ["<node_internals>/**"]}, {"name": "تشغيل الواجهة الأمامية", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/react-scripts", "args": ["start"], "cwd": "${workspaceFolder}", "env": {"NODE_ENV": "development", "BROWSER": "none"}, "console": "integratedTerminal", "restart": true, "skipFiles": ["<node_internals>/**"]}, {"name": "تشغيل النظام كاملاً", "type": "node", "request": "launch", "program": "${workspaceFolder}/start.js", "cwd": "${workspaceFolder}", "console": "integratedTerminal"}], "compounds": [{"name": "تشغيل النظام المحاسبي", "configurations": ["تشغيل الخادم", "تشغيل الواجهة الأمامية"], "stopAll": true}]}