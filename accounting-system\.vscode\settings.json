{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "emmet.includeLanguages": {"javascript": "javascriptreact"}, "files.associations": {"*.js": "javascriptreact"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 2, "editor.insertSpaces": true, "files.encoding": "utf8", "files.eol": "\n", "search.exclude": {"**/node_modules": true, "**/build": true, "**/dist": true, "**/.git": true, "**/coverage": true}, "files.exclude": {"**/node_modules": true, "**/build": true, "**/dist": true, "**/.git": true, "**/coverage": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "javascript.preferences.quoteStyle": "single", "typescript.preferences.quoteStyle": "single", "editor.wordWrap": "on", "editor.minimap.enabled": true, "editor.lineNumbers": "on", "editor.rulers": [80, 120], "workbench.colorTheme": "Default Dark+", "terminal.integrated.defaultProfile.windows": "PowerShell", "git.autofetch": true, "git.confirmSync": false, "extensions.ignoreRecommendations": false}