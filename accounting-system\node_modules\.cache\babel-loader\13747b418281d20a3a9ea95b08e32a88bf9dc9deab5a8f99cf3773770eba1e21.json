{"ast": null, "code": "var _jsxFileName = \"F:\\\\\\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\\\\accounting-system\\\\src\\\\components\\\\DownloadCenter.js\";\nimport React from 'react';\nimport { downloadTemplate, downloadCompleteTemplate } from './TemplateGenerator';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DownloadCenter = () => {\n  const templates = [{\n    id: 'customers',\n    title: 'قالب العملاء',\n    description: 'قالب Excel لاستيراد بيانات العملاء مع أمثلة وتعليمات',\n    icon: '👥',\n    color: '#4CAF50'\n  }, {\n    id: 'suppliers',\n    title: 'قالب الموردين',\n    description: 'قالب Excel لاستيراد بيانات الموردين مع أمثلة وتعليمات',\n    icon: '🏭',\n    color: '#2196F3'\n  }, {\n    id: 'products',\n    title: 'قالب المنتجات',\n    description: 'قالب Excel لاستيراد بيانات المنتجات مع أمثلة وتعليمات',\n    icon: '📦',\n    color: '#FF9800'\n  }, {\n    id: 'expenses',\n    title: 'قالب المصاريف',\n    description: 'قالب Excel لاستيراد بيانات المصاريف مع أمثلة وتعليمات',\n    icon: '💸',\n    color: '#F44336'\n  }];\n  const handleDownload = templateId => {\n    downloadTemplate(templateId);\n  };\n  const handleDownloadComplete = () => {\n    downloadCompleteTemplate();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"page-title\",\n      children: \"\\uD83D\\uDCE5 \\u0645\\u0631\\u0643\\u0632 \\u0627\\u0644\\u062A\\u062D\\u0645\\u064A\\u0644\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\u0642\\u0648\\u0627\\u0644\\u0628 \\u0627\\u0644\\u0627\\u0633\\u062A\\u064A\\u0631\\u0627\\u062F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#666',\n            marginBottom: '2rem'\n          },\n          children: \"\\u062D\\u0645\\u0644 \\u0627\\u0644\\u0642\\u0648\\u0627\\u0644\\u0628 \\u0627\\u0644\\u062C\\u0627\\u0647\\u0632\\u0629 \\u0644\\u0627\\u0633\\u062A\\u064A\\u0631\\u0627\\u062F \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0625\\u0644\\u0649 \\u0627\\u0644\\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u064A. \\u0643\\u0644 \\u0642\\u0627\\u0644\\u0628 \\u064A\\u062D\\u062A\\u0648\\u064A \\u0639\\u0644\\u0649 \\u0623\\u0645\\u062B\\u0644\\u0629 \\u0648\\u062A\\u0639\\u0644\\u064A\\u0645\\u0627\\u062A \\u0645\\u0641\\u0635\\u0644\\u0629.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          style: {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            color: 'white',\n            marginBottom: '2rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    color: 'white',\n                    marginBottom: '0.5rem'\n                  },\n                  children: \"\\uD83C\\uDFAF \\u0627\\u0644\\u0642\\u0627\\u0644\\u0628 \\u0627\\u0644\\u0634\\u0627\\u0645\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    color: 'rgba(255,255,255,0.9)',\n                    marginBottom: '0'\n                  },\n                  children: \"\\u0642\\u0627\\u0644\\u0628 \\u0648\\u0627\\u062D\\u062F \\u064A\\u062D\\u062A\\u0648\\u064A \\u0639\\u0644\\u0649 \\u062C\\u0645\\u064A\\u0639 \\u0627\\u0644\\u0623\\u0642\\u0633\\u0627\\u0645 (\\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\\u060C \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\\u060C \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\\u060C \\u0627\\u0644\\u0645\\u0635\\u0627\\u0631\\u064A\\u0641)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 69,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleDownloadComplete,\n                className: \"btn\",\n                style: {\n                  backgroundColor: 'white',\n                  color: '#667eea',\n                  fontWeight: 'bold'\n                },\n                children: \"\\uD83D\\uDCE5 \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0642\\u0627\\u0644\\u0628 \\u0627\\u0644\\u0634\\u0627\\u0645\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: templates.map(template => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card card-hover\",\n              style: {\n                marginBottom: '1.5rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '2rem',\n                      marginLeft: '1rem',\n                      padding: '0.5rem',\n                      borderRadius: '50%',\n                      backgroundColor: template.color + '20',\n                      color: template.color\n                    },\n                    children: template.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 95,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                      style: {\n                        marginBottom: '0.25rem',\n                        color: template.color\n                      },\n                      children: template.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 106,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      style: {\n                        color: '#666',\n                        marginBottom: '0',\n                        fontSize: '0.9rem'\n                      },\n                      children: template.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 109,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 94,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleDownload(template.id),\n                  className: \"btn btn-primary\",\n                  style: {\n                    width: '100%'\n                  },\n                  children: \"\\uD83D\\uDCE5 \\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0642\\u0627\\u0644\\u0628\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 17\n            }, this)\n          }, template.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\uD83D\\uDCCB \\u062A\\u0639\\u0644\\u064A\\u0645\\u0627\\u062A \\u0627\\u0644\\u0627\\u0633\\u062A\\u062E\\u062F\\u0627\\u0645\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"\\uD83D\\uDD04 \\u062E\\u0637\\u0648\\u0627\\u062A \\u0627\\u0644\\u0627\\u0633\\u062A\\u064A\\u0631\\u0627\\u062F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u062D\\u0645\\u0644 \\u0627\\u0644\\u0642\\u0627\\u0644\\u0628 \\u0627\\u0644\\u0645\\u0646\\u0627\\u0633\\u0628 \\u0645\\u0646 \\u0627\\u0644\\u0623\\u0639\\u0644\\u0649\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0627\\u0641\\u062A\\u062D \\u0627\\u0644\\u0645\\u0644\\u0641 \\u0641\\u064A Excel \\u0623\\u0648 Google Sheets\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0627\\u062D\\u0630\\u0641 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u062A\\u062C\\u0631\\u064A\\u0628\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0623\\u062F\\u062E\\u0644 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\\u0643 \\u0627\\u0644\\u062D\\u0642\\u064A\\u0642\\u064A\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0627\\u062D\\u0641\\u0638 \\u0627\\u0644\\u0645\\u0644\\u0641 \\u0628\\u0635\\u064A\\u063A\\u0629 Excel (.xlsx)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0627\\u0631\\u0641\\u0639 \\u0627\\u0644\\u0645\\u0644\\u0641 \\u0641\\u064A \\u0627\\u0644\\u0642\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u0627\\u0633\\u0628\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"\\u26A0\\uFE0F \\u0646\\u0635\\u0627\\u0626\\u062D \\u0645\\u0647\\u0645\\u0629:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0644\\u0627 \\u062A\\u063A\\u064A\\u0631 \\u0623\\u0633\\u0645\\u0627\\u0621 \\u0627\\u0644\\u0623\\u0639\\u0645\\u062F\\u0629 \\u0641\\u064A \\u0627\\u0644\\u0635\\u0641 \\u0627\\u0644\\u0623\\u0648\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u062A\\u0623\\u0643\\u062F \\u0645\\u0646 \\u0635\\u062D\\u0629 \\u0627\\u0644\\u062A\\u0648\\u0627\\u0631\\u064A\\u062E (DD/MM/YYYY)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u062A\\u0623\\u0643\\u062F \\u0645\\u0646 \\u0635\\u062D\\u0629 \\u0627\\u0644\\u0623\\u0631\\u0642\\u0627\\u0645 (\\u0628\\u062F\\u0648\\u0646 \\u0641\\u0648\\u0627\\u0635\\u0644)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0627\\u0645\\u0644\\u0623 \\u0627\\u0644\\u062D\\u0642\\u0648\\u0644 \\u0627\\u0644\\u0645\\u0637\\u0644\\u0648\\u0628\\u0629 \\u0641\\u0642\\u0637\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0631\\u0627\\u062C\\u0639 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0642\\u0628\\u0644 \\u0627\\u0644\\u0627\\u0633\\u062A\\u064A\\u0631\\u0627\\u062F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\u0627\\u062D\\u062A\\u0641\\u0638 \\u0628\\u0646\\u0633\\u062E\\u0629 \\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\\u0629 \\u0645\\u0646 \\u0645\\u0644\\u0641\\u0643\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\uD83D\\uDCA1 \\u0623\\u0645\\u062B\\u0644\\u0629 \\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                  children: \"\\uD83D\\uDC65 \\u0645\\u062B\\u0627\\u0644 \\u0639\\u0645\\u064A\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0627\\u0633\\u0645:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 21\n                  }, this), \" \\u0623\\u062D\\u0645\\u062F \\u0645\\u062D\\u0645\\u062F \\u0639\\u0644\\u064A\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 58\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0647\\u0627\\u062A\\u0641:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 21\n                  }, this), \" 01234567890\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 57\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 21\n                  }, this), \" <EMAIL>\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 63\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 21\n                  }, this), \" \\u0627\\u0644\\u0642\\u0627\\u0647\\u0631\\u0629\\u060C \\u0645\\u0635\\u0631\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                  children: \"\\uD83C\\uDFED \\u0645\\u062B\\u0627\\u0644 \\u0645\\u0648\\u0631\\u062F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0627\\u0633\\u0645:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 21\n                  }, this), \" \\u0634\\u0631\\u0643\\u0629 \\u0627\\u0644\\u062A\\u0648\\u0631\\u064A\\u062F\\u0627\\u062A\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 59\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0647\\u0627\\u062A\\u0641:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 21\n                  }, this), \" 0223456789\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 56\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 21\n                  }, this), \" <EMAIL>\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 64\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0639\\u0646\\u0648\\u0627\\u0646:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 21\n                  }, this), \" \\u0627\\u0644\\u0642\\u0627\\u0647\\u0631\\u0629 \\u0627\\u0644\\u062C\\u062F\\u064A\\u062F\\u0629\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                  children: \"\\uD83D\\uDCE6 \\u0645\\u062B\\u0627\\u0644 \\u0645\\u0646\\u062A\\u062C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0627\\u0633\\u0645:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 21\n                  }, this), \" \\u0644\\u0627\\u0628\\u062A\\u0648\\u0628 Dell\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 56\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0648\\u062D\\u062F\\u0629:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 21\n                  }, this), \" \\u0642\\u0637\\u0639\\u0629\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 50\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0633\\u0639\\u0631:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 21\n                  }, this), \" 15000\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 50\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 21\n                  }, this), \" 10\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                  children: \"\\uD83D\\uDCB8 \\u0645\\u062B\\u0627\\u0644 \\u0645\\u0635\\u0631\\u0648\\u0641\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0641\\u0626\\u0629:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 21\n                  }, this), \" \\u0645\\u0635\\u0627\\u0631\\u064A\\u0641 \\u0625\\u062F\\u0627\\u0631\\u064A\\u0629\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 58\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0648\\u0635\\u0641:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 21\n                  }, this), \" \\u0641\\u0627\\u062A\\u0648\\u0631\\u0629 \\u0643\\u0647\\u0631\\u0628\\u0627\\u0621\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 58\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u0645\\u0628\\u0644\\u063A:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 21\n                  }, this), \" 500\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u0627\\u0644\\u062A\\u0627\\u0631\\u064A\\u062E:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 21\n                  }, this), \" 15/01/2024\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"card-title\",\n          children: \"\\uD83C\\uDD98 \\u0627\\u0644\\u062F\\u0639\\u0645 \\u0627\\u0644\\u0641\\u0646\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-body\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"\\uD83D\\uDCDE \\u062A\\u0648\\u0627\\u0635\\u0644 \\u0645\\u0639\\u0646\\u0627:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0625\\u0630\\u0627 \\u0648\\u0627\\u062C\\u0647\\u062A \\u0623\\u064A \\u0645\\u0634\\u0643\\u0644\\u0629 \\u0641\\u064A \\u0627\\u0633\\u062A\\u062E\\u062F\\u0627\\u0645 \\u0627\\u0644\\u0642\\u0648\\u0627\\u0644\\u0628 \\u0623\\u0648 \\u0627\\u0644\\u0627\\u0633\\u062A\\u064A\\u0631\\u0627\\u062F\\u060C \\u0644\\u0627 \\u062A\\u062A\\u0631\\u062F\\u062F \\u0641\\u064A \\u0627\\u0644\\u062A\\u0648\\u0627\\u0635\\u0644 \\u0645\\u0639\\u0646\\u0627:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\uD83D\\uDCE7 \\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A: <EMAIL>\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\uD83D\\uDCF1 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641: +20 123 456 7890\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"\\uD83D\\uDCAC \\u0627\\u0644\\u062F\\u0631\\u062F\\u0634\\u0629 \\u0627\\u0644\\u0645\\u0628\\u0627\\u0634\\u0631\\u0629: \\u0645\\u062A\\u0627\\u062D\\u0629 24/7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"\\uD83D\\uDCDA \\u0645\\u0635\\u0627\\u062F\\u0631 \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  style: {\n                    color: '#667eea'\n                  },\n                  children: \"\\u062F\\u0644\\u064A\\u0644 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645 \\u0627\\u0644\\u0643\\u0627\\u0645\\u0644\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  style: {\n                    color: '#667eea'\n                  },\n                  children: \"\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A \\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  style: {\n                    color: '#667eea'\n                  },\n                  children: \"\\u0627\\u0644\\u0623\\u0633\\u0626\\u0644\\u0629 \\u0627\\u0644\\u0634\\u0627\\u0626\\u0639\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#\",\n                  style: {\n                    color: '#667eea'\n                  },\n                  children: \"\\u0645\\u0646\\u062A\\u062F\\u0649 \\u0627\\u0644\\u0645\\u062C\\u062A\\u0645\\u0639\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_c = DownloadCenter;\nexport default DownloadCenter;\nvar _c;\n$RefreshReg$(_c, \"DownloadCenter\");", "map": {"version": 3, "names": ["React", "downloadTemplate", "downloadCompleteTemplate", "jsxDEV", "_jsxDEV", "DownloadCenter", "templates", "id", "title", "description", "icon", "color", "handleDownload", "templateId", "handleDownloadComplete", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginBottom", "background", "display", "justifyContent", "alignItems", "onClick", "backgroundColor", "fontWeight", "map", "template", "fontSize", "marginLeft", "padding", "borderRadius", "width", "href", "_c", "$RefreshReg$"], "sources": ["F:/القيمة المضافة/accounting-system/src/components/DownloadCenter.js"], "sourcesContent": ["import React from 'react';\nimport { downloadTemplate, downloadCompleteTemplate } from './TemplateGenerator';\n\nconst DownloadCenter = () => {\n  const templates = [\n    {\n      id: 'customers',\n      title: 'قالب العملاء',\n      description: 'قالب Excel لاستيراد بيانات العملاء مع أمثلة وتعليمات',\n      icon: '👥',\n      color: '#4CAF50'\n    },\n    {\n      id: 'suppliers',\n      title: 'قالب الموردين',\n      description: 'قالب Excel لاستيراد بيانات الموردين مع أمثلة وتعليمات',\n      icon: '🏭',\n      color: '#2196F3'\n    },\n    {\n      id: 'products',\n      title: 'قالب المنتجات',\n      description: 'قالب Excel لاستيراد بيانات المنتجات مع أمثلة وتعليمات',\n      icon: '📦',\n      color: '#FF9800'\n    },\n    {\n      id: 'expenses',\n      title: 'قالب المصاريف',\n      description: 'قالب Excel لاستيراد بيانات المصاريف مع أمثلة وتعليمات',\n      icon: '💸',\n      color: '#F44336'\n    }\n  ];\n\n  const handleDownload = (templateId) => {\n    downloadTemplate(templateId);\n  };\n\n  const handleDownloadComplete = () => {\n    downloadCompleteTemplate();\n  };\n\n  return (\n    <div className=\"container\">\n      <h1 className=\"page-title\">📥 مركز التحميل</h1>\n      \n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">قوالب الاستيراد</h3>\n        </div>\n        <div className=\"card-body\">\n          <p style={{ color: '#666', marginBottom: '2rem' }}>\n            حمل القوالب الجاهزة لاستيراد البيانات إلى النظام المحاسبي. كل قالب يحتوي على أمثلة وتعليمات مفصلة.\n          </p>\n\n          {/* القالب الشامل */}\n          <div className=\"card\" style={{ \n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            color: 'white',\n            marginBottom: '2rem'\n          }}>\n            <div className=\"card-body\">\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                <div>\n                  <h4 style={{ color: 'white', marginBottom: '0.5rem' }}>\n                    🎯 القالب الشامل\n                  </h4>\n                  <p style={{ color: 'rgba(255,255,255,0.9)', marginBottom: '0' }}>\n                    قالب واحد يحتوي على جميع الأقسام (العملاء، الموردين، المنتجات، المصاريف)\n                  </p>\n                </div>\n                <button \n                  onClick={handleDownloadComplete}\n                  className=\"btn\"\n                  style={{ \n                    backgroundColor: 'white', \n                    color: '#667eea',\n                    fontWeight: 'bold'\n                  }}\n                >\n                  📥 تحميل القالب الشامل\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* القوالب الفردية */}\n          <div className=\"row\">\n            {templates.map(template => (\n              <div key={template.id} className=\"col-md-6\">\n                <div className=\"card card-hover\" style={{ marginBottom: '1.5rem' }}>\n                  <div className=\"card-body\">\n                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>\n                      <div style={{\n                        fontSize: '2rem',\n                        marginLeft: '1rem',\n                        padding: '0.5rem',\n                        borderRadius: '50%',\n                        backgroundColor: template.color + '20',\n                        color: template.color\n                      }}>\n                        {template.icon}\n                      </div>\n                      <div>\n                        <h5 style={{ marginBottom: '0.25rem', color: template.color }}>\n                          {template.title}\n                        </h5>\n                        <p style={{ color: '#666', marginBottom: '0', fontSize: '0.9rem' }}>\n                          {template.description}\n                        </p>\n                      </div>\n                    </div>\n                    \n                    <button \n                      onClick={() => handleDownload(template.id)}\n                      className=\"btn btn-primary\"\n                      style={{ width: '100%' }}\n                    >\n                      📥 تحميل القالب\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* تعليمات الاستخدام */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">📋 تعليمات الاستخدام</h3>\n        </div>\n        <div className=\"card-body\">\n          <div className=\"row\">\n            <div className=\"col-md-6\">\n              <h5>🔄 خطوات الاستيراد:</h5>\n              <ol>\n                <li>حمل القالب المناسب من الأعلى</li>\n                <li>افتح الملف في Excel أو Google Sheets</li>\n                <li>احذف البيانات التجريبية</li>\n                <li>أدخل بياناتك الحقيقية</li>\n                <li>احفظ الملف بصيغة Excel (.xlsx)</li>\n                <li>ارفع الملف في القسم المناسب</li>\n              </ol>\n            </div>\n            <div className=\"col-md-6\">\n              <h5>⚠️ نصائح مهمة:</h5>\n              <ul>\n                <li>لا تغير أسماء الأعمدة في الصف الأول</li>\n                <li>تأكد من صحة التواريخ (DD/MM/YYYY)</li>\n                <li>تأكد من صحة الأرقام (بدون فواصل)</li>\n                <li>املأ الحقول المطلوبة فقط</li>\n                <li>راجع البيانات قبل الاستيراد</li>\n                <li>احتفظ بنسخة احتياطية من ملفك</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* أمثلة البيانات */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">💡 أمثلة البيانات</h3>\n        </div>\n        <div className=\"card-body\">\n          <div className=\"row\">\n            <div className=\"col-md-3\">\n              <div className=\"card\">\n                <div className=\"card-header\">\n                  <h6>👥 مثال عميل</h6>\n                </div>\n                <div className=\"card-body\">\n                  <small>\n                    <strong>الاسم:</strong> أحمد محمد علي<br />\n                    <strong>الهاتف:</strong> 01234567890<br />\n                    <strong>البريد:</strong> <EMAIL><br />\n                    <strong>العنوان:</strong> القاهرة، مصر\n                  </small>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"col-md-3\">\n              <div className=\"card\">\n                <div className=\"card-header\">\n                  <h6>🏭 مثال مورد</h6>\n                </div>\n                <div className=\"card-body\">\n                  <small>\n                    <strong>الاسم:</strong> شركة التوريدات<br />\n                    <strong>الهاتف:</strong> 0223456789<br />\n                    <strong>البريد:</strong> <EMAIL><br />\n                    <strong>العنوان:</strong> القاهرة الجديدة\n                  </small>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"col-md-3\">\n              <div className=\"card\">\n                <div className=\"card-header\">\n                  <h6>📦 مثال منتج</h6>\n                </div>\n                <div className=\"card-body\">\n                  <small>\n                    <strong>الاسم:</strong> لابتوب Dell<br />\n                    <strong>الوحدة:</strong> قطعة<br />\n                    <strong>السعر:</strong> 15000<br />\n                    <strong>الكمية:</strong> 10\n                  </small>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"col-md-3\">\n              <div className=\"card\">\n                <div className=\"card-header\">\n                  <h6>💸 مثال مصروف</h6>\n                </div>\n                <div className=\"card-body\">\n                  <small>\n                    <strong>الفئة:</strong> مصاريف إدارية<br />\n                    <strong>الوصف:</strong> فاتورة كهرباء<br />\n                    <strong>المبلغ:</strong> 500<br />\n                    <strong>التاريخ:</strong> 15/01/2024\n                  </small>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* الدعم الفني */}\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h3 className=\"card-title\">🆘 الدعم الفني</h3>\n        </div>\n        <div className=\"card-body\">\n          <div className=\"row\">\n            <div className=\"col-md-6\">\n              <h5>📞 تواصل معنا:</h5>\n              <p>\n                إذا واجهت أي مشكلة في استخدام القوالب أو الاستيراد، لا تتردد في التواصل معنا:\n              </p>\n              <ul>\n                <li>📧 البريد الإلكتروني: <EMAIL></li>\n                <li>📱 الهاتف: +20 123 456 7890</li>\n                <li>💬 الدردشة المباشرة: متاحة 24/7</li>\n              </ul>\n            </div>\n            <div className=\"col-md-6\">\n              <h5>📚 مصادر إضافية:</h5>\n              <ul>\n                <li><a href=\"#\" style={{ color: '#667eea' }}>دليل المستخدم الكامل</a></li>\n                <li><a href=\"#\" style={{ color: '#667eea' }}>فيديوهات تعليمية</a></li>\n                <li><a href=\"#\" style={{ color: '#667eea' }}>الأسئلة الشائعة</a></li>\n                <li><a href=\"#\" style={{ color: '#667eea' }}>منتدى المجتمع</a></li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DownloadCenter;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,gBAAgB,EAAEC,wBAAwB,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjF,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAC3B,MAAMC,SAAS,GAAG,CAChB;IACEC,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,sDAAsD;IACnEC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,uDAAuD;IACpEC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,uDAAuD;IACpEC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,uDAAuD;IACpEC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,cAAc,GAAIC,UAAU,IAAK;IACrCZ,gBAAgB,CAACY,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnCZ,wBAAwB,CAAC,CAAC;EAC5B,CAAC;EAED,oBACEE,OAAA;IAAKW,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBZ,OAAA;MAAIW,SAAS,EAAC,YAAY;MAAAC,QAAA,EAAC;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE/ChB,OAAA;MAAKW,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBZ,OAAA;QAAKW,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BZ,OAAA;UAAIW,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,eACNhB,OAAA;QAAKW,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBZ,OAAA;UAAGiB,KAAK,EAAE;YAAEV,KAAK,EAAE,MAAM;YAAEW,YAAY,EAAE;UAAO,CAAE;UAAAN,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAGJhB,OAAA;UAAKW,SAAS,EAAC,MAAM;UAACM,KAAK,EAAE;YAC3BE,UAAU,EAAE,mDAAmD;YAC/DZ,KAAK,EAAE,OAAO;YACdW,YAAY,EAAE;UAChB,CAAE;UAAAN,QAAA,eACAZ,OAAA;YAAKW,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBZ,OAAA;cAAKiB,KAAK,EAAE;gBAAEG,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAV,QAAA,gBACrFZ,OAAA;gBAAAY,QAAA,gBACEZ,OAAA;kBAAIiB,KAAK,EAAE;oBAAEV,KAAK,EAAE,OAAO;oBAAEW,YAAY,EAAE;kBAAS,CAAE;kBAAAN,QAAA,EAAC;gBAEvD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLhB,OAAA;kBAAGiB,KAAK,EAAE;oBAAEV,KAAK,EAAE,uBAAuB;oBAAEW,YAAY,EAAE;kBAAI,CAAE;kBAAAN,QAAA,EAAC;gBAEjE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNhB,OAAA;gBACEuB,OAAO,EAAEb,sBAAuB;gBAChCC,SAAS,EAAC,KAAK;gBACfM,KAAK,EAAE;kBACLO,eAAe,EAAE,OAAO;kBACxBjB,KAAK,EAAE,SAAS;kBAChBkB,UAAU,EAAE;gBACd,CAAE;gBAAAb,QAAA,EACH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhB,OAAA;UAAKW,SAAS,EAAC,KAAK;UAAAC,QAAA,EACjBV,SAAS,CAACwB,GAAG,CAACC,QAAQ,iBACrB3B,OAAA;YAAuBW,SAAS,EAAC,UAAU;YAAAC,QAAA,eACzCZ,OAAA;cAAKW,SAAS,EAAC,iBAAiB;cAACM,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAS,CAAE;cAAAN,QAAA,eACjEZ,OAAA;gBAAKW,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBZ,OAAA;kBAAKiB,KAAK,EAAE;oBAAEG,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEJ,YAAY,EAAE;kBAAO,CAAE;kBAAAN,QAAA,gBAC1EZ,OAAA;oBAAKiB,KAAK,EAAE;sBACVW,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,MAAM;sBAClBC,OAAO,EAAE,QAAQ;sBACjBC,YAAY,EAAE,KAAK;sBACnBP,eAAe,EAAEG,QAAQ,CAACpB,KAAK,GAAG,IAAI;sBACtCA,KAAK,EAAEoB,QAAQ,CAACpB;oBAClB,CAAE;oBAAAK,QAAA,EACCe,QAAQ,CAACrB;kBAAI;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACNhB,OAAA;oBAAAY,QAAA,gBACEZ,OAAA;sBAAIiB,KAAK,EAAE;wBAAEC,YAAY,EAAE,SAAS;wBAAEX,KAAK,EAAEoB,QAAQ,CAACpB;sBAAM,CAAE;sBAAAK,QAAA,EAC3De,QAAQ,CAACvB;oBAAK;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC,eACLhB,OAAA;sBAAGiB,KAAK,EAAE;wBAAEV,KAAK,EAAE,MAAM;wBAAEW,YAAY,EAAE,GAAG;wBAAEU,QAAQ,EAAE;sBAAS,CAAE;sBAAAhB,QAAA,EAChEe,QAAQ,CAACtB;oBAAW;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENhB,OAAA;kBACEuB,OAAO,EAAEA,CAAA,KAAMf,cAAc,CAACmB,QAAQ,CAACxB,EAAE,CAAE;kBAC3CQ,SAAS,EAAC,iBAAiB;kBAC3BM,KAAK,EAAE;oBAAEe,KAAK,EAAE;kBAAO,CAAE;kBAAApB,QAAA,EAC1B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAhCEW,QAAQ,CAACxB,EAAE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiChB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhB,OAAA;MAAKW,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBZ,OAAA;QAAKW,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BZ,OAAA;UAAIW,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eACNhB,OAAA;QAAKW,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBZ,OAAA;UAAKW,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBZ,OAAA;YAAKW,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBZ,OAAA;cAAAY,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BhB,OAAA;cAAAY,QAAA,gBACEZ,OAAA;gBAAAY,QAAA,EAAI;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrChB,OAAA;gBAAAY,QAAA,EAAI;cAAoC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC7ChB,OAAA;gBAAAY,QAAA,EAAI;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChChB,OAAA;gBAAAY,QAAA,EAAI;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9BhB,OAAA;gBAAAY,QAAA,EAAI;cAA8B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvChB,OAAA;gBAAAY,QAAA,EAAI;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACNhB,OAAA;YAAKW,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBZ,OAAA;cAAAY,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBhB,OAAA;cAAAY,QAAA,gBACEZ,OAAA;gBAAAY,QAAA,EAAI;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5ChB,OAAA;gBAAAY,QAAA,EAAI;cAAiC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1ChB,OAAA;gBAAAY,QAAA,EAAI;cAAgC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzChB,OAAA;gBAAAY,QAAA,EAAI;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjChB,OAAA;gBAAAY,QAAA,EAAI;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpChB,OAAA;gBAAAY,QAAA,EAAI;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhB,OAAA;MAAKW,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBZ,OAAA;QAAKW,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BZ,OAAA;UAAIW,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eACNhB,OAAA;QAAKW,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBZ,OAAA;UAAKW,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBZ,OAAA;YAAKW,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBZ,OAAA;cAAKW,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBZ,OAAA;gBAAKW,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BZ,OAAA;kBAAAY,QAAA,EAAI;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACNhB,OAAA;gBAAKW,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACxBZ,OAAA;kBAAAY,QAAA,gBACEZ,OAAA;oBAAAY,QAAA,EAAQ;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,yEAAc,eAAAhB,OAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3ChB,OAAA;oBAAAY,QAAA,EAAQ;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,gBAAY,eAAAhB,OAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1ChB,OAAA;oBAAAY,QAAA,EAAQ;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,sBAAkB,eAAAhB,OAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChDhB,OAAA;oBAAAY,QAAA,EAAQ;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,wEAC3B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhB,OAAA;YAAKW,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBZ,OAAA;cAAKW,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBZ,OAAA;gBAAKW,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BZ,OAAA;kBAAAY,QAAA,EAAI;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACNhB,OAAA;gBAAKW,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACxBZ,OAAA;kBAAAY,QAAA,gBACEZ,OAAA;oBAAAY,QAAA,EAAQ;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,oFAAe,eAAAhB,OAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5ChB,OAAA;oBAAAY,QAAA,EAAQ;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAAW,eAAAhB,OAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzChB,OAAA;oBAAAY,QAAA,EAAQ;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,uBAAmB,eAAAhB,OAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjDhB,OAAA;oBAAAY,QAAA,EAAQ;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,0FAC3B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhB,OAAA;YAAKW,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBZ,OAAA;cAAKW,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBZ,OAAA;gBAAKW,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BZ,OAAA;kBAAAY,QAAA,EAAI;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACNhB,OAAA;gBAAKW,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACxBZ,OAAA;kBAAAY,QAAA,gBACEZ,OAAA;oBAAAY,QAAA,EAAQ;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,8CAAY,eAAAhB,OAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzChB,OAAA;oBAAAY,QAAA,EAAQ;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,6BAAK,eAAAhB,OAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnChB,OAAA;oBAAAY,QAAA,EAAQ;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,UAAM,eAAAhB,OAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnChB,OAAA;oBAAAY,QAAA,EAAQ;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,OAC1B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhB,OAAA;YAAKW,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBZ,OAAA;cAAKW,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBZ,OAAA;gBAAKW,SAAS,EAAC,aAAa;gBAAAC,QAAA,eAC1BZ,OAAA;kBAAAY,QAAA,EAAI;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eACNhB,OAAA;gBAAKW,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACxBZ,OAAA;kBAAAY,QAAA,gBACEZ,OAAA;oBAAAY,QAAA,EAAQ;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,8EAAc,eAAAhB,OAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3ChB,OAAA;oBAAAY,QAAA,EAAQ;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,8EAAc,eAAAhB,OAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3ChB,OAAA;oBAAAY,QAAA,EAAQ;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,QAAI,eAAAhB,OAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAClChB,OAAA;oBAAAY,QAAA,EAAQ;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC3B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhB,OAAA;MAAKW,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBZ,OAAA;QAAKW,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BZ,OAAA;UAAIW,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACNhB,OAAA;QAAKW,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBZ,OAAA;UAAKW,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBZ,OAAA;YAAKW,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBZ,OAAA;cAAAY,QAAA,EAAI;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvBhB,OAAA;cAAAY,QAAA,EAAG;YAEH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJhB,OAAA;cAAAY,QAAA,gBACEZ,OAAA;gBAAAY,QAAA,EAAI;cAAmD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5DhB,OAAA;gBAAAY,QAAA,EAAI;cAA2B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpChB,OAAA;gBAAAY,QAAA,EAAI;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACNhB,OAAA;YAAKW,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBZ,OAAA;cAAAY,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBhB,OAAA;cAAAY,QAAA,gBACEZ,OAAA;gBAAAY,QAAA,eAAIZ,OAAA;kBAAGiC,IAAI,EAAC,GAAG;kBAAChB,KAAK,EAAE;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAK,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1EhB,OAAA;gBAAAY,QAAA,eAAIZ,OAAA;kBAAGiC,IAAI,EAAC,GAAG;kBAAChB,KAAK,EAAE;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAK,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtEhB,OAAA;gBAAAY,QAAA,eAAIZ,OAAA;kBAAGiC,IAAI,EAAC,GAAG;kBAAChB,KAAK,EAAE;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAK,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrEhB,OAAA;gBAAAY,QAAA,eAAIZ,OAAA;kBAAGiC,IAAI,EAAC,GAAG;kBAAChB,KAAK,EAAE;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAAK,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACkB,EAAA,GAzQIjC,cAAc;AA2QpB,eAAeA,cAAc;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}