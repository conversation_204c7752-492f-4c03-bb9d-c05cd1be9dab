import React, { useState } from 'react';
import axios from 'axios';

const Backup = () => {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [backupData, setBackupData] = useState(null);

  const createBackup = async () => {
    try {
      setLoading(true);
      setError('');
      
      // جلب جميع البيانات
      const [
        customersRes,
        suppliersRes,
        productsRes,
        salesRes,
        purchasesRes,
        expensesRes,
        treasuryRes,
        inventoryRes
      ] = await Promise.all([
        axios.get('http://localhost:5000/api/customers'),
        axios.get('http://localhost:5000/api/suppliers'),
        axios.get('http://localhost:5000/api/products'),
        axios.get('http://localhost:5000/api/sales-invoices'),
        axios.get('http://localhost:5000/api/purchase-invoices'),
        axios.get('http://localhost:5000/api/expenses'),
        axios.get('http://localhost:5000/api/treasury-transactions'),
        axios.get('http://localhost:5000/api/inventory-movements')
      ]);

      const backup = {
        timestamp: new Date().toISOString(),
        version: '1.0',
        data: {
          customers: customersRes.data,
          suppliers: suppliersRes.data,
          products: productsRes.data,
          sales_invoices: salesRes.data,
          purchase_invoices: purchasesRes.data,
          expenses: expensesRes.data,
          treasury_transactions: treasuryRes.data,
          inventory_movements: inventoryRes.data
        }
      };

      setBackupData(backup);
      setMessage('تم إنشاء النسخة الاحتياطية بنجاح');
    } catch (error) {
      setError('خطأ في إنشاء النسخة الاحتياطية: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const downloadBackup = () => {
    if (!backupData) return;

    const dataStr = JSON.stringify(backupData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `accounting-backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const backupData = JSON.parse(e.target.result);
        setBackupData(backupData);
        setMessage('تم تحميل ملف النسخة الاحتياطية بنجاح');
      } catch (error) {
        setError('خطأ في قراءة ملف النسخة الاحتياطية');
      }
    };
    reader.readAsText(file);
  };

  const restoreBackup = async () => {
    if (!backupData || !backupData.data) {
      setError('لا توجد بيانات للاستعادة');
      return;
    }

    if (!window.confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.')) {
      return;
    }

    try {
      setLoading(true);
      setError('');

      // استعادة البيانات (هذا يتطلب APIs خاصة في الخادم)
      await axios.post('http://localhost:5000/api/restore-backup', backupData);
      
      setMessage('تم استعادة النسخة الاحتياطية بنجاح');
    } catch (error) {
      setError('خطأ في استعادة النسخة الاحتياطية: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const exportToExcel = async () => {
    try {
      setLoading(true);
      
      // إنشاء ملف Excel (يتطلب مكتبة إضافية)
      const response = await axios.get('http://localhost:5000/api/export-excel', {
        responseType: 'blob'
      });
      
      const blob = new Blob([response.data], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `accounting-data-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      setMessage('تم تصدير البيانات إلى Excel بنجاح');
    } catch (error) {
      setError('خطأ في تصدير البيانات: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container">
      <h1 className="page-title">النسخ الاحتياطي والاستعادة</h1>
      
      {message && <div className="success">{message}</div>}
      {error && <div className="error">{error}</div>}
      
      <div className="row">
        {/* إنشاء نسخة احتياطية */}
        <div className="col-md-6">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">💾 إنشاء نسخة احتياطية</h3>
            </div>
            <div className="card-body">
              <p>قم بإنشاء نسخة احتياطية من جميع بيانات النظام المحاسبي.</p>
              <div style={{ marginBottom: '1rem' }}>
                <button 
                  className="btn btn-primary" 
                  onClick={createBackup}
                  disabled={loading}
                  style={{ width: '100%', marginBottom: '1rem' }}
                >
                  {loading ? 'جاري الإنشاء...' : 'إنشاء نسخة احتياطية'}
                </button>
                
                {backupData && (
                  <button 
                    className="btn btn-success" 
                    onClick={downloadBackup}
                    style={{ width: '100%' }}
                  >
                    تحميل النسخة الاحتياطية
                  </button>
                )}
              </div>
              
              {backupData && (
                <div className="alert alert-info">
                  <strong>معلومات النسخة الاحتياطية:</strong><br />
                  التاريخ: {new Date(backupData.timestamp).toLocaleString('ar-EG')}<br />
                  الإصدار: {backupData.version}<br />
                  عدد العملاء: {backupData.data.customers?.length || 0}<br />
                  عدد الموردين: {backupData.data.suppliers?.length || 0}<br />
                  عدد المنتجات: {backupData.data.products?.length || 0}
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* استعادة نسخة احتياطية */}
        <div className="col-md-6">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">📂 استعادة نسخة احتياطية</h3>
            </div>
            <div className="card-body">
              <p>قم بتحميل ملف نسخة احتياطية لاستعادة البيانات.</p>
              
              <div className="form-group">
                <label className="form-label">اختر ملف النسخة الاحتياطية</label>
                <input
                  type="file"
                  accept=".json"
                  onChange={handleFileUpload}
                  className="form-control"
                />
              </div>
              
              {backupData && (
                <button 
                  className="btn btn-warning" 
                  onClick={restoreBackup}
                  disabled={loading}
                  style={{ width: '100%' }}
                >
                  {loading ? 'جاري الاستعادة...' : 'استعادة النسخة الاحتياطية'}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
      
      {/* تصدير البيانات */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">📊 تصدير البيانات</h3>
        </div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-4">
              <button 
                className="btn btn-success" 
                onClick={exportToExcel}
                disabled={loading}
                style={{ width: '100%' }}
              >
                تصدير إلى Excel
              </button>
            </div>
            <div className="col-md-4">
              <button 
                className="btn btn-info" 
                onClick={() => {
                  if (backupData) {
                    const csvData = convertToCSV(backupData.data);
                    downloadCSV(csvData, 'accounting-data.csv');
                  } else {
                    setError('يجب إنشاء نسخة احتياطية أولاً');
                  }
                }}
                style={{ width: '100%' }}
              >
                تصدير إلى CSV
              </button>
            </div>
            <div className="col-md-4">
              <button 
                className="btn btn-secondary" 
                onClick={() => {
                  if (backupData) {
                    downloadBackup();
                  } else {
                    setError('يجب إنشاء نسخة احتياطية أولاً');
                  }
                }}
                style={{ width: '100%' }}
              >
                تصدير إلى JSON
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* نصائح الأمان */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">🔒 نصائح الأمان</h3>
        </div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-6">
              <h5>📋 أفضل الممارسات:</h5>
              <ul>
                <li>قم بإنشاء نسخة احتياطية يومياً</li>
                <li>احتفظ بنسخ متعددة في أماكن مختلفة</li>
                <li>اختبر استعادة النسخ الاحتياطية بانتظام</li>
                <li>احم ملفات النسخ الاحتياطية بكلمة مرور</li>
              </ul>
            </div>
            <div className="col-md-6">
              <h5>⚠️ تحذيرات مهمة:</h5>
              <ul>
                <li>استعادة النسخة الاحتياطية تستبدل جميع البيانات</li>
                <li>تأكد من صحة ملف النسخة الاحتياطية قبل الاستعادة</li>
                <li>قم بإنشاء نسخة احتياطية قبل أي تحديث</li>
                <li>لا تشارك ملفات النسخ الاحتياطية مع أشخاص غير مخولين</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// دالة مساعدة لتحويل البيانات إلى CSV
const convertToCSV = (data) => {
  // تحويل بسيط للعملاء كمثال
  if (!data.customers) return '';
  
  const headers = ['الاسم', 'الهاتف', 'البريد الإلكتروني', 'العنوان'];
  const rows = data.customers.map(customer => [
    customer.name,
    customer.phone || '',
    customer.email || '',
    customer.address || ''
  ]);
  
  return [headers, ...rows].map(row => row.join(',')).join('\n');
};

// دالة مساعدة لتحميل ملف CSV
const downloadCSV = (csvData, filename) => {
  const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export default Backup;
