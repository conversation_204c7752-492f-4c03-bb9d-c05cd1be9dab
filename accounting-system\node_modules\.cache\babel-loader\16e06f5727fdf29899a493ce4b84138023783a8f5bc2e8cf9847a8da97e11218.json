{"ast": null, "code": "var _jsxFileName = \"F:\\\\\\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\\\\accounting-system\\\\src\\\\components\\\\ImportPDF.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImportPDF = ({\n  type,\n  onClose,\n  onImportComplete\n}) => {\n  _s();\n  const [file, setFile] = useState(null);\n  const [extractedText, setExtractedText] = useState('');\n  const [parsedData, setParsedData] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [step, setStep] = useState(1); // 1: Upload, 2: Extract, 3: Parse, 4: Import\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n  const onDrop = acceptedFiles => {\n    const file = acceptedFiles[0];\n    if (file) {\n      setFile(file);\n      extractTextFromPDF(file);\n    }\n  };\n  const {\n    getRootProps,\n    getInputProps,\n    isDragActive\n  } = useDropzone({\n    onDrop,\n    accept: {\n      'application/pdf': ['.pdf']\n    },\n    multiple: false\n  });\n  const extractTextFromPDF = async file => {\n    try {\n      setLoading(true);\n      setStep(2);\n      const formData = new FormData();\n      formData.append('pdf', file);\n      formData.append('type', type);\n      const response = await axios.post('http://localhost:5000/api/extract-pdf', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      setExtractedText(response.data.text);\n      parseExtractedData(response.data.text);\n    } catch (error) {\n      setError('خطأ في استخراج النص من PDF: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const parseExtractedData = text => {\n    setStep(3);\n    try {\n      let parsed = [];\n      switch (type) {\n        case 'customers':\n          parsed = parseCustomersFromText(text);\n          break;\n        case 'suppliers':\n          parsed = parseSuppliersFromText(text);\n          break;\n        case 'products':\n          parsed = parseProductsFromText(text);\n          break;\n        case 'invoices':\n          parsed = parseInvoicesFromText(text);\n          break;\n        default:\n          parsed = parseGenericFromText(text);\n      }\n      setParsedData(parsed);\n    } catch (error) {\n      setError('خطأ في تحليل البيانات: ' + error.message);\n    }\n  };\n  const parseCustomersFromText = text => {\n    const customers = [];\n    const lines = text.split('\\n');\n\n    // البحث عن أنماط العملاء\n    const patterns = {\n      name: /اسم|عميل|customer|name/i,\n      phone: /هاتف|تليفون|phone|mobile|tel/i,\n      email: /بريد|email|mail/i,\n      address: /عنوان|address/i\n    };\n    let currentCustomer = {};\n    lines.forEach(line => {\n      line = line.trim();\n      if (!line) return;\n\n      // البحث عن أرقام الهواتف\n      const phoneMatch = line.match(/(\\+?\\d{10,15})/);\n      if (phoneMatch) {\n        currentCustomer.phone = phoneMatch[1];\n      }\n\n      // البحث عن البريد الإلكتروني\n      const emailMatch = line.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})/);\n      if (emailMatch) {\n        currentCustomer.email = emailMatch[1];\n      }\n\n      // إذا كان السطر يحتوي على اسم محتمل\n      if (line.length > 3 && line.length < 50 && !phoneMatch && !emailMatch) {\n        if (currentCustomer.name) {\n          customers.push({\n            ...currentCustomer\n          });\n          currentCustomer = {};\n        }\n        currentCustomer.name = line;\n      }\n    });\n    if (currentCustomer.name) {\n      customers.push(currentCustomer);\n    }\n    return customers;\n  };\n  const parseSuppliersFromText = text => {\n    // نفس منطق العملاء مع تعديلات للموردين\n    return parseCustomersFromText(text);\n  };\n  const parseProductsFromText = text => {\n    const products = [];\n    const lines = text.split('\\n');\n    lines.forEach(line => {\n      line = line.trim();\n      if (!line) return;\n\n      // البحث عن أسعار\n      const priceMatch = line.match(/(\\d+\\.?\\d*)\\s*(ج\\.م|جنيه|ريال|درهم|دولار)/i);\n      if (priceMatch) {\n        const price = parseFloat(priceMatch[1]);\n        const productName = line.replace(priceMatch[0], '').trim();\n        if (productName) {\n          products.push({\n            name: productName,\n            price: price,\n            cost: price * 0.8,\n            // تقدير تكلفة 80% من السعر\n            stock_quantity: 0\n          });\n        }\n      }\n    });\n    return products;\n  };\n  const parseInvoicesFromText = text => {\n    const invoices = [];\n    const lines = text.split('\\n');\n    let currentInvoice = {};\n    let items = [];\n    lines.forEach(line => {\n      line = line.trim();\n      if (!line) return;\n\n      // البحث عن رقم الفاتورة\n      const invoiceMatch = line.match(/فاتورة|invoice|رقم|number.*?(\\d+)/i);\n      if (invoiceMatch) {\n        currentInvoice.invoice_number = invoiceMatch[1];\n      }\n\n      // البحث عن التاريخ\n      const dateMatch = line.match(/(\\d{1,2}\\/\\d{1,2}\\/\\d{4})/);\n      if (dateMatch) {\n        currentInvoice.invoice_date = dateMatch[1];\n      }\n\n      // البحث عن المجموع\n      const totalMatch = line.match(/مجموع|total|إجمالي.*?(\\d+\\.?\\d*)/i);\n      if (totalMatch) {\n        currentInvoice.total_amount = parseFloat(totalMatch[1]);\n      }\n    });\n    if (currentInvoice.invoice_number) {\n      invoices.push(currentInvoice);\n    }\n    return invoices;\n  };\n  const parseGenericFromText = text => {\n    // تحليل عام للنصوص\n    const lines = text.split('\\n').filter(line => line.trim());\n    return lines.map((line, index) => ({\n      id: index + 1,\n      content: line.trim()\n    }));\n  };\n  const importData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const response = await axios.post(`http://localhost:5000/api/import/${type}`, {\n        data: parsedData\n      });\n      setMessage(`تم استيراد ${response.data.imported} عنصر بنجاح من PDF`);\n      setStep(4);\n      if (onImportComplete) {\n        onImportComplete();\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      setError('خطأ في استيراد البيانات: ' + (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || error.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getTypeTitle = () => {\n    const titles = {\n      customers: 'العملاء',\n      suppliers: 'الموردين',\n      products: 'المنتجات',\n      invoices: 'الفواتير'\n    };\n    return titles[type] || type;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modal-overlay\",\n    style: {\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      zIndex: 1000\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-content\",\n      style: {\n        backgroundColor: 'white',\n        borderRadius: '10px',\n        padding: '2rem',\n        maxWidth: '90vw',\n        maxHeight: '90vh',\n        overflow: 'auto',\n        width: '800px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          marginBottom: '2rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"\\uD83D\\uDCC4 \\u0627\\u0633\\u062A\\u064A\\u0631\\u0627\\u062F \", getTypeTitle(), \" \\u0645\\u0646 PDF\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"btn btn-danger\",\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"success\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 21\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 19\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '2rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            marginBottom: '1rem'\n          },\n          children: ['رفع PDF', 'استخراج النص', 'تحليل البيانات', 'الاستيراد'].map((stepName, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '0.5rem 1rem',\n              borderRadius: '20px',\n              backgroundColor: step > index ? '#4CAF50' : step === index + 1 ? '#2196F3' : '#f0f0f0',\n              color: step >= index + 1 ? 'white' : '#666',\n              fontSize: '0.9rem'\n            },\n            children: [index + 1, \". \", stepName]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this), step === 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          ...getRootProps(),\n          style: {\n            border: '2px dashed #ccc',\n            borderRadius: '10px',\n            padding: '3rem',\n            textAlign: 'center',\n            cursor: 'pointer',\n            backgroundColor: isDragActive ? '#f0f8ff' : '#fafafa'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            ...getInputProps()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '3rem',\n              marginBottom: '1rem'\n            },\n            children: \"\\uD83D\\uDCC4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this), isDragActive ? /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u0627\\u0633\\u062D\\u0628 \\u0645\\u0644\\u0641 PDF \\u0647\\u0646\\u0627...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0627\\u0633\\u062D\\u0628 \\u0645\\u0644\\u0641 PDF \\u0647\\u0646\\u0627 \\u0623\\u0648 \\u0627\\u0636\\u063A\\u0637 \\u0644\\u0644\\u0627\\u062E\\u062A\\u064A\\u0627\\u0631\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#666',\n                fontSize: '0.9rem'\n              },\n              children: \"\\u0633\\u064A\\u062A\\u0645 \\u0627\\u0633\\u062A\\u062E\\u0631\\u0627\\u062C \\u0627\\u0644\\u0646\\u0635 \\u0648\\u062A\\u062D\\u0644\\u064A\\u0644\\u0647 \\u062A\\u0644\\u0642\\u0627\\u0626\\u064A\\u0627\\u064B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 11\n      }, this), step === 2 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '3rem',\n            marginBottom: '2rem'\n          },\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0633\\u062A\\u062E\\u0631\\u0627\\u062C \\u0627\\u0644\\u0646\\u0635 \\u0645\\u0646 PDF...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#666'\n          },\n          children: \"\\u064A\\u0631\\u062C\\u0649 \\u0627\\u0644\\u0627\\u0646\\u062A\\u0638\\u0627\\u0631\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 11\n      }, this), step === 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u0631\\u062C\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"\\u0627\\u0644\\u0646\\u0635 \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u0631\\u062C:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: extractedText,\n            onChange: e => setExtractedText(e.target.value),\n            style: {\n              width: '100%',\n              height: '200px',\n              padding: '1rem',\n              border: '1px solid #ddd',\n              borderRadius: '5px',\n              fontSize: '0.9rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '2rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: [\"\\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u0645\\u062D\\u0644\\u0644\\u0629 (\", parsedData.length, \" \\u0639\\u0646\\u0635\\u0631):\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              maxHeight: '200px',\n              overflow: 'auto',\n              border: '1px solid #ddd',\n              padding: '1rem'\n            },\n            children: parsedData.length > 0 ? /*#__PURE__*/_jsxDEV(\"pre\", {\n              style: {\n                fontSize: '0.8rem'\n              },\n              children: JSON.stringify(parsedData, null, 2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#666'\n              },\n              children: \"\\u0644\\u0645 \\u064A\\u062A\\u0645 \\u0627\\u0644\\u0639\\u062B\\u0648\\u0631 \\u0639\\u0644\\u0649 \\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0642\\u0627\\u0628\\u0644\\u0629 \\u0644\\u0644\\u062A\\u062D\\u0644\\u064A\\u0644\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '1rem',\n            justifyContent: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => parseExtractedData(extractedText),\n            className: \"btn btn-warning\",\n            children: \"\\u0625\\u0639\\u0627\\u062F\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0644\\u064A\\u0644\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: importData,\n            className: \"btn btn-success\",\n            disabled: loading || parsedData.length === 0,\n            children: loading ? 'جاري الاستيراد...' : 'تأكيد الاستيراد'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 11\n      }, this), step === 4 && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '4rem',\n            color: '#4CAF50',\n            marginBottom: '2rem'\n          },\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\u062A\\u0645 \\u0627\\u0644\\u0627\\u0633\\u062A\\u064A\\u0631\\u0627\\u062F \\u0628\\u0646\\u062C\\u0627\\u062D!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#666',\n            marginBottom: '2rem'\n          },\n          children: message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onClose,\n          className: \"btn btn-primary\",\n          children: \"\\u0625\\u063A\\u0644\\u0627\\u0642\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 242,\n    columnNumber: 5\n  }, this);\n};\n_s(ImportPDF, \"P4hFRbMqf4ObFpXPmEaF8ICYnCw=\", false, function () {\n  return [useDropzone];\n});\n_c = ImportPDF;\nexport default ImportPDF;\nvar _c;\n$RefreshReg$(_c, \"ImportPDF\");", "map": {"version": 3, "names": ["React", "useState", "useDropzone", "axios", "jsxDEV", "_jsxDEV", "ImportPDF", "type", "onClose", "onImportComplete", "_s", "file", "setFile", "extractedText", "setExtractedText", "parsedData", "setParsedData", "loading", "setLoading", "step", "setStep", "message", "setMessage", "error", "setError", "onDrop", "acceptedFiles", "extractTextFromPDF", "getRootProps", "getInputProps", "isDragActive", "accept", "multiple", "formData", "FormData", "append", "response", "post", "headers", "data", "text", "parseExtractedData", "parsed", "parseCustomersFromText", "parseSuppliersFromText", "parseProductsFromText", "parseInvoicesFromText", "parseGenericFromText", "customers", "lines", "split", "patterns", "name", "phone", "email", "address", "currentCustomer", "for<PERSON>ach", "line", "trim", "phoneMatch", "match", "emailMatch", "length", "push", "products", "priceMatch", "price", "parseFloat", "productName", "replace", "cost", "stock_quantity", "invoices", "currentInvoice", "items", "invoiceMatch", "invoice_number", "dateMatch", "invoice_date", "totalMatch", "total_amount", "filter", "map", "index", "id", "content", "importData", "imported", "_error$response", "_error$response$data", "getTypeTitle", "titles", "suppliers", "className", "style", "position", "top", "left", "right", "bottom", "backgroundColor", "display", "justifyContent", "alignItems", "zIndex", "children", "borderRadius", "padding", "max<PERSON><PERSON><PERSON>", "maxHeight", "overflow", "width", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "<PERSON><PERSON><PERSON>", "color", "fontSize", "border", "textAlign", "cursor", "value", "onChange", "e", "target", "height", "JSON", "stringify", "gap", "disabled", "_c", "$RefreshReg$"], "sources": ["F:/القيمة المضافة/accounting-system/src/components/ImportPDF.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport axios from 'axios';\n\nconst ImportPDF = ({ type, onClose, onImportComplete }) => {\n  const [file, setFile] = useState(null);\n  const [extractedText, setExtractedText] = useState('');\n  const [parsedData, setParsedData] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [step, setStep] = useState(1); // 1: Upload, 2: Extract, 3: Parse, 4: Import\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n\n  const onDrop = (acceptedFiles) => {\n    const file = acceptedFiles[0];\n    if (file) {\n      setFile(file);\n      extractTextFromPDF(file);\n    }\n  };\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'application/pdf': ['.pdf']\n    },\n    multiple: false\n  });\n\n  const extractTextFromPDF = async (file) => {\n    try {\n      setLoading(true);\n      setStep(2);\n      \n      const formData = new FormData();\n      formData.append('pdf', file);\n      formData.append('type', type);\n      \n      const response = await axios.post('http://localhost:5000/api/extract-pdf', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      \n      setExtractedText(response.data.text);\n      parseExtractedData(response.data.text);\n    } catch (error) {\n      setError('خطأ في استخراج النص من PDF: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const parseExtractedData = (text) => {\n    setStep(3);\n    \n    try {\n      let parsed = [];\n      \n      switch (type) {\n        case 'customers':\n          parsed = parseCustomersFromText(text);\n          break;\n        case 'suppliers':\n          parsed = parseSuppliersFromText(text);\n          break;\n        case 'products':\n          parsed = parseProductsFromText(text);\n          break;\n        case 'invoices':\n          parsed = parseInvoicesFromText(text);\n          break;\n        default:\n          parsed = parseGenericFromText(text);\n      }\n      \n      setParsedData(parsed);\n    } catch (error) {\n      setError('خطأ في تحليل البيانات: ' + error.message);\n    }\n  };\n\n  const parseCustomersFromText = (text) => {\n    const customers = [];\n    const lines = text.split('\\n');\n    \n    // البحث عن أنماط العملاء\n    const patterns = {\n      name: /اسم|عميل|customer|name/i,\n      phone: /هاتف|تليفون|phone|mobile|tel/i,\n      email: /بريد|email|mail/i,\n      address: /عنوان|address/i\n    };\n    \n    let currentCustomer = {};\n    \n    lines.forEach(line => {\n      line = line.trim();\n      if (!line) return;\n      \n      // البحث عن أرقام الهواتف\n      const phoneMatch = line.match(/(\\+?\\d{10,15})/);\n      if (phoneMatch) {\n        currentCustomer.phone = phoneMatch[1];\n      }\n      \n      // البحث عن البريد الإلكتروني\n      const emailMatch = line.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})/);\n      if (emailMatch) {\n        currentCustomer.email = emailMatch[1];\n      }\n      \n      // إذا كان السطر يحتوي على اسم محتمل\n      if (line.length > 3 && line.length < 50 && !phoneMatch && !emailMatch) {\n        if (currentCustomer.name) {\n          customers.push({ ...currentCustomer });\n          currentCustomer = {};\n        }\n        currentCustomer.name = line;\n      }\n    });\n    \n    if (currentCustomer.name) {\n      customers.push(currentCustomer);\n    }\n    \n    return customers;\n  };\n\n  const parseSuppliersFromText = (text) => {\n    // نفس منطق العملاء مع تعديلات للموردين\n    return parseCustomersFromText(text);\n  };\n\n  const parseProductsFromText = (text) => {\n    const products = [];\n    const lines = text.split('\\n');\n    \n    lines.forEach(line => {\n      line = line.trim();\n      if (!line) return;\n      \n      // البحث عن أسعار\n      const priceMatch = line.match(/(\\d+\\.?\\d*)\\s*(ج\\.م|جنيه|ريال|درهم|دولار)/i);\n      if (priceMatch) {\n        const price = parseFloat(priceMatch[1]);\n        const productName = line.replace(priceMatch[0], '').trim();\n        \n        if (productName) {\n          products.push({\n            name: productName,\n            price: price,\n            cost: price * 0.8, // تقدير تكلفة 80% من السعر\n            stock_quantity: 0\n          });\n        }\n      }\n    });\n    \n    return products;\n  };\n\n  const parseInvoicesFromText = (text) => {\n    const invoices = [];\n    const lines = text.split('\\n');\n    \n    let currentInvoice = {};\n    let items = [];\n    \n    lines.forEach(line => {\n      line = line.trim();\n      if (!line) return;\n      \n      // البحث عن رقم الفاتورة\n      const invoiceMatch = line.match(/فاتورة|invoice|رقم|number.*?(\\d+)/i);\n      if (invoiceMatch) {\n        currentInvoice.invoice_number = invoiceMatch[1];\n      }\n      \n      // البحث عن التاريخ\n      const dateMatch = line.match(/(\\d{1,2}\\/\\d{1,2}\\/\\d{4})/);\n      if (dateMatch) {\n        currentInvoice.invoice_date = dateMatch[1];\n      }\n      \n      // البحث عن المجموع\n      const totalMatch = line.match(/مجموع|total|إجمالي.*?(\\d+\\.?\\d*)/i);\n      if (totalMatch) {\n        currentInvoice.total_amount = parseFloat(totalMatch[1]);\n      }\n    });\n    \n    if (currentInvoice.invoice_number) {\n      invoices.push(currentInvoice);\n    }\n    \n    return invoices;\n  };\n\n  const parseGenericFromText = (text) => {\n    // تحليل عام للنصوص\n    const lines = text.split('\\n').filter(line => line.trim());\n    return lines.map((line, index) => ({\n      id: index + 1,\n      content: line.trim()\n    }));\n  };\n\n  const importData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      \n      const response = await axios.post(`http://localhost:5000/api/import/${type}`, {\n        data: parsedData\n      });\n\n      setMessage(`تم استيراد ${response.data.imported} عنصر بنجاح من PDF`);\n      setStep(4);\n      \n      if (onImportComplete) {\n        onImportComplete();\n      }\n    } catch (error) {\n      setError('خطأ في استيراد البيانات: ' + (error.response?.data?.error || error.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getTypeTitle = () => {\n    const titles = {\n      customers: 'العملاء',\n      suppliers: 'الموردين',\n      products: 'المنتجات',\n      invoices: 'الفواتير'\n    };\n    return titles[type] || type;\n  };\n\n  return (\n    <div className=\"modal-overlay\" style={{\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      zIndex: 1000\n    }}>\n      <div className=\"modal-content\" style={{\n        backgroundColor: 'white',\n        borderRadius: '10px',\n        padding: '2rem',\n        maxWidth: '90vw',\n        maxHeight: '90vh',\n        overflow: 'auto',\n        width: '800px'\n      }}>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>\n          <h2>📄 استيراد {getTypeTitle()} من PDF</h2>\n          <button onClick={onClose} className=\"btn btn-danger\">✕</button>\n        </div>\n\n        {message && <div className=\"success\">{message}</div>}\n        {error && <div className=\"error\">{error}</div>}\n\n        {/* مؤشر التقدم */}\n        <div style={{ marginBottom: '2rem' }}>\n          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '1rem' }}>\n            {['رفع PDF', 'استخراج النص', 'تحليل البيانات', 'الاستيراد'].map((stepName, index) => (\n              <div key={index} style={{\n                padding: '0.5rem 1rem',\n                borderRadius: '20px',\n                backgroundColor: step > index ? '#4CAF50' : step === index + 1 ? '#2196F3' : '#f0f0f0',\n                color: step >= index + 1 ? 'white' : '#666',\n                fontSize: '0.9rem'\n              }}>\n                {index + 1}. {stepName}\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* الخطوة 1: رفع الملف */}\n        {step === 1 && (\n          <div>\n            <div\n              {...getRootProps()}\n              style={{\n                border: '2px dashed #ccc',\n                borderRadius: '10px',\n                padding: '3rem',\n                textAlign: 'center',\n                cursor: 'pointer',\n                backgroundColor: isDragActive ? '#f0f8ff' : '#fafafa'\n              }}\n            >\n              <input {...getInputProps()} />\n              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📄</div>\n              {isDragActive ? (\n                <p>اسحب ملف PDF هنا...</p>\n              ) : (\n                <div>\n                  <p>اسحب ملف PDF هنا أو اضغط للاختيار</p>\n                  <p style={{ color: '#666', fontSize: '0.9rem' }}>\n                    سيتم استخراج النص وتحليله تلقائياً\n                  </p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* الخطوة 2: استخراج النص */}\n        {step === 2 && (\n          <div style={{ textAlign: 'center' }}>\n            <div style={{ fontSize: '3rem', marginBottom: '2rem' }}>⏳</div>\n            <h3>جاري استخراج النص من PDF...</h3>\n            <p style={{ color: '#666' }}>يرجى الانتظار</p>\n          </div>\n        )}\n\n        {/* الخطوة 3: تحليل البيانات */}\n        {step === 3 && (\n          <div>\n            <h3>البيانات المستخرجة</h3>\n            \n            <div style={{ marginBottom: '2rem' }}>\n              <h4>النص المستخرج:</h4>\n              <textarea\n                value={extractedText}\n                onChange={(e) => setExtractedText(e.target.value)}\n                style={{\n                  width: '100%',\n                  height: '200px',\n                  padding: '1rem',\n                  border: '1px solid #ddd',\n                  borderRadius: '5px',\n                  fontSize: '0.9rem'\n                }}\n              />\n            </div>\n\n            <div style={{ marginBottom: '2rem' }}>\n              <h4>البيانات المحللة ({parsedData.length} عنصر):</h4>\n              <div style={{ maxHeight: '200px', overflow: 'auto', border: '1px solid #ddd', padding: '1rem' }}>\n                {parsedData.length > 0 ? (\n                  <pre style={{ fontSize: '0.8rem' }}>{JSON.stringify(parsedData, null, 2)}</pre>\n                ) : (\n                  <p style={{ color: '#666' }}>لم يتم العثور على بيانات قابلة للتحليل</p>\n                )}\n              </div>\n            </div>\n\n            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>\n              <button onClick={() => parseExtractedData(extractedText)} className=\"btn btn-warning\">\n                إعادة التحليل\n              </button>\n              <button \n                onClick={importData} \n                className=\"btn btn-success\" \n                disabled={loading || parsedData.length === 0}\n              >\n                {loading ? 'جاري الاستيراد...' : 'تأكيد الاستيراد'}\n              </button>\n            </div>\n          </div>\n        )}\n\n        {/* الخطوة 4: اكتمال الاستيراد */}\n        {step === 4 && (\n          <div style={{ textAlign: 'center' }}>\n            <div style={{ fontSize: '4rem', color: '#4CAF50', marginBottom: '2rem' }}>✅</div>\n            <h3>تم الاستيراد بنجاح!</h3>\n            <p style={{ color: '#666', marginBottom: '2rem' }}>{message}</p>\n            \n            <button onClick={onClose} className=\"btn btn-primary\">\n              إغلاق\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ImportPDF;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,SAAS,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EACzD,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACY,aAAa,EAAEC,gBAAgB,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkB,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMwB,MAAM,GAAIC,aAAa,IAAK;IAChC,MAAMf,IAAI,GAAGe,aAAa,CAAC,CAAC,CAAC;IAC7B,IAAIf,IAAI,EAAE;MACRC,OAAO,CAACD,IAAI,CAAC;MACbgB,kBAAkB,CAAChB,IAAI,CAAC;IAC1B;EACF,CAAC;EAED,MAAM;IAAEiB,YAAY;IAAEC,aAAa;IAAEC;EAAa,CAAC,GAAG5B,WAAW,CAAC;IAChEuB,MAAM;IACNM,MAAM,EAAE;MACN,iBAAiB,EAAE,CAAC,MAAM;IAC5B,CAAC;IACDC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAML,kBAAkB,GAAG,MAAOhB,IAAI,IAAK;IACzC,IAAI;MACFO,UAAU,CAAC,IAAI,CAAC;MAChBE,OAAO,CAAC,CAAC,CAAC;MAEV,MAAMa,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,KAAK,EAAExB,IAAI,CAAC;MAC5BsB,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE5B,IAAI,CAAC;MAE7B,MAAM6B,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,IAAI,CAAC,uCAAuC,EAAEJ,QAAQ,EAAE;QACnFK,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEFxB,gBAAgB,CAACsB,QAAQ,CAACG,IAAI,CAACC,IAAI,CAAC;MACpCC,kBAAkB,CAACL,QAAQ,CAACG,IAAI,CAACC,IAAI,CAAC;IACxC,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdC,QAAQ,CAAC,8BAA8B,GAAGD,KAAK,CAACF,OAAO,CAAC;IAC1D,CAAC,SAAS;MACRH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,kBAAkB,GAAID,IAAI,IAAK;IACnCpB,OAAO,CAAC,CAAC,CAAC;IAEV,IAAI;MACF,IAAIsB,MAAM,GAAG,EAAE;MAEf,QAAQnC,IAAI;QACV,KAAK,WAAW;UACdmC,MAAM,GAAGC,sBAAsB,CAACH,IAAI,CAAC;UACrC;QACF,KAAK,WAAW;UACdE,MAAM,GAAGE,sBAAsB,CAACJ,IAAI,CAAC;UACrC;QACF,KAAK,UAAU;UACbE,MAAM,GAAGG,qBAAqB,CAACL,IAAI,CAAC;UACpC;QACF,KAAK,UAAU;UACbE,MAAM,GAAGI,qBAAqB,CAACN,IAAI,CAAC;UACpC;QACF;UACEE,MAAM,GAAGK,oBAAoB,CAACP,IAAI,CAAC;MACvC;MAEAxB,aAAa,CAAC0B,MAAM,CAAC;IACvB,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdC,QAAQ,CAAC,yBAAyB,GAAGD,KAAK,CAACF,OAAO,CAAC;IACrD;EACF,CAAC;EAED,MAAMsB,sBAAsB,GAAIH,IAAI,IAAK;IACvC,MAAMQ,SAAS,GAAG,EAAE;IACpB,MAAMC,KAAK,GAAGT,IAAI,CAACU,KAAK,CAAC,IAAI,CAAC;;IAE9B;IACA,MAAMC,QAAQ,GAAG;MACfC,IAAI,EAAE,yBAAyB;MAC/BC,KAAK,EAAE,+BAA+B;MACtCC,KAAK,EAAE,kBAAkB;MACzBC,OAAO,EAAE;IACX,CAAC;IAED,IAAIC,eAAe,GAAG,CAAC,CAAC;IAExBP,KAAK,CAACQ,OAAO,CAACC,IAAI,IAAI;MACpBA,IAAI,GAAGA,IAAI,CAACC,IAAI,CAAC,CAAC;MAClB,IAAI,CAACD,IAAI,EAAE;;MAEX;MACA,MAAME,UAAU,GAAGF,IAAI,CAACG,KAAK,CAAC,gBAAgB,CAAC;MAC/C,IAAID,UAAU,EAAE;QACdJ,eAAe,CAACH,KAAK,GAAGO,UAAU,CAAC,CAAC,CAAC;MACvC;;MAEA;MACA,MAAME,UAAU,GAAGJ,IAAI,CAACG,KAAK,CAAC,kDAAkD,CAAC;MACjF,IAAIC,UAAU,EAAE;QACdN,eAAe,CAACF,KAAK,GAAGQ,UAAU,CAAC,CAAC,CAAC;MACvC;;MAEA;MACA,IAAIJ,IAAI,CAACK,MAAM,GAAG,CAAC,IAAIL,IAAI,CAACK,MAAM,GAAG,EAAE,IAAI,CAACH,UAAU,IAAI,CAACE,UAAU,EAAE;QACrE,IAAIN,eAAe,CAACJ,IAAI,EAAE;UACxBJ,SAAS,CAACgB,IAAI,CAAC;YAAE,GAAGR;UAAgB,CAAC,CAAC;UACtCA,eAAe,GAAG,CAAC,CAAC;QACtB;QACAA,eAAe,CAACJ,IAAI,GAAGM,IAAI;MAC7B;IACF,CAAC,CAAC;IAEF,IAAIF,eAAe,CAACJ,IAAI,EAAE;MACxBJ,SAAS,CAACgB,IAAI,CAACR,eAAe,CAAC;IACjC;IAEA,OAAOR,SAAS;EAClB,CAAC;EAED,MAAMJ,sBAAsB,GAAIJ,IAAI,IAAK;IACvC;IACA,OAAOG,sBAAsB,CAACH,IAAI,CAAC;EACrC,CAAC;EAED,MAAMK,qBAAqB,GAAIL,IAAI,IAAK;IACtC,MAAMyB,QAAQ,GAAG,EAAE;IACnB,MAAMhB,KAAK,GAAGT,IAAI,CAACU,KAAK,CAAC,IAAI,CAAC;IAE9BD,KAAK,CAACQ,OAAO,CAACC,IAAI,IAAI;MACpBA,IAAI,GAAGA,IAAI,CAACC,IAAI,CAAC,CAAC;MAClB,IAAI,CAACD,IAAI,EAAE;;MAEX;MACA,MAAMQ,UAAU,GAAGR,IAAI,CAACG,KAAK,CAAC,4CAA4C,CAAC;MAC3E,IAAIK,UAAU,EAAE;QACd,MAAMC,KAAK,GAAGC,UAAU,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC;QACvC,MAAMG,WAAW,GAAGX,IAAI,CAACY,OAAO,CAACJ,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAACP,IAAI,CAAC,CAAC;QAE1D,IAAIU,WAAW,EAAE;UACfJ,QAAQ,CAACD,IAAI,CAAC;YACZZ,IAAI,EAAEiB,WAAW;YACjBF,KAAK,EAAEA,KAAK;YACZI,IAAI,EAAEJ,KAAK,GAAG,GAAG;YAAE;YACnBK,cAAc,EAAE;UAClB,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;IAEF,OAAOP,QAAQ;EACjB,CAAC;EAED,MAAMnB,qBAAqB,GAAIN,IAAI,IAAK;IACtC,MAAMiC,QAAQ,GAAG,EAAE;IACnB,MAAMxB,KAAK,GAAGT,IAAI,CAACU,KAAK,CAAC,IAAI,CAAC;IAE9B,IAAIwB,cAAc,GAAG,CAAC,CAAC;IACvB,IAAIC,KAAK,GAAG,EAAE;IAEd1B,KAAK,CAACQ,OAAO,CAACC,IAAI,IAAI;MACpBA,IAAI,GAAGA,IAAI,CAACC,IAAI,CAAC,CAAC;MAClB,IAAI,CAACD,IAAI,EAAE;;MAEX;MACA,MAAMkB,YAAY,GAAGlB,IAAI,CAACG,KAAK,CAAC,oCAAoC,CAAC;MACrE,IAAIe,YAAY,EAAE;QAChBF,cAAc,CAACG,cAAc,GAAGD,YAAY,CAAC,CAAC,CAAC;MACjD;;MAEA;MACA,MAAME,SAAS,GAAGpB,IAAI,CAACG,KAAK,CAAC,2BAA2B,CAAC;MACzD,IAAIiB,SAAS,EAAE;QACbJ,cAAc,CAACK,YAAY,GAAGD,SAAS,CAAC,CAAC,CAAC;MAC5C;;MAEA;MACA,MAAME,UAAU,GAAGtB,IAAI,CAACG,KAAK,CAAC,mCAAmC,CAAC;MAClE,IAAImB,UAAU,EAAE;QACdN,cAAc,CAACO,YAAY,GAAGb,UAAU,CAACY,UAAU,CAAC,CAAC,CAAC,CAAC;MACzD;IACF,CAAC,CAAC;IAEF,IAAIN,cAAc,CAACG,cAAc,EAAE;MACjCJ,QAAQ,CAACT,IAAI,CAACU,cAAc,CAAC;IAC/B;IAEA,OAAOD,QAAQ;EACjB,CAAC;EAED,MAAM1B,oBAAoB,GAAIP,IAAI,IAAK;IACrC;IACA,MAAMS,KAAK,GAAGT,IAAI,CAACU,KAAK,CAAC,IAAI,CAAC,CAACgC,MAAM,CAACxB,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;IAC1D,OAAOV,KAAK,CAACkC,GAAG,CAAC,CAACzB,IAAI,EAAE0B,KAAK,MAAM;MACjCC,EAAE,EAAED,KAAK,GAAG,CAAC;MACbE,OAAO,EAAE5B,IAAI,CAACC,IAAI,CAAC;IACrB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM4B,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFrE,UAAU,CAAC,IAAI,CAAC;MAChBM,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAMY,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,IAAI,CAAC,oCAAoC9B,IAAI,EAAE,EAAE;QAC5EgC,IAAI,EAAExB;MACR,CAAC,CAAC;MAEFO,UAAU,CAAC,cAAcc,QAAQ,CAACG,IAAI,CAACiD,QAAQ,oBAAoB,CAAC;MACpEpE,OAAO,CAAC,CAAC,CAAC;MAEV,IAAIX,gBAAgB,EAAE;QACpBA,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC,OAAOc,KAAK,EAAE;MAAA,IAAAkE,eAAA,EAAAC,oBAAA;MACdlE,QAAQ,CAAC,2BAA2B,IAAI,EAAAiE,eAAA,GAAAlE,KAAK,CAACa,QAAQ,cAAAqD,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBlD,IAAI,cAAAmD,oBAAA,uBAApBA,oBAAA,CAAsBnE,KAAK,KAAIA,KAAK,CAACF,OAAO,CAAC,CAAC;IACxF,CAAC,SAAS;MACRH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyE,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG;MACb5C,SAAS,EAAE,SAAS;MACpB6C,SAAS,EAAE,UAAU;MACrB5B,QAAQ,EAAE,UAAU;MACpBQ,QAAQ,EAAE;IACZ,CAAC;IACD,OAAOmB,MAAM,CAACrF,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;EAED,oBACEF,OAAA;IAAKyF,SAAS,EAAC,eAAe;IAACC,KAAK,EAAE;MACpCC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,eAAe,EAAE,iBAAiB;MAClCC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,MAAM,EAAE;IACV,CAAE;IAAAC,QAAA,eACArG,OAAA;MAAKyF,SAAS,EAAC,eAAe;MAACC,KAAK,EAAE;QACpCM,eAAe,EAAE,OAAO;QACxBM,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE,MAAM;QACfC,QAAQ,EAAE,MAAM;QAChBC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE;MACT,CAAE;MAAAN,QAAA,gBACArG,OAAA;QAAK0F,KAAK,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAES,YAAY,EAAE;QAAO,CAAE;QAAAP,QAAA,gBAC3GrG,OAAA;UAAAqG,QAAA,GAAI,0DAAW,EAACf,YAAY,CAAC,CAAC,EAAC,mBAAO;QAAA;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3ChH,OAAA;UAAQiH,OAAO,EAAE9G,OAAQ;UAACsF,SAAS,EAAC,gBAAgB;UAAAY,QAAA,EAAC;QAAC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,EAELhG,OAAO,iBAAIhB,OAAA;QAAKyF,SAAS,EAAC,SAAS;QAAAY,QAAA,EAAErF;MAAO;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EACnD9F,KAAK,iBAAIlB,OAAA;QAAKyF,SAAS,EAAC,OAAO;QAAAY,QAAA,EAAEnF;MAAK;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAG9ChH,OAAA;QAAK0F,KAAK,EAAE;UAAEkB,YAAY,EAAE;QAAO,CAAE;QAAAP,QAAA,eACnCrG,OAAA;UAAK0F,KAAK,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEU,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,EACpF,CAAC,SAAS,EAAE,cAAc,EAAE,gBAAgB,EAAE,WAAW,CAAC,CAACvB,GAAG,CAAC,CAACoC,QAAQ,EAAEnC,KAAK,kBAC9E/E,OAAA;YAAiB0F,KAAK,EAAE;cACtBa,OAAO,EAAE,aAAa;cACtBD,YAAY,EAAE,MAAM;cACpBN,eAAe,EAAElF,IAAI,GAAGiE,KAAK,GAAG,SAAS,GAAGjE,IAAI,KAAKiE,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;cACtFoC,KAAK,EAAErG,IAAI,IAAIiE,KAAK,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM;cAC3CqC,QAAQ,EAAE;YACZ,CAAE;YAAAf,QAAA,GACCtB,KAAK,GAAG,CAAC,EAAC,IAAE,EAACmC,QAAQ;UAAA,GAPdnC,KAAK;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLlG,IAAI,KAAK,CAAC,iBACTd,OAAA;QAAAqG,QAAA,eACErG,OAAA;UAAA,GACMuB,YAAY,CAAC,CAAC;UAClBmE,KAAK,EAAE;YACL2B,MAAM,EAAE,iBAAiB;YACzBf,YAAY,EAAE,MAAM;YACpBC,OAAO,EAAE,MAAM;YACfe,SAAS,EAAE,QAAQ;YACnBC,MAAM,EAAE,SAAS;YACjBvB,eAAe,EAAEvE,YAAY,GAAG,SAAS,GAAG;UAC9C,CAAE;UAAA4E,QAAA,gBAEFrG,OAAA;YAAA,GAAWwB,aAAa,CAAC;UAAC;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9BhH,OAAA;YAAK0F,KAAK,EAAE;cAAE0B,QAAQ,EAAE,MAAM;cAAER,YAAY,EAAE;YAAO,CAAE;YAAAP,QAAA,EAAC;UAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EAC/DvF,YAAY,gBACXzB,OAAA;YAAAqG,QAAA,EAAG;UAAmB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,gBAE1BhH,OAAA;YAAAqG,QAAA,gBACErG,OAAA;cAAAqG,QAAA,EAAG;YAAiC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxChH,OAAA;cAAG0F,KAAK,EAAE;gBAAEyB,KAAK,EAAE,MAAM;gBAAEC,QAAQ,EAAE;cAAS,CAAE;cAAAf,QAAA,EAAC;YAEjD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAlG,IAAI,KAAK,CAAC,iBACTd,OAAA;QAAK0F,KAAK,EAAE;UAAE4B,SAAS,EAAE;QAAS,CAAE;QAAAjB,QAAA,gBAClCrG,OAAA;UAAK0F,KAAK,EAAE;YAAE0B,QAAQ,EAAE,MAAM;YAAER,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,EAAC;QAAC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC/DhH,OAAA;UAAAqG,QAAA,EAAI;QAA2B;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpChH,OAAA;UAAG0F,KAAK,EAAE;YAAEyB,KAAK,EAAE;UAAO,CAAE;UAAAd,QAAA,EAAC;QAAa;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CACN,EAGAlG,IAAI,KAAK,CAAC,iBACTd,OAAA;QAAAqG,QAAA,gBACErG,OAAA;UAAAqG,QAAA,EAAI;QAAkB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE3BhH,OAAA;UAAK0F,KAAK,EAAE;YAAEkB,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBACnCrG,OAAA;YAAAqG,QAAA,EAAI;UAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvBhH,OAAA;YACEwH,KAAK,EAAEhH,aAAc;YACrBiH,QAAQ,EAAGC,CAAC,IAAKjH,gBAAgB,CAACiH,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAClD9B,KAAK,EAAE;cACLiB,KAAK,EAAE,MAAM;cACbiB,MAAM,EAAE,OAAO;cACfrB,OAAO,EAAE,MAAM;cACfc,MAAM,EAAE,gBAAgB;cACxBf,YAAY,EAAE,KAAK;cACnBc,QAAQ,EAAE;YACZ;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENhH,OAAA;UAAK0F,KAAK,EAAE;YAAEkB,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,gBACnCrG,OAAA;YAAAqG,QAAA,GAAI,+FAAkB,EAAC3F,UAAU,CAACgD,MAAM,EAAC,6BAAO;UAAA;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDhH,OAAA;YAAK0F,KAAK,EAAE;cAAEe,SAAS,EAAE,OAAO;cAAEC,QAAQ,EAAE,MAAM;cAAEW,MAAM,EAAE,gBAAgB;cAAEd,OAAO,EAAE;YAAO,CAAE;YAAAF,QAAA,EAC7F3F,UAAU,CAACgD,MAAM,GAAG,CAAC,gBACpB1D,OAAA;cAAK0F,KAAK,EAAE;gBAAE0B,QAAQ,EAAE;cAAS,CAAE;cAAAf,QAAA,EAAEwB,IAAI,CAACC,SAAS,CAACpH,UAAU,EAAE,IAAI,EAAE,CAAC;YAAC;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,gBAE/EhH,OAAA;cAAG0F,KAAK,EAAE;gBAAEyB,KAAK,EAAE;cAAO,CAAE;cAAAd,QAAA,EAAC;YAAsC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UACvE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhH,OAAA;UAAK0F,KAAK,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAE8B,GAAG,EAAE,MAAM;YAAE7B,cAAc,EAAE;UAAS,CAAE;UAAAG,QAAA,gBACrErG,OAAA;YAAQiH,OAAO,EAAEA,CAAA,KAAM7E,kBAAkB,CAAC5B,aAAa,CAAE;YAACiF,SAAS,EAAC,iBAAiB;YAAAY,QAAA,EAAC;UAEtF;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThH,OAAA;YACEiH,OAAO,EAAE/B,UAAW;YACpBO,SAAS,EAAC,iBAAiB;YAC3BuC,QAAQ,EAAEpH,OAAO,IAAIF,UAAU,CAACgD,MAAM,KAAK,CAAE;YAAA2C,QAAA,EAE5CzF,OAAO,GAAG,mBAAmB,GAAG;UAAiB;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAlG,IAAI,KAAK,CAAC,iBACTd,OAAA;QAAK0F,KAAK,EAAE;UAAE4B,SAAS,EAAE;QAAS,CAAE;QAAAjB,QAAA,gBAClCrG,OAAA;UAAK0F,KAAK,EAAE;YAAE0B,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE,SAAS;YAAEP,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,EAAC;QAAC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjFhH,OAAA;UAAAqG,QAAA,EAAI;QAAmB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5BhH,OAAA;UAAG0F,KAAK,EAAE;YAAEyB,KAAK,EAAE,MAAM;YAAEP,YAAY,EAAE;UAAO,CAAE;UAAAP,QAAA,EAAErF;QAAO;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEhEhH,OAAA;UAAQiH,OAAO,EAAE9G,OAAQ;UAACsF,SAAS,EAAC,iBAAiB;UAAAY,QAAA,EAAC;QAEtD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3G,EAAA,CAhYIJ,SAAS;EAAA,QAiByCJ,WAAW;AAAA;AAAAoI,EAAA,GAjB7DhI,SAAS;AAkYf,eAAeA,SAAS;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}