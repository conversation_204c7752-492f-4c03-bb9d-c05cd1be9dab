{"name": "accounting-system", "version": "1.0.0", "description": "نظام محاسبي متكامل باللغة العربية مع إمكانيات متقدمة لإدارة الأعمال", "keywords": ["accounting", "محاسبة", "نظام محاسبي", "فواتير", "مخزون", "عربي", "arabic", "business", "finance"], "author": "Accounting System Team", "license": "MIT", "homepage": ".", "private": false, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.10.0", "chart.js": "^4.5.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "pdf-parse": "^1.1.1", "pdf2json": "^3.1.6", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-router-dom": "^7.6.3", "react-scripts": "5.0.1", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}