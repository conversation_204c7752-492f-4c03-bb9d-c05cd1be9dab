import React, { useState, useEffect } from 'react';
import axios from 'axios';

const Customers = () => {
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    tax_number: ''
  });
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    fetchCustomers();
  }, []);

  const fetchCustomers = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:5000/api/customers');
      setCustomers(response.data);
    } catch (error) {
      setError('خطأ في جلب بيانات العملاء');
      console.error('Error fetching customers:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingCustomer) {
        await axios.put(`http://localhost:5000/api/customers/${editingCustomer.id}`, formData);
        setMessage('تم تحديث العميل بنجاح');
      } else {
        await axios.post('http://localhost:5000/api/customers', formData);
        setMessage('تم إضافة العميل بنجاح');
      }
      
      setFormData({
        name: '',
        phone: '',
        email: '',
        address: '',
        tax_number: ''
      });
      setShowForm(false);
      setEditingCustomer(null);
      fetchCustomers();
      
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      setError('خطأ في حفظ بيانات العميل');
      console.error('Error saving customer:', error);
    }
  };

  const handleEdit = (customer) => {
    setEditingCustomer(customer);
    setFormData({
      name: customer.name,
      phone: customer.phone || '',
      email: customer.email || '',
      address: customer.address || '',
      tax_number: customer.tax_number || ''
    });
    setShowForm(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      try {
        await axios.delete(`http://localhost:5000/api/customers/${id}`);
        setMessage('تم حذف العميل بنجاح');
        fetchCustomers();
        setTimeout(() => setMessage(''), 3000);
      } catch (error) {
        setError('خطأ في حذف العميل');
        console.error('Error deleting customer:', error);
      }
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      phone: '',
      email: '',
      address: '',
      tax_number: ''
    });
    setShowForm(false);
    setEditingCustomer(null);
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">جاري تحميل بيانات العملاء...</div>
      </div>
    );
  }

  return (
    <div className="container">
      <h1 className="page-title">إدارة العملاء</h1>
      
      {message && <div className="success">{message}</div>}
      {error && <div className="error">{error}</div>}
      
      <div className="card">
        <div className="card-header">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <h3 className="card-title">قائمة العملاء</h3>
            <button 
              className="btn btn-primary"
              onClick={() => setShowForm(!showForm)}
            >
              {showForm ? 'إلغاء' : 'إضافة عميل جديد'}
            </button>
          </div>
        </div>
        
        {showForm && (
          <div className="card-body">
            <form onSubmit={handleSubmit}>
              <div className="row">
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">اسم العميل *</label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="form-control"
                      required
                    />
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">رقم الهاتف</label>
                    <input
                      type="text"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="form-control"
                    />
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">البريد الإلكتروني</label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="form-control"
                    />
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="form-group">
                    <label className="form-label">الرقم الضريبي</label>
                    <input
                      type="text"
                      name="tax_number"
                      value={formData.tax_number}
                      onChange={handleInputChange}
                      className="form-control"
                    />
                  </div>
                </div>
                <div className="col">
                  <div className="form-group">
                    <label className="form-label">العنوان</label>
                    <textarea
                      name="address"
                      value={formData.address}
                      onChange={handleInputChange}
                      className="form-control"
                      rows="3"
                    ></textarea>
                  </div>
                </div>
              </div>
              <div style={{ display: 'flex', gap: '1rem' }}>
                <button type="submit" className="btn btn-success">
                  {editingCustomer ? 'تحديث العميل' : 'إضافة العميل'}
                </button>
                <button type="button" className="btn btn-danger" onClick={resetForm}>
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        )}
        
        <div className="card-body">
          {customers.length === 0 ? (
            <p>لا توجد عملاء مسجلين حتى الآن</p>
          ) : (
            <table className="table">
              <thead>
                <tr>
                  <th>الاسم</th>
                  <th>الهاتف</th>
                  <th>البريد الإلكتروني</th>
                  <th>الرقم الضريبي</th>
                  <th>الرصيد</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {customers.map(customer => (
                  <tr key={customer.id}>
                    <td>{customer.name}</td>
                    <td>{customer.phone || '-'}</td>
                    <td>{customer.email || '-'}</td>
                    <td>{customer.tax_number || '-'}</td>
                    <td>{customer.balance?.toLocaleString() || '0'} ج.م</td>
                    <td>
                      <button 
                        className="btn btn-warning"
                        onClick={() => handleEdit(customer)}
                        style={{ marginLeft: '0.5rem' }}
                      >
                        تعديل
                      </button>
                      <button 
                        className="btn btn-danger"
                        onClick={() => handleDelete(customer.id)}
                      >
                        حذف
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
};

export default Customers;
