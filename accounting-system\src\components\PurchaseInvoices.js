import React, { useState, useEffect } from 'react';
import axios from 'axios';

const PurchaseInvoices = () => {
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    fetchInvoices();
  }, []);

  const fetchInvoices = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:5000/api/purchase-invoices');
      setInvoices(response.data);
    } catch (error) {
      setError('خطأ في جلب فواتير المشتريات');
      console.error('Error fetching purchase invoices:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">جاري تحميل فواتير المشتريات...</div>
      </div>
    );
  }

  return (
    <div className="container">
      <h1 className="page-title">فواتير المشتريات</h1>
      
      {message && <div className="success">{message}</div>}
      {error && <div className="error">{error}</div>}
      
      <div className="card">
        <div className="card-header">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <h3 className="card-title">قائمة فواتير المشتريات</h3>
            <button className="btn btn-primary">
              إضافة فاتورة جديدة
            </button>
          </div>
        </div>
        
        <div className="card-body">
          {invoices.length === 0 ? (
            <p>لا توجد فواتير مشتريات حتى الآن</p>
          ) : (
            <table className="table">
              <thead>
                <tr>
                  <th>رقم الفاتورة</th>
                  <th>المورد</th>
                  <th>التاريخ</th>
                  <th>المبلغ الإجمالي</th>
                  <th>صافي المبلغ</th>
                  <th>حالة الدفع</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {invoices.map(invoice => (
                  <tr key={invoice.id}>
                    <td>{invoice.invoice_number}</td>
                    <td>{invoice.supplier_name || '-'}</td>
                    <td>{new Date(invoice.invoice_date).toLocaleDateString('ar-EG')}</td>
                    <td>{invoice.total_amount?.toLocaleString()} ج.م</td>
                    <td>{invoice.net_amount?.toLocaleString()} ج.م</td>
                    <td>
                      <span className={`badge ${invoice.payment_status === 'paid' ? 'badge-success' : 'badge-warning'}`}>
                        {invoice.payment_status === 'paid' ? 'مدفوعة' : 'معلقة'}
                      </span>
                    </td>
                    <td>
                      <button className="btn btn-primary" style={{ marginLeft: '0.5rem' }}>
                        عرض
                      </button>
                      <button className="btn btn-warning">
                        طباعة
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
};

export default PurchaseInvoices;
