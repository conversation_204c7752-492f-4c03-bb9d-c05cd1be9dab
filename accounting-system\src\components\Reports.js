import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  SalesVsPurchasesChart,
  ExpensesPieChart,
  ProfitTrendChart,
  TreasuryBalanceChart,
  TopCustomersChart,
  ProductPerformanceChart
} from './Charts';

const Reports = () => {
  const [reportData, setReportData] = useState({
    totalSales: 0,
    totalPurchases: 0,
    totalExpenses: 0,
    netProfit: 0,
    customersCount: 0,
    suppliersCount: 0,
    productsCount: 0,
    treasuryBalance: 0
  });
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: ''
  });
  const [error, setError] = useState('');

  useEffect(() => {
    fetchReportData();
  }, []);

  const fetchReportData = async () => {
    try {
      setLoading(true);
      
      // جلب البيانات من مختلف الـ APIs
      const [
        customersRes,
        suppliersRes,
        productsRes,
        salesRes,
        purchasesRes,
        expensesRes,
        treasuryRes
      ] = await Promise.all([
        axios.get('http://localhost:5000/api/customers'),
        axios.get('http://localhost:5000/api/suppliers'),
        axios.get('http://localhost:5000/api/products'),
        axios.get('http://localhost:5000/api/sales-invoices'),
        axios.get('http://localhost:5000/api/purchase-invoices'),
        axios.get('http://localhost:5000/api/expenses'),
        axios.get('http://localhost:5000/api/treasury-transactions')
      ]);

      // حساب الإحصائيات
      const totalSales = salesRes.data.reduce((sum, invoice) => sum + (invoice.net_amount || 0), 0);
      const totalPurchases = purchasesRes.data.reduce((sum, invoice) => sum + (invoice.net_amount || 0), 0);
      const totalExpenses = expensesRes.data.reduce((sum, expense) => sum + (expense.amount || 0), 0);
      
      const treasuryIncome = treasuryRes.data
        .filter(t => t.transaction_type === 'income')
        .reduce((sum, t) => sum + t.amount, 0);
      
      const treasuryExpense = treasuryRes.data
        .filter(t => t.transaction_type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0);

      setReportData({
        totalSales,
        totalPurchases,
        totalExpenses,
        netProfit: totalSales - totalPurchases - totalExpenses,
        customersCount: customersRes.data.length,
        suppliersCount: suppliersRes.data.length,
        productsCount: productsRes.data.length,
        treasuryBalance: treasuryIncome - treasuryExpense
      });
    } catch (error) {
      setError('خطأ في جلب بيانات التقارير');
      console.error('Error fetching report data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDateRangeChange = (e) => {
    setDateRange({
      ...dateRange,
      [e.target.name]: e.target.value
    });
  };

  const generateReport = () => {
    // هنا يمكن إضافة منطق تصفية البيانات حسب التاريخ
    fetchReportData();
  };

  const printReport = () => {
    window.print();
  };

  if (loading) {
    return (
      <div className="container">
        <div className="loading">جاري تحميل بيانات التقارير...</div>
      </div>
    );
  }

  return (
    <div className="container">
      <h1 className="page-title">التقارير المالية</h1>
      
      {error && <div className="error">{error}</div>}
      
      {/* فلاتر التقارير */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">فلاتر التقارير</h3>
        </div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-4">
              <div className="form-group">
                <label className="form-label">من تاريخ</label>
                <input
                  type="date"
                  name="startDate"
                  value={dateRange.startDate}
                  onChange={handleDateRangeChange}
                  className="form-control"
                />
              </div>
            </div>
            <div className="col-md-4">
              <div className="form-group">
                <label className="form-label">إلى تاريخ</label>
                <input
                  type="date"
                  name="endDate"
                  value={dateRange.endDate}
                  onChange={handleDateRangeChange}
                  className="form-control"
                />
              </div>
            </div>
            <div className="col-md-4">
              <div className="form-group">
                <label className="form-label">&nbsp;</label>
                <div>
                  <button className="btn btn-primary" onClick={generateReport} style={{ marginLeft: '1rem' }}>
                    تحديث التقرير
                  </button>
                  <button className="btn btn-success" onClick={printReport}>
                    طباعة التقرير
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* الإحصائيات الرئيسية */}
      <div className="dashboard-stats">
        <div className="stat-card" style={{ background: 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)' }}>
          <div className="stat-number">{reportData.totalSales.toLocaleString()}</div>
          <div className="stat-label">إجمالي المبيعات (ج.م)</div>
        </div>
        
        <div className="stat-card" style={{ background: 'linear-gradient(135deg, #f44336 0%, #da190b 100%)' }}>
          <div className="stat-number">{reportData.totalPurchases.toLocaleString()}</div>
          <div className="stat-label">إجمالي المشتريات (ج.م)</div>
        </div>
        
        <div className="stat-card" style={{ background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)' }}>
          <div className="stat-number">{reportData.totalExpenses.toLocaleString()}</div>
          <div className="stat-label">إجمالي المصاريف (ج.م)</div>
        </div>
        
        <div className="stat-card" style={{ 
          background: reportData.netProfit >= 0 
            ? 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)' 
            : 'linear-gradient(135deg, #f44336 0%, #da190b 100%)'
        }}>
          <div className="stat-number">{reportData.netProfit.toLocaleString()}</div>
          <div className="stat-label">صافي الربح (ج.م)</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-number">{reportData.treasuryBalance.toLocaleString()}</div>
          <div className="stat-label">رصيد الخزينة (ج.م)</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-number">{reportData.customersCount}</div>
          <div className="stat-label">عدد العملاء</div>
        </div>
      </div>

      {/* تفاصيل التقرير */}
      <div className="row">
        <div className="col-md-6">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">ملخص المبيعات والمشتريات</h3>
            </div>
            <div className="card-body">
              <table className="table">
                <tbody>
                  <tr>
                    <td><strong>إجمالي المبيعات</strong></td>
                    <td style={{ color: 'green' }}>{reportData.totalSales.toLocaleString()} ج.م</td>
                  </tr>
                  <tr>
                    <td><strong>إجمالي المشتريات</strong></td>
                    <td style={{ color: 'red' }}>{reportData.totalPurchases.toLocaleString()} ج.م</td>
                  </tr>
                  <tr>
                    <td><strong>إجمالي المصاريف</strong></td>
                    <td style={{ color: 'red' }}>{reportData.totalExpenses.toLocaleString()} ج.م</td>
                  </tr>
                  <tr style={{ borderTop: '2px solid #ddd' }}>
                    <td><strong>صافي الربح</strong></td>
                    <td style={{ 
                      color: reportData.netProfit >= 0 ? 'green' : 'red',
                      fontWeight: 'bold',
                      fontSize: '1.1rem'
                    }}>
                      {reportData.netProfit.toLocaleString()} ج.م
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        
        <div className="col-md-6">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">إحصائيات عامة</h3>
            </div>
            <div className="card-body">
              <table className="table">
                <tbody>
                  <tr>
                    <td><strong>عدد العملاء</strong></td>
                    <td>{reportData.customersCount}</td>
                  </tr>
                  <tr>
                    <td><strong>عدد الموردين</strong></td>
                    <td>{reportData.suppliersCount}</td>
                  </tr>
                  <tr>
                    <td><strong>عدد المنتجات</strong></td>
                    <td>{reportData.productsCount}</td>
                  </tr>
                  <tr>
                    <td><strong>رصيد الخزينة</strong></td>
                    <td style={{ 
                      color: reportData.treasuryBalance >= 0 ? 'green' : 'red',
                      fontWeight: 'bold'
                    }}>
                      {reportData.treasuryBalance.toLocaleString()} ج.م
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* الرسوم البيانية */}
      <div className="row">
        <div className="col-md-6">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">مقارنة المبيعات والمشتريات</h3>
            </div>
            <div className="card-body">
              <SalesVsPurchasesChart
                salesData={[reportData.totalSales/12, reportData.totalSales/12, reportData.totalSales/12, reportData.totalSales/12, reportData.totalSales/12, reportData.totalSales/12, reportData.totalSales/12, reportData.totalSales/12, reportData.totalSales/12, reportData.totalSales/12, reportData.totalSales/12, reportData.totalSales/12]}
                purchasesData={[reportData.totalPurchases/12, reportData.totalPurchases/12, reportData.totalPurchases/12, reportData.totalPurchases/12, reportData.totalPurchases/12, reportData.totalPurchases/12, reportData.totalPurchases/12, reportData.totalPurchases/12, reportData.totalPurchases/12, reportData.totalPurchases/12, reportData.totalPurchases/12, reportData.totalPurchases/12]}
              />
            </div>
          </div>
        </div>

        <div className="col-md-6">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">توزيع المصاريف</h3>
            </div>
            <div className="card-body">
              <ExpensesPieChart
                expensesData={{
                  labels: ['مصاريف إدارية', 'مصاريف تشغيلية', 'مصاريف تسويق', 'أخرى'],
                  data: [reportData.totalExpenses * 0.4, reportData.totalExpenses * 0.3, reportData.totalExpenses * 0.2, reportData.totalExpenses * 0.1]
                }}
              />
            </div>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-md-6">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">اتجاه الأرباح</h3>
            </div>
            <div className="card-body">
              <ProfitTrendChart
                profitData={[reportData.netProfit/12, reportData.netProfit/12, reportData.netProfit/12, reportData.netProfit/12, reportData.netProfit/12, reportData.netProfit/12, reportData.netProfit/12, reportData.netProfit/12, reportData.netProfit/12, reportData.netProfit/12, reportData.netProfit/12, reportData.netProfit/12]}
              />
            </div>
          </div>
        </div>

        <div className="col-md-6">
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">رصيد الخزينة</h3>
            </div>
            <div className="card-body">
              <TreasuryBalanceChart
                treasuryData={{
                  labels: ['الأسبوع الأول', 'الأسبوع الثاني', 'الأسبوع الثالث', 'الأسبوع الرابع'],
                  data: [reportData.treasuryBalance * 0.8, reportData.treasuryBalance * 0.9, reportData.treasuryBalance * 0.95, reportData.treasuryBalance]
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* نصائح وتوصيات */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">نصائح وتوصيات</h3>
        </div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-6">
              <h5>📈 تحليل الأداء:</h5>
              <ul>
                <li>
                  {reportData.netProfit >= 0
                    ? '✅ الشركة تحقق أرباحاً جيدة'
                    : '⚠️ الشركة تواجه خسائر - يجب مراجعة المصاريف'}
                </li>
                <li>
                  {reportData.totalSales > reportData.totalPurchases
                    ? '✅ هامش ربح إيجابي من المبيعات'
                    : '⚠️ تكلفة المشتريات عالية مقارنة بالمبيعات'}
                </li>
                <li>
                  {reportData.treasuryBalance > 0
                    ? '✅ السيولة النقدية متاحة'
                    : '⚠️ نقص في السيولة النقدية'}
                </li>
              </ul>
            </div>
            <div className="col-md-6">
              <h5>💡 توصيات:</h5>
              <ul>
                <li>مراجعة المصاريف الشهرية وتحسين الكفاءة</li>
                <li>تحليل أداء المنتجات الأكثر ربحية</li>
                <li>متابعة مستحقات العملاء بانتظام</li>
                <li>تحسين إدارة المخزون</li>
                <li>إنشاء خطة تسويقية لزيادة المبيعات</li>
                <li>تحسين علاقات الموردين للحصول على أسعار أفضل</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Reports;
