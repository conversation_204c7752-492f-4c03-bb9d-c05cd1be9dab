import { IPropertyIdentValueDescriptor } from '../IPropertyDescriptor';
export declare const enum LIST_STYLE_TYPE {
    NONE = -1,
    DISC = 0,
    CIRCLE = 1,
    SQUARE = 2,
    DECIMAL = 3,
    CJK_DECIMAL = 4,
    DECIMAL_LEADING_ZERO = 5,
    <PERSON><PERSON><PERSON>_ROMAN = 6,
    UPPER_ROMAN = 7,
    LOWER_GREEK = 8,
    LOWER_ALPHA = 9,
    UPPER_ALPHA = 10,
    ARABIC_INDIC = 11,
    ARMENIAN = 12,
    BENGALI = 13,
    CAMBODIAN = 14,
    CJK_EARTHLY_BRANCH = 15,
    <PERSON><PERSON><PERSON>_HEAVENLY_STEM = 16,
    CJK_IDEOGRAPHIC = 17,
    DEVANAGARI = 18,
    ETHIOPIC_NUMERIC = 19,
    GEORGIAN = 20,
    GUJARATI = 21,
    GURMUKHI = 22,
    HEBREW = 22,
    HIRAGANA = 23,
    HIRAGANA_IROHA = 24,
    JAPANESE_FORMAL = 25,
    JAPANESE_INFORMAL = 26,
    KANNADA = 27,
    KATAKANA = 28,
    <PERSON><PERSON>AKANA_IROHA = 29,
    KHMER = 30,
    <PERSON><PERSON>EA<PERSON>_<PERSON>ANGUL_FORMAL = 31,
    KOREAN_HANJA_FORMAL = 32,
    KOREAN_HANJA_INFORMAL = 33,
    LAO = 34,
    LOWER_ARMENIAN = 35,
    MALAYALAM = 36,
    MONGOLIAN = 37,
    MYANMAR = 38,
    ORIYA = 39,
    PERSIAN = 40,
    SIMP_CHINESE_FORMAL = 41,
    SIMP_CHINESE_INFORMAL = 42,
    TAMIL = 43,
    TELUGU = 44,
    THAI = 45,
    TIBETAN = 46,
    TRAD_CHINESE_FORMAL = 47,
    TRAD_CHINESE_INFORMAL = 48,
    UPPER_ARMENIAN = 49,
    DISCLOSURE_OPEN = 50,
    DISCLOSURE_CLOSED = 51
}
export declare const listStyleType: IPropertyIdentValueDescriptor<LIST_STYLE_TYPE>;
