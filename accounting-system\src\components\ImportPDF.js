import React, { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import axios from 'axios';

const ImportPDF = ({ type, onClose, onImportComplete }) => {
  const [file, setFile] = useState(null);
  const [extractedText, setExtractedText] = useState('');
  const [parsedData, setParsedData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1); // 1: Upload, 2: Extract, 3: Parse, 4: Import
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const onDrop = (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (file) {
      setFile(file);
      extractTextFromPDF(file);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    multiple: false
  });

  const extractTextFromPDF = async (file) => {
    try {
      setLoading(true);
      setStep(2);
      
      const formData = new FormData();
      formData.append('pdf', file);
      formData.append('type', type);
      
      const response = await axios.post('http://localhost:5000/api/extract-pdf', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      setExtractedText(response.data.text);
      parseExtractedData(response.data.text);
    } catch (error) {
      setError('خطأ في استخراج النص من PDF: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const parseExtractedData = (text) => {
    setStep(3);
    
    try {
      let parsed = [];
      
      switch (type) {
        case 'customers':
          parsed = parseCustomersFromText(text);
          break;
        case 'suppliers':
          parsed = parseSuppliersFromText(text);
          break;
        case 'products':
          parsed = parseProductsFromText(text);
          break;
        case 'invoices':
          parsed = parseInvoicesFromText(text);
          break;
        default:
          parsed = parseGenericFromText(text);
      }
      
      setParsedData(parsed);
    } catch (error) {
      setError('خطأ في تحليل البيانات: ' + error.message);
    }
  };

  const parseCustomersFromText = (text) => {
    const customers = [];
    const lines = text.split('\n');
    
    // البحث عن أنماط العملاء
    const patterns = {
      name: /اسم|عميل|customer|name/i,
      phone: /هاتف|تليفون|phone|mobile|tel/i,
      email: /بريد|email|mail/i,
      address: /عنوان|address/i
    };
    
    let currentCustomer = {};
    
    lines.forEach(line => {
      line = line.trim();
      if (!line) return;
      
      // البحث عن أرقام الهواتف
      const phoneMatch = line.match(/(\+?\d{10,15})/);
      if (phoneMatch) {
        currentCustomer.phone = phoneMatch[1];
      }
      
      // البحث عن البريد الإلكتروني
      const emailMatch = line.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
      if (emailMatch) {
        currentCustomer.email = emailMatch[1];
      }
      
      // إذا كان السطر يحتوي على اسم محتمل
      if (line.length > 3 && line.length < 50 && !phoneMatch && !emailMatch) {
        if (currentCustomer.name) {
          customers.push({ ...currentCustomer });
          currentCustomer = {};
        }
        currentCustomer.name = line;
      }
    });
    
    if (currentCustomer.name) {
      customers.push(currentCustomer);
    }
    
    return customers;
  };

  const parseSuppliersFromText = (text) => {
    // نفس منطق العملاء مع تعديلات للموردين
    return parseCustomersFromText(text);
  };

  const parseProductsFromText = (text) => {
    const products = [];
    const lines = text.split('\n');
    
    lines.forEach(line => {
      line = line.trim();
      if (!line) return;
      
      // البحث عن أسعار
      const priceMatch = line.match(/(\d+\.?\d*)\s*(ج\.م|جنيه|ريال|درهم|دولار)/i);
      if (priceMatch) {
        const price = parseFloat(priceMatch[1]);
        const productName = line.replace(priceMatch[0], '').trim();
        
        if (productName) {
          products.push({
            name: productName,
            price: price,
            cost: price * 0.8, // تقدير تكلفة 80% من السعر
            stock_quantity: 0
          });
        }
      }
    });
    
    return products;
  };

  const parseInvoicesFromText = (text) => {
    const invoices = [];
    const lines = text.split('\n');
    
    let currentInvoice = {};
    let items = [];
    
    lines.forEach(line => {
      line = line.trim();
      if (!line) return;
      
      // البحث عن رقم الفاتورة
      const invoiceMatch = line.match(/فاتورة|invoice|رقم|number.*?(\d+)/i);
      if (invoiceMatch) {
        currentInvoice.invoice_number = invoiceMatch[1];
      }
      
      // البحث عن التاريخ
      const dateMatch = line.match(/(\d{1,2}\/\d{1,2}\/\d{4})/);
      if (dateMatch) {
        currentInvoice.invoice_date = dateMatch[1];
      }
      
      // البحث عن المجموع
      const totalMatch = line.match(/مجموع|total|إجمالي.*?(\d+\.?\d*)/i);
      if (totalMatch) {
        currentInvoice.total_amount = parseFloat(totalMatch[1]);
      }
    });
    
    if (currentInvoice.invoice_number) {
      invoices.push(currentInvoice);
    }
    
    return invoices;
  };

  const parseGenericFromText = (text) => {
    // تحليل عام للنصوص
    const lines = text.split('\n').filter(line => line.trim());
    return lines.map((line, index) => ({
      id: index + 1,
      content: line.trim()
    }));
  };

  const importData = async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await axios.post(`http://localhost:5000/api/import/${type}`, {
        data: parsedData
      });

      setMessage(`تم استيراد ${response.data.imported} عنصر بنجاح من PDF`);
      setStep(4);
      
      if (onImportComplete) {
        onImportComplete();
      }
    } catch (error) {
      setError('خطأ في استيراد البيانات: ' + (error.response?.data?.error || error.message));
    } finally {
      setLoading(false);
    }
  };

  const getTypeTitle = () => {
    const titles = {
      customers: 'العملاء',
      suppliers: 'الموردين',
      products: 'المنتجات',
      invoices: 'الفواتير'
    };
    return titles[type] || type;
  };

  return (
    <div className="modal-overlay" style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.5)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000
    }}>
      <div className="modal-content" style={{
        backgroundColor: 'white',
        borderRadius: '10px',
        padding: '2rem',
        maxWidth: '90vw',
        maxHeight: '90vh',
        overflow: 'auto',
        width: '800px'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
          <h2>📄 استيراد {getTypeTitle()} من PDF</h2>
          <button onClick={onClose} className="btn btn-danger">✕</button>
        </div>

        {message && <div className="success">{message}</div>}
        {error && <div className="error">{error}</div>}

        {/* مؤشر التقدم */}
        <div style={{ marginBottom: '2rem' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '1rem' }}>
            {['رفع PDF', 'استخراج النص', 'تحليل البيانات', 'الاستيراد'].map((stepName, index) => (
              <div key={index} style={{
                padding: '0.5rem 1rem',
                borderRadius: '20px',
                backgroundColor: step > index ? '#4CAF50' : step === index + 1 ? '#2196F3' : '#f0f0f0',
                color: step >= index + 1 ? 'white' : '#666',
                fontSize: '0.9rem'
              }}>
                {index + 1}. {stepName}
              </div>
            ))}
          </div>
        </div>

        {/* الخطوة 1: رفع الملف */}
        {step === 1 && (
          <div>
            <div
              {...getRootProps()}
              style={{
                border: '2px dashed #ccc',
                borderRadius: '10px',
                padding: '3rem',
                textAlign: 'center',
                cursor: 'pointer',
                backgroundColor: isDragActive ? '#f0f8ff' : '#fafafa'
              }}
            >
              <input {...getInputProps()} />
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📄</div>
              {isDragActive ? (
                <p>اسحب ملف PDF هنا...</p>
              ) : (
                <div>
                  <p>اسحب ملف PDF هنا أو اضغط للاختيار</p>
                  <p style={{ color: '#666', fontSize: '0.9rem' }}>
                    سيتم استخراج النص وتحليله تلقائياً
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* الخطوة 2: استخراج النص */}
        {step === 2 && (
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '3rem', marginBottom: '2rem' }}>⏳</div>
            <h3>جاري استخراج النص من PDF...</h3>
            <p style={{ color: '#666' }}>يرجى الانتظار</p>
          </div>
        )}

        {/* الخطوة 3: تحليل البيانات */}
        {step === 3 && (
          <div>
            <h3>البيانات المستخرجة</h3>
            
            <div style={{ marginBottom: '2rem' }}>
              <h4>النص المستخرج:</h4>
              <textarea
                value={extractedText}
                onChange={(e) => setExtractedText(e.target.value)}
                style={{
                  width: '100%',
                  height: '200px',
                  padding: '1rem',
                  border: '1px solid #ddd',
                  borderRadius: '5px',
                  fontSize: '0.9rem'
                }}
              />
            </div>

            <div style={{ marginBottom: '2rem' }}>
              <h4>البيانات المحللة ({parsedData.length} عنصر):</h4>
              <div style={{ maxHeight: '200px', overflow: 'auto', border: '1px solid #ddd', padding: '1rem' }}>
                {parsedData.length > 0 ? (
                  <pre style={{ fontSize: '0.8rem' }}>{JSON.stringify(parsedData, null, 2)}</pre>
                ) : (
                  <p style={{ color: '#666' }}>لم يتم العثور على بيانات قابلة للتحليل</p>
                )}
              </div>
            </div>

            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
              <button onClick={() => parseExtractedData(extractedText)} className="btn btn-warning">
                إعادة التحليل
              </button>
              <button 
                onClick={importData} 
                className="btn btn-success" 
                disabled={loading || parsedData.length === 0}
              >
                {loading ? 'جاري الاستيراد...' : 'تأكيد الاستيراد'}
              </button>
            </div>
          </div>
        )}

        {/* الخطوة 4: اكتمال الاستيراد */}
        {step === 4 && (
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '4rem', color: '#4CAF50', marginBottom: '2rem' }}>✅</div>
            <h3>تم الاستيراد بنجاح!</h3>
            <p style={{ color: '#666', marginBottom: '2rem' }}>{message}</p>
            
            <button onClick={onClose} className="btn btn-primary">
              إغلاق
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImportPDF;
