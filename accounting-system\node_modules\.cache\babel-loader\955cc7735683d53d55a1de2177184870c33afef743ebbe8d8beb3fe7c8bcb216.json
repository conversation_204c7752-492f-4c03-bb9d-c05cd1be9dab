{"ast": null, "code": "import * as XLSX from 'xlsx';\nexport const generateCustomersTemplate = () => {\n  const headers = ['اسم العميل', 'رقم الهاتف', 'البريد الإلكتروني', 'العنوان', 'الرقم الضريبي'];\n  const sampleData = [['أحمد محمد علي', '***********', '<EMAIL>', 'القاهرة، مصر', '*********'], ['فاطمة أحمد', '***********', '<EMAIL>', 'الإسكندرية، مصر', '*********'], ['محمد حسن', '01*********', '<EMAIL>', 'الجيزة، مصر', '*********']];\n  const data = [headers, ...sampleData];\n  const ws = XLSX.utils.aoa_to_sheet(data);\n\n  // تنسيق الأعمدة\n  const colWidths = [{\n    wch: 20\n  },\n  // اسم العميل\n  {\n    wch: 15\n  },\n  // رقم الهاتف\n  {\n    wch: 25\n  },\n  // البريد الإلكتروني\n  {\n    wch: 30\n  },\n  // العنوان\n  {\n    wch: 15\n  } // الرقم الضريبي\n  ];\n  ws['!cols'] = colWidths;\n\n  // تنسيق الرأس\n  const headerStyle = {\n    font: {\n      bold: true,\n      color: {\n        rgb: \"FFFFFF\"\n      }\n    },\n    fill: {\n      fgColor: {\n        rgb: \"4472C4\"\n      }\n    },\n    alignment: {\n      horizontal: \"center\"\n    }\n  };\n\n  // تطبيق التنسيق على الرأس\n  for (let i = 0; i < headers.length; i++) {\n    const cellRef = XLSX.utils.encode_cell({\n      r: 0,\n      c: i\n    });\n    if (!ws[cellRef]) ws[cellRef] = {};\n    ws[cellRef].s = headerStyle;\n  }\n  const wb = XLSX.utils.book_new();\n  XLSX.utils.book_append_sheet(wb, ws, 'العملاء');\n\n  // إضافة ورقة التعليمات\n  const instructionsData = [['تعليمات استيراد العملاء'], [''], ['1. املأ البيانات في ورقة \"العملاء\"'], ['2. اسم العميل مطلوب'], ['3. باقي الحقول اختيارية'], ['4. احفظ الملف بصيغة Excel (.xlsx)'], ['5. ارفع الملف في نظام المحاسبة'], [''], ['ملاحظات:'], ['- تأكد من صحة أرقام الهواتف'], ['- تأكد من صحة عناوين البريد الإلكتروني'], ['- يمكن ترك الحقول فارغة إذا لم تكن متوفرة']];\n  const instructionsWs = XLSX.utils.aoa_to_sheet(instructionsData);\n  instructionsWs['!cols'] = [{\n    wch: 50\n  }];\n  XLSX.utils.book_append_sheet(wb, instructionsWs, 'التعليمات');\n  return wb;\n};\nexport const generateSuppliersTemplate = () => {\n  const headers = ['اسم المورد', 'رقم الهاتف', 'البريد الإلكتروني', 'العنوان', 'الرقم الضريبي'];\n  const sampleData = [['شركة التوريدات المتقدمة', '0223456789', '<EMAIL>', 'القاهرة الجديدة، مصر', '111222333'], ['مؤسسة الخدمات التجارية', '0212345678', '<EMAIL>', 'المعادي، القاهرة', '444555666'], ['شركة المواد الخام', '0201234567', '<EMAIL>', 'مدينة نصر، القاهرة', '777888999']];\n  const data = [headers, ...sampleData];\n  const ws = XLSX.utils.aoa_to_sheet(data);\n  ws['!cols'] = [{\n    wch: 25\n  }, {\n    wch: 15\n  }, {\n    wch: 25\n  }, {\n    wch: 30\n  }, {\n    wch: 15\n  }];\n  const wb = XLSX.utils.book_new();\n  XLSX.utils.book_append_sheet(wb, ws, 'الموردين');\n  return wb;\n};\nexport const generateProductsTemplate = () => {\n  const headers = ['اسم المنتج', 'الوصف', 'الوحدة', 'سعر البيع', 'سعر التكلفة', 'الكمية المتاحة'];\n  const sampleData = [['لابتوب Dell', 'لابتوب Dell Inspiron 15', 'قطعة', '15000', '12000', '10'], ['طابعة HP', 'طابعة HP LaserJet', 'قطعة', '3000', '2500', '5'], ['ورق A4', 'ورق طباعة A4 أبيض', 'رزمة', '50', '40', '100'], ['قلم حبر جاف', 'قلم حبر جاف أزرق', 'قطعة', '2', '1.5', '500']];\n  const data = [headers, ...sampleData];\n  const ws = XLSX.utils.aoa_to_sheet(data);\n  ws['!cols'] = [{\n    wch: 20\n  }, {\n    wch: 30\n  }, {\n    wch: 10\n  }, {\n    wch: 12\n  }, {\n    wch: 12\n  }, {\n    wch: 15\n  }];\n  const wb = XLSX.utils.book_new();\n  XLSX.utils.book_append_sheet(wb, ws, 'المنتجات');\n\n  // إضافة ورقة التعليمات\n  const instructionsData = [['تعليمات استيراد المنتجات'], [''], ['1. املأ البيانات في ورقة \"المنتجات\"'], ['2. اسم المنتج مطلوب'], ['3. الأسعار يجب أن تكون أرقام'], ['4. الكمية يجب أن تكون رقم صحيح'], [''], ['أمثلة على الوحدات:'], ['- قطعة، كيلو، متر، لتر، رزمة، علبة، كرتونة'], [''], ['ملاحظات:'], ['- تأكد من صحة الأسعار'], ['- سعر التكلفة يجب أن يكون أقل من سعر البيع'], ['- الكمية المتاحة تمثل المخزون الحالي']];\n  const instructionsWs = XLSX.utils.aoa_to_sheet(instructionsData);\n  instructionsWs['!cols'] = [{\n    wch: 50\n  }];\n  XLSX.utils.book_append_sheet(wb, instructionsWs, 'التعليمات');\n  return wb;\n};\nexport const generateExpensesTemplate = () => {\n  const headers = ['فئة المصروف', 'الوصف', 'المبلغ', 'تاريخ المصروف', 'طريقة الدفع', 'رقم الإيصال', 'ملاحظات'];\n  const sampleData = [['مصاريف إدارية', 'فاتورة كهرباء', '500', '2024-01-15', 'نقدي', 'R001', 'فاتورة شهر يناير'], ['مصاريف تشغيلية', 'صيانة أجهزة', '1200', '2024-01-16', 'شيك', 'R002', 'صيانة دورية'], ['مصاريف تسويق', 'إعلانات فيسبوك', '800', '2024-01-17', 'تحويل بنكي', 'R003', 'حملة إعلانية'], ['مصاريف مواصلات', 'وقود السيارات', '300', '2024-01-18', 'نقدي', 'R004', 'وقود الأسبوع']];\n  const data = [headers, ...sampleData];\n  const ws = XLSX.utils.aoa_to_sheet(data);\n  ws['!cols'] = [{\n    wch: 18\n  }, {\n    wch: 25\n  }, {\n    wch: 12\n  }, {\n    wch: 15\n  }, {\n    wch: 15\n  }, {\n    wch: 15\n  }, {\n    wch: 30\n  }];\n  const wb = XLSX.utils.book_new();\n  XLSX.utils.book_append_sheet(wb, ws, 'المصاريف');\n\n  // إضافة ورقة فئات المصاريف\n  const categoriesData = [['فئات المصاريف المتاحة'], [''], ['مصاريف إدارية'], ['مصاريف تشغيلية'], ['مصاريف تسويق'], ['مصاريف صيانة'], ['مصاريف مواصلات'], ['مصاريف اتصالات'], ['مصاريف كهرباء'], ['مصاريف إيجار'], ['مصاريف أخرى'], [''], ['طرق الدفع المتاحة:'], [''], ['نقدي'], ['شيك'], ['تحويل بنكي'], ['بطاقة ائتمان']];\n  const categoriesWs = XLSX.utils.aoa_to_sheet(categoriesData);\n  categoriesWs['!cols'] = [{\n    wch: 30\n  }];\n  XLSX.utils.book_append_sheet(wb, categoriesWs, 'الفئات المتاحة');\n  return wb;\n};\nexport const downloadTemplate = type => {\n  let workbook;\n  let filename;\n  switch (type) {\n    case 'customers':\n      workbook = generateCustomersTemplate();\n      filename = 'قالب_العملاء.xlsx';\n      break;\n    case 'suppliers':\n      workbook = generateSuppliersTemplate();\n      filename = 'قالب_الموردين.xlsx';\n      break;\n    case 'products':\n      workbook = generateProductsTemplate();\n      filename = 'قالب_المنتجات.xlsx';\n      break;\n    case 'expenses':\n      workbook = generateExpensesTemplate();\n      filename = 'قالب_المصاريف.xlsx';\n      break;\n    default:\n      console.error('نوع القالب غير مدعوم:', type);\n      return;\n  }\n  XLSX.writeFile(workbook, filename);\n};\n\n// دالة لإنشاء قالب شامل يحتوي على جميع الأقسام\nexport const generateCompleteTemplate = () => {\n  const wb = XLSX.utils.book_new();\n\n  // إضافة ورقة العملاء\n  const customersWb = generateCustomersTemplate();\n  const customersWs = customersWb.Sheets['العملاء'];\n  XLSX.utils.book_append_sheet(wb, customersWs, 'العملاء');\n\n  // إضافة ورقة الموردين\n  const suppliersWb = generateSuppliersTemplate();\n  const suppliersWs = suppliersWb.Sheets['الموردين'];\n  XLSX.utils.book_append_sheet(wb, suppliersWs, 'الموردين');\n\n  // إضافة ورقة المنتجات\n  const productsWb = generateProductsTemplate();\n  const productsWs = productsWb.Sheets['المنتجات'];\n  XLSX.utils.book_append_sheet(wb, productsWs, 'المنتجات');\n\n  // إضافة ورقة المصاريف\n  const expensesWb = generateExpensesTemplate();\n  const expensesWs = expensesWb.Sheets['المصاريف'];\n  XLSX.utils.book_append_sheet(wb, expensesWs, 'المصاريف');\n\n  // إضافة ورقة التعليمات العامة\n  const generalInstructionsData = [['دليل استخدام قوالب النظام المحاسبي'], [''], ['مرحباً بك في النظام المحاسبي المتكامل'], [''], ['هذا الملف يحتوي على قوالب لاستيراد البيانات:'], [''], ['1. ورقة العملاء - لاستيراد بيانات العملاء'], ['2. ورقة الموردين - لاستيراد بيانات الموردين'], ['3. ورقة المنتجات - لاستيراد بيانات المنتجات'], ['4. ورقة المصاريف - لاستيراد بيانات المصاريف'], [''], ['تعليمات عامة:'], [''], ['• لا تغير أسماء الأعمدة في الصف الأول'], ['• املأ البيانات بدءاً من الصف الثاني'], ['• احذف البيانات التجريبية قبل إدخال بياناتك'], ['• تأكد من صحة التواريخ (DD/MM/YYYY)'], ['• تأكد من صحة الأرقام (بدون فواصل أو رموز)'], ['• احفظ الملف بصيغة Excel (.xlsx)'], [''], ['للدعم الفني: <EMAIL>'], ['الموقع الإلكتروني: www.accounting-system.com']];\n  const generalInstructionsWs = XLSX.utils.aoa_to_sheet(generalInstructionsData);\n  generalInstructionsWs['!cols'] = [{\n    wch: 60\n  }];\n\n  // إدراج ورقة التعليمات في البداية\n  const sheets = wb.SheetNames;\n  wb.SheetNames = ['التعليمات العامة', ...sheets];\n  wb.Sheets['التعليمات العامة'] = generalInstructionsWs;\n  return wb;\n};\nexport const downloadCompleteTemplate = () => {\n  const workbook = generateCompleteTemplate();\n  XLSX.writeFile(workbook, 'قوالب_النظام_المحاسبي_الشاملة.xlsx');\n};", "map": {"version": 3, "names": ["XLSX", "generateCustomersTemplate", "headers", "sampleData", "data", "ws", "utils", "aoa_to_sheet", "col<PERSON><PERSON><PERSON>", "wch", "headerStyle", "font", "bold", "color", "rgb", "fill", "fgColor", "alignment", "horizontal", "i", "length", "cellRef", "encode_cell", "r", "c", "s", "wb", "book_new", "book_append_sheet", "instructionsData", "instructionsWs", "generateSuppliersTemplate", "generateProductsTemplate", "generateExpensesTemplate", "categoriesData", "categoriesWs", "downloadTemplate", "type", "workbook", "filename", "console", "error", "writeFile", "generateCompleteTemplate", "customersWb", "customersWs", "Sheets", "suppliersWb", "suppliersWs", "productsWb", "productsWs", "expensesWb", "expensesWs", "generalInstructionsData", "generalInstructionsWs", "sheets", "SheetNames", "downloadCompleteTemplate"], "sources": ["F:/القيمة المضافة/accounting-system/src/components/TemplateGenerator.js"], "sourcesContent": ["import * as XLSX from 'xlsx';\n\nexport const generateCustomersTemplate = () => {\n  const headers = [\n    'اسم العميل',\n    'رقم الهاتف',\n    'البريد الإلكتروني',\n    'العنوان',\n    'الرقم الضريبي'\n  ];\n  \n  const sampleData = [\n    ['أحمد محمد علي', '***********', '<EMAIL>', 'القاهرة، مصر', '*********'],\n    ['فاطمة أحمد', '***********', '<EMAIL>', 'الإسكندرية، مصر', '*********'],\n    ['محمد حسن', '01*********', '<EMAIL>', 'الجيزة، مصر', '*********']\n  ];\n  \n  const data = [headers, ...sampleData];\n  \n  const ws = XLSX.utils.aoa_to_sheet(data);\n  \n  // تنسيق الأعمدة\n  const colWidths = [\n    { wch: 20 }, // اسم العميل\n    { wch: 15 }, // رقم الهاتف\n    { wch: 25 }, // البريد الإلكتروني\n    { wch: 30 }, // العنوان\n    { wch: 15 }  // الرقم الضريبي\n  ];\n  ws['!cols'] = colWidths;\n  \n  // تنسيق الرأس\n  const headerStyle = {\n    font: { bold: true, color: { rgb: \"FFFFFF\" } },\n    fill: { fgColor: { rgb: \"4472C4\" } },\n    alignment: { horizontal: \"center\" }\n  };\n  \n  // تطبيق التنسيق على الرأس\n  for (let i = 0; i < headers.length; i++) {\n    const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });\n    if (!ws[cellRef]) ws[cellRef] = {};\n    ws[cellRef].s = headerStyle;\n  }\n  \n  const wb = XLSX.utils.book_new();\n  XLSX.utils.book_append_sheet(wb, ws, 'العملاء');\n  \n  // إضافة ورقة التعليمات\n  const instructionsData = [\n    ['تعليمات استيراد العملاء'],\n    [''],\n    ['1. املأ البيانات في ورقة \"العملاء\"'],\n    ['2. اسم العميل مطلوب'],\n    ['3. باقي الحقول اختيارية'],\n    ['4. احفظ الملف بصيغة Excel (.xlsx)'],\n    ['5. ارفع الملف في نظام المحاسبة'],\n    [''],\n    ['ملاحظات:'],\n    ['- تأكد من صحة أرقام الهواتف'],\n    ['- تأكد من صحة عناوين البريد الإلكتروني'],\n    ['- يمكن ترك الحقول فارغة إذا لم تكن متوفرة']\n  ];\n  \n  const instructionsWs = XLSX.utils.aoa_to_sheet(instructionsData);\n  instructionsWs['!cols'] = [{ wch: 50 }];\n  \n  XLSX.utils.book_append_sheet(wb, instructionsWs, 'التعليمات');\n  \n  return wb;\n};\n\nexport const generateSuppliersTemplate = () => {\n  const headers = [\n    'اسم المورد',\n    'رقم الهاتف',\n    'البريد الإلكتروني',\n    'العنوان',\n    'الرقم الضريبي'\n  ];\n  \n  const sampleData = [\n    ['شركة التوريدات المتقدمة', '0223456789', '<EMAIL>', 'القاهرة الجديدة، مصر', '111222333'],\n    ['مؤسسة الخدمات التجارية', '0212345678', '<EMAIL>', 'المعادي، القاهرة', '444555666'],\n    ['شركة المواد الخام', '0201234567', '<EMAIL>', 'مدينة نصر، القاهرة', '777888999']\n  ];\n  \n  const data = [headers, ...sampleData];\n  const ws = XLSX.utils.aoa_to_sheet(data);\n  \n  ws['!cols'] = [\n    { wch: 25 }, { wch: 15 }, { wch: 25 }, { wch: 30 }, { wch: 15 }\n  ];\n  \n  const wb = XLSX.utils.book_new();\n  XLSX.utils.book_append_sheet(wb, ws, 'الموردين');\n  \n  return wb;\n};\n\nexport const generateProductsTemplate = () => {\n  const headers = [\n    'اسم المنتج',\n    'الوصف',\n    'الوحدة',\n    'سعر البيع',\n    'سعر التكلفة',\n    'الكمية المتاحة'\n  ];\n  \n  const sampleData = [\n    ['لابتوب Dell', 'لابتوب Dell Inspiron 15', 'قطعة', '15000', '12000', '10'],\n    ['طابعة HP', 'طابعة HP LaserJet', 'قطعة', '3000', '2500', '5'],\n    ['ورق A4', 'ورق طباعة A4 أبيض', 'رزمة', '50', '40', '100'],\n    ['قلم حبر جاف', 'قلم حبر جاف أزرق', 'قطعة', '2', '1.5', '500']\n  ];\n  \n  const data = [headers, ...sampleData];\n  const ws = XLSX.utils.aoa_to_sheet(data);\n  \n  ws['!cols'] = [\n    { wch: 20 }, { wch: 30 }, { wch: 10 }, { wch: 12 }, { wch: 12 }, { wch: 15 }\n  ];\n  \n  const wb = XLSX.utils.book_new();\n  XLSX.utils.book_append_sheet(wb, ws, 'المنتجات');\n  \n  // إضافة ورقة التعليمات\n  const instructionsData = [\n    ['تعليمات استيراد المنتجات'],\n    [''],\n    ['1. املأ البيانات في ورقة \"المنتجات\"'],\n    ['2. اسم المنتج مطلوب'],\n    ['3. الأسعار يجب أن تكون أرقام'],\n    ['4. الكمية يجب أن تكون رقم صحيح'],\n    [''],\n    ['أمثلة على الوحدات:'],\n    ['- قطعة، كيلو، متر، لتر، رزمة، علبة، كرتونة'],\n    [''],\n    ['ملاحظات:'],\n    ['- تأكد من صحة الأسعار'],\n    ['- سعر التكلفة يجب أن يكون أقل من سعر البيع'],\n    ['- الكمية المتاحة تمثل المخزون الحالي']\n  ];\n  \n  const instructionsWs = XLSX.utils.aoa_to_sheet(instructionsData);\n  instructionsWs['!cols'] = [{ wch: 50 }];\n  \n  XLSX.utils.book_append_sheet(wb, instructionsWs, 'التعليمات');\n  \n  return wb;\n};\n\nexport const generateExpensesTemplate = () => {\n  const headers = [\n    'فئة المصروف',\n    'الوصف',\n    'المبلغ',\n    'تاريخ المصروف',\n    'طريقة الدفع',\n    'رقم الإيصال',\n    'ملاحظات'\n  ];\n  \n  const sampleData = [\n    ['مصاريف إدارية', 'فاتورة كهرباء', '500', '2024-01-15', 'نقدي', 'R001', 'فاتورة شهر يناير'],\n    ['مصاريف تشغيلية', 'صيانة أجهزة', '1200', '2024-01-16', 'شيك', 'R002', 'صيانة دورية'],\n    ['مصاريف تسويق', 'إعلانات فيسبوك', '800', '2024-01-17', 'تحويل بنكي', 'R003', 'حملة إعلانية'],\n    ['مصاريف مواصلات', 'وقود السيارات', '300', '2024-01-18', 'نقدي', 'R004', 'وقود الأسبوع']\n  ];\n  \n  const data = [headers, ...sampleData];\n  const ws = XLSX.utils.aoa_to_sheet(data);\n  \n  ws['!cols'] = [\n    { wch: 18 }, { wch: 25 }, { wch: 12 }, { wch: 15 }, { wch: 15 }, { wch: 15 }, { wch: 30 }\n  ];\n  \n  const wb = XLSX.utils.book_new();\n  XLSX.utils.book_append_sheet(wb, ws, 'المصاريف');\n  \n  // إضافة ورقة فئات المصاريف\n  const categoriesData = [\n    ['فئات المصاريف المتاحة'],\n    [''],\n    ['مصاريف إدارية'],\n    ['مصاريف تشغيلية'],\n    ['مصاريف تسويق'],\n    ['مصاريف صيانة'],\n    ['مصاريف مواصلات'],\n    ['مصاريف اتصالات'],\n    ['مصاريف كهرباء'],\n    ['مصاريف إيجار'],\n    ['مصاريف أخرى'],\n    [''],\n    ['طرق الدفع المتاحة:'],\n    [''],\n    ['نقدي'],\n    ['شيك'],\n    ['تحويل بنكي'],\n    ['بطاقة ائتمان']\n  ];\n  \n  const categoriesWs = XLSX.utils.aoa_to_sheet(categoriesData);\n  categoriesWs['!cols'] = [{ wch: 30 }];\n  \n  XLSX.utils.book_append_sheet(wb, categoriesWs, 'الفئات المتاحة');\n  \n  return wb;\n};\n\nexport const downloadTemplate = (type) => {\n  let workbook;\n  let filename;\n  \n  switch (type) {\n    case 'customers':\n      workbook = generateCustomersTemplate();\n      filename = 'قالب_العملاء.xlsx';\n      break;\n    case 'suppliers':\n      workbook = generateSuppliersTemplate();\n      filename = 'قالب_الموردين.xlsx';\n      break;\n    case 'products':\n      workbook = generateProductsTemplate();\n      filename = 'قالب_المنتجات.xlsx';\n      break;\n    case 'expenses':\n      workbook = generateExpensesTemplate();\n      filename = 'قالب_المصاريف.xlsx';\n      break;\n    default:\n      console.error('نوع القالب غير مدعوم:', type);\n      return;\n  }\n  \n  XLSX.writeFile(workbook, filename);\n};\n\n// دالة لإنشاء قالب شامل يحتوي على جميع الأقسام\nexport const generateCompleteTemplate = () => {\n  const wb = XLSX.utils.book_new();\n  \n  // إضافة ورقة العملاء\n  const customersWb = generateCustomersTemplate();\n  const customersWs = customersWb.Sheets['العملاء'];\n  XLSX.utils.book_append_sheet(wb, customersWs, 'العملاء');\n  \n  // إضافة ورقة الموردين\n  const suppliersWb = generateSuppliersTemplate();\n  const suppliersWs = suppliersWb.Sheets['الموردين'];\n  XLSX.utils.book_append_sheet(wb, suppliersWs, 'الموردين');\n  \n  // إضافة ورقة المنتجات\n  const productsWb = generateProductsTemplate();\n  const productsWs = productsWb.Sheets['المنتجات'];\n  XLSX.utils.book_append_sheet(wb, productsWs, 'المنتجات');\n  \n  // إضافة ورقة المصاريف\n  const expensesWb = generateExpensesTemplate();\n  const expensesWs = expensesWb.Sheets['المصاريف'];\n  XLSX.utils.book_append_sheet(wb, expensesWs, 'المصاريف');\n  \n  // إضافة ورقة التعليمات العامة\n  const generalInstructionsData = [\n    ['دليل استخدام قوالب النظام المحاسبي'],\n    [''],\n    ['مرحباً بك في النظام المحاسبي المتكامل'],\n    [''],\n    ['هذا الملف يحتوي على قوالب لاستيراد البيانات:'],\n    [''],\n    ['1. ورقة العملاء - لاستيراد بيانات العملاء'],\n    ['2. ورقة الموردين - لاستيراد بيانات الموردين'],\n    ['3. ورقة المنتجات - لاستيراد بيانات المنتجات'],\n    ['4. ورقة المصاريف - لاستيراد بيانات المصاريف'],\n    [''],\n    ['تعليمات عامة:'],\n    [''],\n    ['• لا تغير أسماء الأعمدة في الصف الأول'],\n    ['• املأ البيانات بدءاً من الصف الثاني'],\n    ['• احذف البيانات التجريبية قبل إدخال بياناتك'],\n    ['• تأكد من صحة التواريخ (DD/MM/YYYY)'],\n    ['• تأكد من صحة الأرقام (بدون فواصل أو رموز)'],\n    ['• احفظ الملف بصيغة Excel (.xlsx)'],\n    [''],\n    ['للدعم الفني: <EMAIL>'],\n    ['الموقع الإلكتروني: www.accounting-system.com']\n  ];\n  \n  const generalInstructionsWs = XLSX.utils.aoa_to_sheet(generalInstructionsData);\n  generalInstructionsWs['!cols'] = [{ wch: 60 }];\n  \n  // إدراج ورقة التعليمات في البداية\n  const sheets = wb.SheetNames;\n  wb.SheetNames = ['التعليمات العامة', ...sheets];\n  wb.Sheets['التعليمات العامة'] = generalInstructionsWs;\n  \n  return wb;\n};\n\nexport const downloadCompleteTemplate = () => {\n  const workbook = generateCompleteTemplate();\n  XLSX.writeFile(workbook, 'قوالب_النظام_المحاسبي_الشاملة.xlsx');\n};\n"], "mappings": "AAAA,OAAO,KAAKA,IAAI,MAAM,MAAM;AAE5B,OAAO,MAAMC,yBAAyB,GAAGA,CAAA,KAAM;EAC7C,MAAMC,OAAO,GAAG,CACd,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACnB,SAAS,EACT,eAAe,CAChB;EAED,MAAMC,UAAU,GAAG,CACjB,CAAC,eAAe,EAAE,aAAa,EAAE,mBAAmB,EAAE,cAAc,EAAE,WAAW,CAAC,EAClF,CAAC,YAAY,EAAE,aAAa,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,WAAW,CAAC,EAClF,CAAC,UAAU,EAAE,aAAa,EAAE,qBAAqB,EAAE,aAAa,EAAE,WAAW,CAAC,CAC/E;EAED,MAAMC,IAAI,GAAG,CAACF,OAAO,EAAE,GAAGC,UAAU,CAAC;EAErC,MAAME,EAAE,GAAGL,IAAI,CAACM,KAAK,CAACC,YAAY,CAACH,IAAI,CAAC;;EAExC;EACA,MAAMI,SAAS,GAAG,CAChB;IAAEC,GAAG,EAAE;EAAG,CAAC;EAAE;EACb;IAAEA,GAAG,EAAE;EAAG,CAAC;EAAE;EACb;IAAEA,GAAG,EAAE;EAAG,CAAC;EAAE;EACb;IAAEA,GAAG,EAAE;EAAG,CAAC;EAAE;EACb;IAAEA,GAAG,EAAE;EAAG,CAAC,CAAE;EAAA,CACd;EACDJ,EAAE,CAAC,OAAO,CAAC,GAAGG,SAAS;;EAEvB;EACA,MAAME,WAAW,GAAG;IAClBC,IAAI,EAAE;MAAEC,IAAI,EAAE,IAAI;MAAEC,KAAK,EAAE;QAAEC,GAAG,EAAE;MAAS;IAAE,CAAC;IAC9CC,IAAI,EAAE;MAAEC,OAAO,EAAE;QAAEF,GAAG,EAAE;MAAS;IAAE,CAAC;IACpCG,SAAS,EAAE;MAAEC,UAAU,EAAE;IAAS;EACpC,CAAC;;EAED;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,OAAO,CAACkB,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,MAAME,OAAO,GAAGrB,IAAI,CAACM,KAAK,CAACgB,WAAW,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAEL;IAAE,CAAC,CAAC;IACtD,IAAI,CAACd,EAAE,CAACgB,OAAO,CAAC,EAAEhB,EAAE,CAACgB,OAAO,CAAC,GAAG,CAAC,CAAC;IAClChB,EAAE,CAACgB,OAAO,CAAC,CAACI,CAAC,GAAGf,WAAW;EAC7B;EAEA,MAAMgB,EAAE,GAAG1B,IAAI,CAACM,KAAK,CAACqB,QAAQ,CAAC,CAAC;EAChC3B,IAAI,CAACM,KAAK,CAACsB,iBAAiB,CAACF,EAAE,EAAErB,EAAE,EAAE,SAAS,CAAC;;EAE/C;EACA,MAAMwB,gBAAgB,GAAG,CACvB,CAAC,yBAAyB,CAAC,EAC3B,CAAC,EAAE,CAAC,EACJ,CAAC,oCAAoC,CAAC,EACtC,CAAC,qBAAqB,CAAC,EACvB,CAAC,yBAAyB,CAAC,EAC3B,CAAC,mCAAmC,CAAC,EACrC,CAAC,gCAAgC,CAAC,EAClC,CAAC,EAAE,CAAC,EACJ,CAAC,UAAU,CAAC,EACZ,CAAC,6BAA6B,CAAC,EAC/B,CAAC,wCAAwC,CAAC,EAC1C,CAAC,2CAA2C,CAAC,CAC9C;EAED,MAAMC,cAAc,GAAG9B,IAAI,CAACM,KAAK,CAACC,YAAY,CAACsB,gBAAgB,CAAC;EAChEC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC;IAAErB,GAAG,EAAE;EAAG,CAAC,CAAC;EAEvCT,IAAI,CAACM,KAAK,CAACsB,iBAAiB,CAACF,EAAE,EAAEI,cAAc,EAAE,WAAW,CAAC;EAE7D,OAAOJ,EAAE;AACX,CAAC;AAED,OAAO,MAAMK,yBAAyB,GAAGA,CAAA,KAAM;EAC7C,MAAM7B,OAAO,GAAG,CACd,YAAY,EACZ,YAAY,EACZ,mBAAmB,EACnB,SAAS,EACT,eAAe,CAChB;EAED,MAAMC,UAAU,GAAG,CACjB,CAAC,yBAAyB,EAAE,YAAY,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,WAAW,CAAC,EACpG,CAAC,wBAAwB,EAAE,YAAY,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,WAAW,CAAC,EAC9F,CAAC,mBAAmB,EAAE,YAAY,EAAE,uBAAuB,EAAE,oBAAoB,EAAE,WAAW,CAAC,CAChG;EAED,MAAMC,IAAI,GAAG,CAACF,OAAO,EAAE,GAAGC,UAAU,CAAC;EACrC,MAAME,EAAE,GAAGL,IAAI,CAACM,KAAK,CAACC,YAAY,CAACH,IAAI,CAAC;EAExCC,EAAE,CAAC,OAAO,CAAC,GAAG,CACZ;IAAEI,GAAG,EAAE;EAAG,CAAC,EAAE;IAAEA,GAAG,EAAE;EAAG,CAAC,EAAE;IAAEA,GAAG,EAAE;EAAG,CAAC,EAAE;IAAEA,GAAG,EAAE;EAAG,CAAC,EAAE;IAAEA,GAAG,EAAE;EAAG,CAAC,CAChE;EAED,MAAMiB,EAAE,GAAG1B,IAAI,CAACM,KAAK,CAACqB,QAAQ,CAAC,CAAC;EAChC3B,IAAI,CAACM,KAAK,CAACsB,iBAAiB,CAACF,EAAE,EAAErB,EAAE,EAAE,UAAU,CAAC;EAEhD,OAAOqB,EAAE;AACX,CAAC;AAED,OAAO,MAAMM,wBAAwB,GAAGA,CAAA,KAAM;EAC5C,MAAM9B,OAAO,GAAG,CACd,YAAY,EACZ,OAAO,EACP,QAAQ,EACR,WAAW,EACX,aAAa,EACb,gBAAgB,CACjB;EAED,MAAMC,UAAU,GAAG,CACjB,CAAC,aAAa,EAAE,yBAAyB,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,EAC1E,CAAC,UAAU,EAAE,mBAAmB,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,EAC9D,CAAC,QAAQ,EAAE,mBAAmB,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,EAC1D,CAAC,aAAa,EAAE,kBAAkB,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAC/D;EAED,MAAMC,IAAI,GAAG,CAACF,OAAO,EAAE,GAAGC,UAAU,CAAC;EACrC,MAAME,EAAE,GAAGL,IAAI,CAACM,KAAK,CAACC,YAAY,CAACH,IAAI,CAAC;EAExCC,EAAE,CAAC,OAAO,CAAC,GAAG,CACZ;IAAEI,GAAG,EAAE;EAAG,CAAC,EAAE;IAAEA,GAAG,EAAE;EAAG,CAAC,EAAE;IAAEA,GAAG,EAAE;EAAG,CAAC,EAAE;IAAEA,GAAG,EAAE;EAAG,CAAC,EAAE;IAAEA,GAAG,EAAE;EAAG,CAAC,EAAE;IAAEA,GAAG,EAAE;EAAG,CAAC,CAC7E;EAED,MAAMiB,EAAE,GAAG1B,IAAI,CAACM,KAAK,CAACqB,QAAQ,CAAC,CAAC;EAChC3B,IAAI,CAACM,KAAK,CAACsB,iBAAiB,CAACF,EAAE,EAAErB,EAAE,EAAE,UAAU,CAAC;;EAEhD;EACA,MAAMwB,gBAAgB,GAAG,CACvB,CAAC,0BAA0B,CAAC,EAC5B,CAAC,EAAE,CAAC,EACJ,CAAC,qCAAqC,CAAC,EACvC,CAAC,qBAAqB,CAAC,EACvB,CAAC,8BAA8B,CAAC,EAChC,CAAC,gCAAgC,CAAC,EAClC,CAAC,EAAE,CAAC,EACJ,CAAC,oBAAoB,CAAC,EACtB,CAAC,4CAA4C,CAAC,EAC9C,CAAC,EAAE,CAAC,EACJ,CAAC,UAAU,CAAC,EACZ,CAAC,uBAAuB,CAAC,EACzB,CAAC,4CAA4C,CAAC,EAC9C,CAAC,sCAAsC,CAAC,CACzC;EAED,MAAMC,cAAc,GAAG9B,IAAI,CAACM,KAAK,CAACC,YAAY,CAACsB,gBAAgB,CAAC;EAChEC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC;IAAErB,GAAG,EAAE;EAAG,CAAC,CAAC;EAEvCT,IAAI,CAACM,KAAK,CAACsB,iBAAiB,CAACF,EAAE,EAAEI,cAAc,EAAE,WAAW,CAAC;EAE7D,OAAOJ,EAAE;AACX,CAAC;AAED,OAAO,MAAMO,wBAAwB,GAAGA,CAAA,KAAM;EAC5C,MAAM/B,OAAO,GAAG,CACd,aAAa,EACb,OAAO,EACP,QAAQ,EACR,eAAe,EACf,aAAa,EACb,aAAa,EACb,SAAS,CACV;EAED,MAAMC,UAAU,GAAG,CACjB,CAAC,eAAe,EAAE,eAAe,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,kBAAkB,CAAC,EAC3F,CAAC,gBAAgB,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,EACrF,CAAC,cAAc,EAAE,gBAAgB,EAAE,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,EAAE,cAAc,CAAC,EAC7F,CAAC,gBAAgB,EAAE,eAAe,EAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,cAAc,CAAC,CACzF;EAED,MAAMC,IAAI,GAAG,CAACF,OAAO,EAAE,GAAGC,UAAU,CAAC;EACrC,MAAME,EAAE,GAAGL,IAAI,CAACM,KAAK,CAACC,YAAY,CAACH,IAAI,CAAC;EAExCC,EAAE,CAAC,OAAO,CAAC,GAAG,CACZ;IAAEI,GAAG,EAAE;EAAG,CAAC,EAAE;IAAEA,GAAG,EAAE;EAAG,CAAC,EAAE;IAAEA,GAAG,EAAE;EAAG,CAAC,EAAE;IAAEA,GAAG,EAAE;EAAG,CAAC,EAAE;IAAEA,GAAG,EAAE;EAAG,CAAC,EAAE;IAAEA,GAAG,EAAE;EAAG,CAAC,EAAE;IAAEA,GAAG,EAAE;EAAG,CAAC,CAC1F;EAED,MAAMiB,EAAE,GAAG1B,IAAI,CAACM,KAAK,CAACqB,QAAQ,CAAC,CAAC;EAChC3B,IAAI,CAACM,KAAK,CAACsB,iBAAiB,CAACF,EAAE,EAAErB,EAAE,EAAE,UAAU,CAAC;;EAEhD;EACA,MAAM6B,cAAc,GAAG,CACrB,CAAC,uBAAuB,CAAC,EACzB,CAAC,EAAE,CAAC,EACJ,CAAC,eAAe,CAAC,EACjB,CAAC,gBAAgB,CAAC,EAClB,CAAC,cAAc,CAAC,EAChB,CAAC,cAAc,CAAC,EAChB,CAAC,gBAAgB,CAAC,EAClB,CAAC,gBAAgB,CAAC,EAClB,CAAC,eAAe,CAAC,EACjB,CAAC,cAAc,CAAC,EAChB,CAAC,aAAa,CAAC,EACf,CAAC,EAAE,CAAC,EACJ,CAAC,oBAAoB,CAAC,EACtB,CAAC,EAAE,CAAC,EACJ,CAAC,MAAM,CAAC,EACR,CAAC,KAAK,CAAC,EACP,CAAC,YAAY,CAAC,EACd,CAAC,cAAc,CAAC,CACjB;EAED,MAAMC,YAAY,GAAGnC,IAAI,CAACM,KAAK,CAACC,YAAY,CAAC2B,cAAc,CAAC;EAC5DC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC;IAAE1B,GAAG,EAAE;EAAG,CAAC,CAAC;EAErCT,IAAI,CAACM,KAAK,CAACsB,iBAAiB,CAACF,EAAE,EAAES,YAAY,EAAE,gBAAgB,CAAC;EAEhE,OAAOT,EAAE;AACX,CAAC;AAED,OAAO,MAAMU,gBAAgB,GAAIC,IAAI,IAAK;EACxC,IAAIC,QAAQ;EACZ,IAAIC,QAAQ;EAEZ,QAAQF,IAAI;IACV,KAAK,WAAW;MACdC,QAAQ,GAAGrC,yBAAyB,CAAC,CAAC;MACtCsC,QAAQ,GAAG,mBAAmB;MAC9B;IACF,KAAK,WAAW;MACdD,QAAQ,GAAGP,yBAAyB,CAAC,CAAC;MACtCQ,QAAQ,GAAG,oBAAoB;MAC/B;IACF,KAAK,UAAU;MACbD,QAAQ,GAAGN,wBAAwB,CAAC,CAAC;MACrCO,QAAQ,GAAG,oBAAoB;MAC/B;IACF,KAAK,UAAU;MACbD,QAAQ,GAAGL,wBAAwB,CAAC,CAAC;MACrCM,QAAQ,GAAG,oBAAoB;MAC/B;IACF;MACEC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEJ,IAAI,CAAC;MAC5C;EACJ;EAEArC,IAAI,CAAC0C,SAAS,CAACJ,QAAQ,EAAEC,QAAQ,CAAC;AACpC,CAAC;;AAED;AACA,OAAO,MAAMI,wBAAwB,GAAGA,CAAA,KAAM;EAC5C,MAAMjB,EAAE,GAAG1B,IAAI,CAACM,KAAK,CAACqB,QAAQ,CAAC,CAAC;;EAEhC;EACA,MAAMiB,WAAW,GAAG3C,yBAAyB,CAAC,CAAC;EAC/C,MAAM4C,WAAW,GAAGD,WAAW,CAACE,MAAM,CAAC,SAAS,CAAC;EACjD9C,IAAI,CAACM,KAAK,CAACsB,iBAAiB,CAACF,EAAE,EAAEmB,WAAW,EAAE,SAAS,CAAC;;EAExD;EACA,MAAME,WAAW,GAAGhB,yBAAyB,CAAC,CAAC;EAC/C,MAAMiB,WAAW,GAAGD,WAAW,CAACD,MAAM,CAAC,UAAU,CAAC;EAClD9C,IAAI,CAACM,KAAK,CAACsB,iBAAiB,CAACF,EAAE,EAAEsB,WAAW,EAAE,UAAU,CAAC;;EAEzD;EACA,MAAMC,UAAU,GAAGjB,wBAAwB,CAAC,CAAC;EAC7C,MAAMkB,UAAU,GAAGD,UAAU,CAACH,MAAM,CAAC,UAAU,CAAC;EAChD9C,IAAI,CAACM,KAAK,CAACsB,iBAAiB,CAACF,EAAE,EAAEwB,UAAU,EAAE,UAAU,CAAC;;EAExD;EACA,MAAMC,UAAU,GAAGlB,wBAAwB,CAAC,CAAC;EAC7C,MAAMmB,UAAU,GAAGD,UAAU,CAACL,MAAM,CAAC,UAAU,CAAC;EAChD9C,IAAI,CAACM,KAAK,CAACsB,iBAAiB,CAACF,EAAE,EAAE0B,UAAU,EAAE,UAAU,CAAC;;EAExD;EACA,MAAMC,uBAAuB,GAAG,CAC9B,CAAC,oCAAoC,CAAC,EACtC,CAAC,EAAE,CAAC,EACJ,CAAC,uCAAuC,CAAC,EACzC,CAAC,EAAE,CAAC,EACJ,CAAC,8CAA8C,CAAC,EAChD,CAAC,EAAE,CAAC,EACJ,CAAC,2CAA2C,CAAC,EAC7C,CAAC,6CAA6C,CAAC,EAC/C,CAAC,6CAA6C,CAAC,EAC/C,CAAC,6CAA6C,CAAC,EAC/C,CAAC,EAAE,CAAC,EACJ,CAAC,eAAe,CAAC,EACjB,CAAC,EAAE,CAAC,EACJ,CAAC,uCAAuC,CAAC,EACzC,CAAC,sCAAsC,CAAC,EACxC,CAAC,6CAA6C,CAAC,EAC/C,CAAC,qCAAqC,CAAC,EACvC,CAAC,4CAA4C,CAAC,EAC9C,CAAC,kCAAkC,CAAC,EACpC,CAAC,EAAE,CAAC,EACJ,CAAC,4CAA4C,CAAC,EAC9C,CAAC,8CAA8C,CAAC,CACjD;EAED,MAAMC,qBAAqB,GAAGtD,IAAI,CAACM,KAAK,CAACC,YAAY,CAAC8C,uBAAuB,CAAC;EAC9EC,qBAAqB,CAAC,OAAO,CAAC,GAAG,CAAC;IAAE7C,GAAG,EAAE;EAAG,CAAC,CAAC;;EAE9C;EACA,MAAM8C,MAAM,GAAG7B,EAAE,CAAC8B,UAAU;EAC5B9B,EAAE,CAAC8B,UAAU,GAAG,CAAC,kBAAkB,EAAE,GAAGD,MAAM,CAAC;EAC/C7B,EAAE,CAACoB,MAAM,CAAC,kBAAkB,CAAC,GAAGQ,qBAAqB;EAErD,OAAO5B,EAAE;AACX,CAAC;AAED,OAAO,MAAM+B,wBAAwB,GAAGA,CAAA,KAAM;EAC5C,MAAMnB,QAAQ,GAAGK,wBAAwB,CAAC,CAAC;EAC3C3C,IAAI,CAAC0C,SAAS,CAACJ,QAAQ,EAAE,oCAAoC,CAAC;AAChE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}