{"version": "2.0.0", "tasks": [{"label": "تثبيت المكتبات - الواجهة الأمامية", "type": "shell", "command": "npm", "args": ["install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "تثبيت المكتبات - الخادم", "type": "shell", "command": "npm", "args": ["install"], "options": {"cwd": "${workspaceFolder}/server"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "تشغيل الخادم", "type": "shell", "command": "node", "args": ["server.js"], "options": {"cwd": "${workspaceFolder}/server"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "dedicated", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "isBackground": true}, {"label": "تشغيل الواجهة الأمامية", "type": "shell", "command": "npm", "args": ["start"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "dedicated", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "isBackground": true}, {"label": "بناء المشروع للإنتاج", "type": "shell", "command": "npm", "args": ["run", "build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "تشغيل النظام كاملاً", "dependsOrder": "parallel", "dependsOn": ["تشغيل الخادم", "تشغيل الواجهة الأمامية"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "تثبيت جميع المكتبات", "dependsOrder": "sequence", "dependsOn": ["تثبيت المكتبات - الواجهة الأمامية", "تثبيت المكتبات - الخادم"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}]}