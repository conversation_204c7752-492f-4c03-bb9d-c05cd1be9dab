import React from 'react';
import { downloadTemplate, downloadCompleteTemplate } from './TemplateGenerator';

const DownloadCenter = () => {
  const templates = [
    {
      id: 'customers',
      title: 'قالب العملاء',
      description: 'قالب Excel لاستيراد بيانات العملاء مع أمثلة وتعليمات',
      icon: '👥',
      color: '#4CAF50'
    },
    {
      id: 'suppliers',
      title: 'قالب الموردين',
      description: 'قالب Excel لاستيراد بيانات الموردين مع أمثلة وتعليمات',
      icon: '🏭',
      color: '#2196F3'
    },
    {
      id: 'products',
      title: 'قالب المنتجات',
      description: 'قالب Excel لاستيراد بيانات المنتجات مع أمثلة وتعليمات',
      icon: '📦',
      color: '#FF9800'
    },
    {
      id: 'expenses',
      title: 'قالب المصاريف',
      description: 'قالب Excel لاستيراد بيانات المصاريف مع أمثلة وتعليمات',
      icon: '💸',
      color: '#F44336'
    }
  ];

  const handleDownload = (templateId) => {
    downloadTemplate(templateId);
  };

  const handleDownloadComplete = () => {
    downloadCompleteTemplate();
  };

  return (
    <div className="container">
      <h1 className="page-title">📥 مركز التحميل</h1>
      
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">قوالب الاستيراد</h3>
        </div>
        <div className="card-body">
          <p style={{ color: '#666', marginBottom: '2rem' }}>
            حمل القوالب الجاهزة لاستيراد البيانات إلى النظام المحاسبي. كل قالب يحتوي على أمثلة وتعليمات مفصلة.
          </p>

          {/* القالب الشامل */}
          <div className="card" style={{ 
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            marginBottom: '2rem'
          }}>
            <div className="card-body">
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <h4 style={{ color: 'white', marginBottom: '0.5rem' }}>
                    🎯 القالب الشامل
                  </h4>
                  <p style={{ color: 'rgba(255,255,255,0.9)', marginBottom: '0' }}>
                    قالب واحد يحتوي على جميع الأقسام (العملاء، الموردين، المنتجات، المصاريف)
                  </p>
                </div>
                <button 
                  onClick={handleDownloadComplete}
                  className="btn"
                  style={{ 
                    backgroundColor: 'white', 
                    color: '#667eea',
                    fontWeight: 'bold'
                  }}
                >
                  📥 تحميل القالب الشامل
                </button>
              </div>
            </div>
          </div>

          {/* القوالب الفردية */}
          <div className="row">
            {templates.map(template => (
              <div key={template.id} className="col-md-6">
                <div className="card card-hover" style={{ marginBottom: '1.5rem' }}>
                  <div className="card-body">
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
                      <div style={{
                        fontSize: '2rem',
                        marginLeft: '1rem',
                        padding: '0.5rem',
                        borderRadius: '50%',
                        backgroundColor: template.color + '20',
                        color: template.color
                      }}>
                        {template.icon}
                      </div>
                      <div>
                        <h5 style={{ marginBottom: '0.25rem', color: template.color }}>
                          {template.title}
                        </h5>
                        <p style={{ color: '#666', marginBottom: '0', fontSize: '0.9rem' }}>
                          {template.description}
                        </p>
                      </div>
                    </div>
                    
                    <button 
                      onClick={() => handleDownload(template.id)}
                      className="btn btn-primary"
                      style={{ width: '100%' }}
                    >
                      📥 تحميل القالب
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* تعليمات الاستخدام */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">📋 تعليمات الاستخدام</h3>
        </div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-6">
              <h5>🔄 خطوات الاستيراد:</h5>
              <ol>
                <li>حمل القالب المناسب من الأعلى</li>
                <li>افتح الملف في Excel أو Google Sheets</li>
                <li>احذف البيانات التجريبية</li>
                <li>أدخل بياناتك الحقيقية</li>
                <li>احفظ الملف بصيغة Excel (.xlsx)</li>
                <li>ارفع الملف في القسم المناسب</li>
              </ol>
            </div>
            <div className="col-md-6">
              <h5>⚠️ نصائح مهمة:</h5>
              <ul>
                <li>لا تغير أسماء الأعمدة في الصف الأول</li>
                <li>تأكد من صحة التواريخ (DD/MM/YYYY)</li>
                <li>تأكد من صحة الأرقام (بدون فواصل)</li>
                <li>املأ الحقول المطلوبة فقط</li>
                <li>راجع البيانات قبل الاستيراد</li>
                <li>احتفظ بنسخة احتياطية من ملفك</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* أمثلة البيانات */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">💡 أمثلة البيانات</h3>
        </div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-3">
              <div className="card">
                <div className="card-header">
                  <h6>👥 مثال عميل</h6>
                </div>
                <div className="card-body">
                  <small>
                    <strong>الاسم:</strong> أحمد محمد علي<br />
                    <strong>الهاتف:</strong> 01234567890<br />
                    <strong>البريد:</strong> <EMAIL><br />
                    <strong>العنوان:</strong> القاهرة، مصر
                  </small>
                </div>
              </div>
            </div>
            
            <div className="col-md-3">
              <div className="card">
                <div className="card-header">
                  <h6>🏭 مثال مورد</h6>
                </div>
                <div className="card-body">
                  <small>
                    <strong>الاسم:</strong> شركة التوريدات<br />
                    <strong>الهاتف:</strong> 0223456789<br />
                    <strong>البريد:</strong> <EMAIL><br />
                    <strong>العنوان:</strong> القاهرة الجديدة
                  </small>
                </div>
              </div>
            </div>
            
            <div className="col-md-3">
              <div className="card">
                <div className="card-header">
                  <h6>📦 مثال منتج</h6>
                </div>
                <div className="card-body">
                  <small>
                    <strong>الاسم:</strong> لابتوب Dell<br />
                    <strong>الوحدة:</strong> قطعة<br />
                    <strong>السعر:</strong> 15000<br />
                    <strong>الكمية:</strong> 10
                  </small>
                </div>
              </div>
            </div>
            
            <div className="col-md-3">
              <div className="card">
                <div className="card-header">
                  <h6>💸 مثال مصروف</h6>
                </div>
                <div className="card-body">
                  <small>
                    <strong>الفئة:</strong> مصاريف إدارية<br />
                    <strong>الوصف:</strong> فاتورة كهرباء<br />
                    <strong>المبلغ:</strong> 500<br />
                    <strong>التاريخ:</strong> 15/01/2024
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* الدعم الفني */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">🆘 الدعم الفني</h3>
        </div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-6">
              <h5>📞 تواصل معنا:</h5>
              <p>
                إذا واجهت أي مشكلة في استخدام القوالب أو الاستيراد، لا تتردد في التواصل معنا:
              </p>
              <ul>
                <li>📧 البريد الإلكتروني: <EMAIL></li>
                <li>📱 الهاتف: +20 123 456 7890</li>
                <li>💬 الدردشة المباشرة: متاحة 24/7</li>
              </ul>
            </div>
            <div className="col-md-6">
              <h5>📚 مصادر إضافية:</h5>
              <ul>
                <li><a href="#" style={{ color: '#667eea' }}>دليل المستخدم الكامل</a></li>
                <li><a href="#" style={{ color: '#667eea' }}>فيديوهات تعليمية</a></li>
                <li><a href="#" style={{ color: '#667eea' }}>الأسئلة الشائعة</a></li>
                <li><a href="#" style={{ color: '#667eea' }}>منتدى المجتمع</a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DownloadCenter;
