{"ast": null, "code": "var _jsxFileName = \"F:\\\\\\u0627\\u0644\\u0642\\u064A\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0636\\u0627\\u0641\\u0629\\\\accounting-system\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const location = useLocation();\n  const isActive = path => {\n    return location.pathname === path ? 'nav-link active' : 'nav-link';\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navbar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-brand\",\n      children: \"\\uD83D\\uDCBC \\u0646\\u0638\\u0627\\u0645 \\u0627\\u0644\\u0645\\u062D\\u0627\\u0633\\u0628\\u0629 \\u0627\\u0644\\u0645\\u062A\\u0643\\u0627\\u0645\\u0644\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"navbar-nav\",\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: isActive('/'),\n          children: \"\\uD83C\\uDFE0 \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/customers\",\n          className: isActive('/customers'),\n          children: \"\\uD83D\\uDC65 \\u0627\\u0644\\u0639\\u0645\\u0644\\u0627\\u0621\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/suppliers\",\n          className: isActive('/suppliers'),\n          children: \"\\uD83C\\uDFED \\u0627\\u0644\\u0645\\u0648\\u0631\\u062F\\u064A\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/products\",\n          className: isActive('/products'),\n          children: \"\\uD83D\\uDCE6 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/sales-invoices\",\n          className: isActive('/sales-invoices'),\n          children: \"\\uD83D\\uDCC4 \\u0641\\u0648\\u0627\\u062A\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0628\\u064A\\u0639\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/purchase-invoices\",\n          className: isActive('/purchase-invoices'),\n          children: \"\\uD83D\\uDCCB \\u0641\\u0648\\u0627\\u062A\\u064A\\u0631 \\u0627\\u0644\\u0645\\u0634\\u062A\\u0631\\u064A\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/treasury\",\n          className: isActive('/treasury'),\n          children: \"\\uD83D\\uDCB0 \\u0627\\u0644\\u062E\\u0632\\u064A\\u0646\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/expenses\",\n          className: isActive('/expenses'),\n          children: \"\\uD83D\\uDCB8 \\u0627\\u0644\\u0645\\u0635\\u0627\\u0631\\u064A\\u0641\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/inventory\",\n          className: isActive('/inventory'),\n          children: \"\\uD83D\\uDCE6 \\u0627\\u0644\\u0645\\u062E\\u0632\\u0648\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/backup\",\n          className: isActive('/backup'),\n          children: \"\\uD83D\\uDCBE \\u0627\\u0644\\u0646\\u0633\\u062E \\u0627\\u0644\\u0627\\u062D\\u062A\\u064A\\u0627\\u0637\\u064A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/reports\",\n          className: isActive('/reports'),\n          children: \"\\uD83D\\uDCCA \\u0627\\u0644\\u062A\\u0642\\u0627\\u0631\\u064A\\u0631\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "location", "isActive", "path", "pathname", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["F:/القيمة المضافة/accounting-system/src/components/Navbar.js"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\n\nconst Navbar = () => {\n  const location = useLocation();\n\n  const isActive = (path) => {\n    return location.pathname === path ? 'nav-link active' : 'nav-link';\n  };\n\n  return (\n    <nav className=\"navbar\">\n      <div className=\"navbar-brand\">\n        💼 نظام المحاسبة المتكامل\n      </div>\n      <ul className=\"navbar-nav\">\n        <li>\n          <Link to=\"/\" className={isActive('/')}>\n            🏠 الرئيسية\n          </Link>\n        </li>\n        <li>\n          <Link to=\"/customers\" className={isActive('/customers')}>\n            👥 العملاء\n          </Link>\n        </li>\n        <li>\n          <Link to=\"/suppliers\" className={isActive('/suppliers')}>\n            🏭 الموردين\n          </Link>\n        </li>\n        <li>\n          <Link to=\"/products\" className={isActive('/products')}>\n            📦 المنتجات\n          </Link>\n        </li>\n        <li>\n          <Link to=\"/sales-invoices\" className={isActive('/sales-invoices')}>\n            📄 فواتير المبيعات\n          </Link>\n        </li>\n        <li>\n          <Link to=\"/purchase-invoices\" className={isActive('/purchase-invoices')}>\n            📋 فواتير المشتريات\n          </Link>\n        </li>\n        <li>\n          <Link to=\"/treasury\" className={isActive('/treasury')}>\n            💰 الخزينة\n          </Link>\n        </li>\n        <li>\n          <Link to=\"/expenses\" className={isActive('/expenses')}>\n            💸 المصاريف\n          </Link>\n        </li>\n        <li>\n          <Link to=\"/inventory\" className={isActive('/inventory')}>\n            📦 المخزون\n          </Link>\n        </li>\n        <li>\n          <Link to=\"/backup\" className={isActive('/backup')}>\n            💾 النسخ الاحتياطي\n          </Link>\n        </li>\n        <li>\n          <Link to=\"/reports\" className={isActive('/reports')}>\n            📊 التقارير\n          </Link>\n        </li>\n      </ul>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAE9B,MAAMM,QAAQ,GAAIC,IAAI,IAAK;IACzB,OAAOF,QAAQ,CAACG,QAAQ,KAAKD,IAAI,GAAG,iBAAiB,GAAG,UAAU;EACpE,CAAC;EAED,oBACEL,OAAA;IAAKO,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACrBR,OAAA;MAAKO,SAAS,EAAC,cAAc;MAAAC,QAAA,EAAC;IAE9B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNZ,OAAA;MAAIO,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACxBR,OAAA;QAAAQ,QAAA,eACER,OAAA,CAACH,IAAI;UAACgB,EAAE,EAAC,GAAG;UAACN,SAAS,EAAEH,QAAQ,CAAC,GAAG,CAAE;UAAAI,QAAA,EAAC;QAEvC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACLZ,OAAA;QAAAQ,QAAA,eACER,OAAA,CAACH,IAAI;UAACgB,EAAE,EAAC,YAAY;UAACN,SAAS,EAAEH,QAAQ,CAAC,YAAY,CAAE;UAAAI,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACLZ,OAAA;QAAAQ,QAAA,eACER,OAAA,CAACH,IAAI;UAACgB,EAAE,EAAC,YAAY;UAACN,SAAS,EAAEH,QAAQ,CAAC,YAAY,CAAE;UAAAI,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACLZ,OAAA;QAAAQ,QAAA,eACER,OAAA,CAACH,IAAI;UAACgB,EAAE,EAAC,WAAW;UAACN,SAAS,EAAEH,QAAQ,CAAC,WAAW,CAAE;UAAAI,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACLZ,OAAA;QAAAQ,QAAA,eACER,OAAA,CAACH,IAAI;UAACgB,EAAE,EAAC,iBAAiB;UAACN,SAAS,EAAEH,QAAQ,CAAC,iBAAiB,CAAE;UAAAI,QAAA,EAAC;QAEnE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACLZ,OAAA;QAAAQ,QAAA,eACER,OAAA,CAACH,IAAI;UAACgB,EAAE,EAAC,oBAAoB;UAACN,SAAS,EAAEH,QAAQ,CAAC,oBAAoB,CAAE;UAAAI,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACLZ,OAAA;QAAAQ,QAAA,eACER,OAAA,CAACH,IAAI;UAACgB,EAAE,EAAC,WAAW;UAACN,SAAS,EAAEH,QAAQ,CAAC,WAAW,CAAE;UAAAI,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACLZ,OAAA;QAAAQ,QAAA,eACER,OAAA,CAACH,IAAI;UAACgB,EAAE,EAAC,WAAW;UAACN,SAAS,EAAEH,QAAQ,CAAC,WAAW,CAAE;UAAAI,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACLZ,OAAA;QAAAQ,QAAA,eACER,OAAA,CAACH,IAAI;UAACgB,EAAE,EAAC,YAAY;UAACN,SAAS,EAAEH,QAAQ,CAAC,YAAY,CAAE;UAAAI,QAAA,EAAC;QAEzD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACLZ,OAAA;QAAAQ,QAAA,eACER,OAAA,CAACH,IAAI;UAACgB,EAAE,EAAC,SAAS;UAACN,SAAS,EAAEH,QAAQ,CAAC,SAAS,CAAE;UAAAI,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACLZ,OAAA;QAAAQ,QAAA,eACER,OAAA,CAACH,IAAI;UAACgB,EAAE,EAAC,UAAU;UAACN,SAAS,EAAEH,QAAQ,CAAC,UAAU,CAAE;UAAAI,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEV,CAAC;AAACV,EAAA,CAvEID,MAAM;EAAA,QACOH,WAAW;AAAA;AAAAgB,EAAA,GADxBb,MAAM;AAyEZ,eAAeA,MAAM;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}